package com.zqn.modeldata2.service.impl;

//import com.baomidou.dynamic.datasource.annotation.DS;

import com.zqn.modeldata2.entity.Login;
import com.zqn.modeldata2.mapper.LoginMapper;
import com.zqn.modeldata2.service.LoginService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class LoginServiceImpl implements LoginService {
    @Autowired
    private LoginMapper loginMapper;

    @Override
//    @DS("master")
    public Login login(String userId, String password) {
        Login user = loginMapper.login(userId, password);
        if (user != null && user.getUser_no() != null) {
            loginMapper.updateLoginTime(user.getUser_no());
        }
        return user;
    }
}
