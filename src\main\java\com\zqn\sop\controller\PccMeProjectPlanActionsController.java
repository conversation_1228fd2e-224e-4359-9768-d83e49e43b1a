package com.zqn.sop.controller;

import com.alibaba.fastjson.JSONObject;
import com.zqn.modeldata2.common.R;
import com.zqn.sop.entity.PccMeProjectPlanActions;
import com.zqn.sop.service.PccMeProjectPlanActionsService;
import com.zqn.sop.vo.PccMeProjectPlanDtVo;
import com.zqn.sop.vo.PccMeProjectPlanHdVo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: SOP动作类
 * @date 2024/5/8 10:02
 */
@RestController
@RequestMapping("/pccmeprjplanaction")
@Validated
public class PccMeProjectPlanActionsController {

    @Resource
    private PccMeProjectPlanActionsService pccMeProjectPlanActionsService;

    @GetMapping("/queryContByAction")
    public R<PccMeProjectPlanDtVo> queryContByAction(@RequestParam(value = "ids") String ids) {
        PccMeProjectPlanDtVo result = pccMeProjectPlanActionsService.queryContByAction(ids);
        return R.success(result);
    }

    @PostMapping("/save")
    public R<Integer> save(@RequestBody PccMeProjectPlanHdVo pccMeProjectPlanHdVo) {
        Integer result = pccMeProjectPlanActionsService.save(pccMeProjectPlanHdVo);
        return R.success(result);
    }

    /**
     * 通过GET请求查询标签信息。
     * <p>
     * 本方法提供了一个接口，用于查询项目计划动作的标签。它不接受任何参数，
     * 通过调用pccMeProjectPlanActionsService的queryTag方法来获取标签列表，
     * 并将这些标签封装在一个成功响应对象中返回。
     *
     * @return R<List < PccMeProjectPlanActions>> - 包含查询到的标签列表的成功响应对象。
     */
    @GetMapping("/queryTag")
    public R<List<JSONObject>> queryTag(@RequestParam(value = "type") String type) {
        // 调用服务层方法查询标签信息
        List<JSONObject> result = pccMeProjectPlanActionsService.queryTag(type);
        // 返回包含查询结果的成功响应
        return R.success(result);
    }
}
