package com.zqn.modeldata2.service;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.entity.CutProGress;

import java.util.Date;
import java.util.List;

public interface CutProGressService {


    R<PageInfo<CutProGress>> query(int pageNo, int pageSize, Date startTime, Date endTime,
                                   Date cinStartTime, Date cinEndTime,
                                   String brand, String devType, Integer externalStatus);

    List<JSONObject> queryAllDevType();

    Integer update(CutProGress cutProGress) throws Exception;
}