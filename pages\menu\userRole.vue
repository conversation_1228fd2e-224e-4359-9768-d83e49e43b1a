<template>
	<view class="container">
		<view  style="height:calc(100vh - var(--window-bottom) - var(--status-bar-height));" >
			
				<view  class="right-top-top">
						<uni-icons class="back" @click="back" type="back" size="36" style="margin-left:1vw;margin-top:10rpx;"></uni-icons>
				</view>
			<uni-row>
				<uni-col :span="11">
					<view id="userTable" class="user-selection-panel">
						<view class="search-container">
							<uni-easyinput
								class="uni-input search-input"
								id="inputUserNo"
								@input="getUser"
								v-model="inputUserNo"
								focus
								placeholder="🔍 输入用户编码搜索..."
								clearable
							/>
						</view>
						<uni-section title="选中用户" subTitle="下滑浏览并选中用户" type="line">
							<scroll-view scroll-y="true" class="scroll-Y selectUser">
								<view class="uni-px-5 uni-pb-5 user-list-container">
									<uni-data-checkbox
										v-if="users.length > 0"
										wrap
										multiple
										max="1"
										@change="change"
										v-model="userNo"
										:localdata="users"
										class="user-checkbox-group"
									></uni-data-checkbox>
									<view v-else-if="inputUserNo.length > 3 && users.length === 0" class="user-list-empty">
										<text>未找到匹配的用户</text>
										<text style="font-size: 24rpx; margin-top: 10rpx; opacity: 0.7;">请尝试输入其他关键词</text>
									</view>
									<view v-else class="user-list-empty">
										<text>请输入用户编码进行搜索</text>
										<text style="font-size: 24rpx; margin-top: 10rpx; opacity: 0.7;">至少输入4个字符</text>
									</view>
								</view>
							</scroll-view>
						</uni-section>
					</view>
				</uni-col>
				<uni-col :span="2" class="middleText">
					<uv-button type="primary" shape="circle" text="保存" style="width: 80px;" @click="save"></uv-button>
					<!-- <uni-icons type="checkmarkempty" size="50" @click="save"></uni-icons> -->
				</uni-col>
				<uni-col :span="11">
					<view id="menuTable">
						<uni-section title="选中菜单" subTitle="分配菜单并保存" type="line">
						<scroll-view scroll-y="true" class="scroll-Y selectUser">
							<view class="uni-px-5 uni-pb-5">
								<TreeMenu
									ref="treeMenu"
									:data="treeMenuData"
									@node-change="onMenuNodeChange"
									@data-change="onMenuDataChange"
								/>
							</view>
						</scroll-view>
						</uni-section>
					</view>
				</uni-col>
			</uni-row>
		</view>
		
			<view>
				<!-- 提示信息弹窗 -->
				<uni-popup ref="message" type="message">
					<uni-popup-message type="success" :message="'保存成功！'" ></uni-popup-message>
				</uni-popup>
			</view>
	</view>
</template>

<script>
	import urlPrefix from '@/pages/common/urlPrefix.js'
	import TreeMenu from '@/components/TreeMenu/TreeMenu.vue'
	export default {
		components: {
			TreeMenu
		},
		data() {
			return {
				inputUserNo: '',
				userNo:'',
				users: [],
				menuData: [],
				menus:[],
				menuNo:'',
				checkboxMenu:[],
				treeMenuData: [], // 树形菜单数据
				selectedMenuData: [] // 选中的菜单数据
			}
		},
		mounted() {
			this.getMenu();
		},
		methods: {
			getUser(event){
				if(event.length > 3){
					uni.request({
					   url: urlPrefix + "/user/getUser?userNo="+event+"",
					   method: "GET"
					 }).then(res => {
						this.users.length = 0;
						const convertedArray = res.data.data.map(item => {
								this.users.push({ value: item.userId, text: item.userId+' : '+item.userDesc });
						});
					 }).catch(err => {
					   console.log(err)
					 });
				} else{
					this.userNo = '';
					this.users.length = 0;
					this.menus.length = 0;
					this.checkboxMenu = [];
					
				}
			},
			getMenu(){
				uni.request({
				   url: urlPrefix + "/menu/findByUser?userNo="+this.userNo+"",
				   method: "GET"
				 }).then(res => {
					 this.menus.length = 0;
					 this.treeMenuData.length = 0;
					var i = 1;
					if(res.data.data != ''){
						this.checkboxMenu = [];
						const convertedArray = res.data.data.map(item => {
								if(item.checked){
									this.checkboxMenu.push(item.menuNo);
								}
							this.menus.push({ value: item.menuNo, text: i +' : '+item.menuDesc, checked: item.checked, menuList: item.menuList });
							i++;
						});
						
						// 转换为三层树形结构
						this.treeMenuData = this.convertToTreeStructure(res.data.data);
					}
				 }).catch(err => {
				   console.log(err)
				 })
			},
			
			// 将后端数据转换为三层树形结构
			convertToTreeStructure(menuData) {
				const treeData = [];
				
				// 分离不同层级的菜单
				const firstLevelMenus = []; // menuParentNo = 0 的一级菜单（分类）
				const secondLevelMenus = []; // menuParentNo != 0 且 menuLevel = 0 的二级菜单（功能菜单）
				const thirdLevelMenus = []; // menuLevel = 1 的三级菜单（按钮权限）
				
				menuData.forEach(menu => {
					if (menu.menuParentNo === 0) {
						// 第一层：父级菜单分类
						firstLevelMenus.push({
							value: menu.menuNo,
							text: menu.menuDesc,
							checked: menu.checked || false,
							expanded: false,
							children: [],
							originalData: menu
						});
					} else {
						// 第二层：具体功能菜单
						const menuNode = {
							value: menu.menuNo,
							text: menu.menuDesc,
							checked: menu.checked || false,
							expanded: false,
							children: [],
							parentNo: menu.menuParentNo,
							originalData: menu
						};
						
						// 第三层：菜单权限（从menuList中获取）
						if (menu.menuList && menu.menuList.length > 0) {
							menu.menuList.forEach(permission => {
								menuNode.children.push({
									value: permission.menuNo,
									text: permission.menuDesc,
									checked: permission.checked || false,
									parentNo: menu.menuNo,
									originalData: permission
								});
							});
						}
						
						secondLevelMenus.push(menuNode);
					}
				});
				
				// 构建树形结构：将二级菜单挂载到对应的一级菜单下
				firstLevelMenus.forEach(firstLevel => {
					const childMenus = secondLevelMenus.filter(secondLevel =>
						secondLevel.parentNo === firstLevel.value
					);
					firstLevel.children = childMenus;
				});
				
				return firstLevelMenus;
			},
			
			// 树形菜单节点变化事件
			onMenuNodeChange(data) {
				console.log('节点变化:', data);
				// 这里可以添加节点变化的处理逻辑
			},
			
			// 树形菜单数据变化事件
			onMenuDataChange(selectedData) {
				console.log('选中数据变化:', selectedData);
				this.selectedMenuData = selectedData;
				// 实时更新treeMenuData以保持数据同步
				this.syncTreeMenuData();
			},
			
			// 同步树形菜单数据
			syncTreeMenuData() {
				// 通过ref获取TreeMenu组件的最新数据
				if (this.$refs.treeMenu) {
					this.treeMenuData = this.$refs.treeMenu.treeData;
				}
			},
			back(){
				uni.navigateBack({
					delta: 1,
					animationType: 'pop-out',
					animationDuration: 200
				})
			},
			change(){
				this.getMenu();
			},
			messageToggle(type) {
				this.$refs.message.open();
			},
			save(){
				console.log("开始保存");
				// 创建包含JSON数组的JavaScript对象
				let selectedMenus = [];
				
				// 从TreeMenu组件获取最新的选中数据
				if (this.$refs.treeMenu) {
					const latestSelectedData = this.$refs.treeMenu.getSelectedData();
					console.log("从TreeMenu获取的最新选中数据:", latestSelectedData);
					
					// 从最新选中数据中收集菜单编号
					latestSelectedData.forEach(item => {
						if (item.value && !item.value.toString().startsWith('category_')) {
							selectedMenus.push({
								userNo: this.userNo[0],
								menuNo: item.value
							});
						}
					});
				} else {
					// 备用方案：从本地数据收集
					this.collectSelectedMenus(this.treeMenuData, selectedMenus);
				}
				
				// 如果没有选中的菜单项，插入默认值
				if (selectedMenus.length === 0) {
					selectedMenus.push({
						userNo: this.userNo[0],
						menuNo: -1
					});
				}
				
				let jsonDataString = JSON.stringify(selectedMenus);
				console.log("提交的数据:", jsonDataString);
				
				uni.request({
				   url: urlPrefix + "/menu/editMenuByUser",
				   data:jsonDataString,
				   method: "POST"
				 }).then(res => {
					// uni.reLaunch({
					//     url: '/pages/menu/menu'
					// })
					this.messageToggle('success');
				 }).catch(err => {
				   console.log(err)
				 })
			},
			
			// 递归收集选中的菜单数据（备用方案）
			collectSelectedMenus(treeData, selectedMenus) {
				treeData.forEach(node => {
					if (node.checked && node.value && !node.value.toString().startsWith('category_')) {
						// 只收集非分类节点且选中的菜单
						selectedMenus.push({
							userNo: this.userNo[0],
							menuNo: node.value
						});
					}
					
					// 递归处理子节点
					if (node.children && node.children.length > 0) {
						this.collectSelectedMenus(node.children, selectedMenus);
					}
				});
			}
		}
	}
</script>

<style>
page {
	width: 100%;
	height: 100%;
	padding: 2.5% 2% 1.5% 2%;
	box-sizing: border-box;
	background-color: white;
}
.container {
	width: 100%;
	height: 100%;
	box-sizing: border-box;
	border-radius: 10px;
	box-shadow: 0 0 1px 5px #dddddd;
}

#userTable{
	padding-left: 20rpx;
	margin-top: 0vh;
}
.selectUser{
	height: 70vh;
	border: 1px solid #dddddd;
	border-radius: 4px;
}
#menuTable{
	font-size: 20px;
	margin-top: 3.5vh;
	padding-right: 20rpx;
}
.middleText{	
	margin-top: 50vh;
	display: flex;
	justify-content: center;
}
.text{
	font-size: 20px;
}
uni-row{
	margin-top: 5vh;
}

:deep(.checklist-text){
	font-size: 18px !important;
}
.right-top-top{
	display: flex;
}

.back {
	display: flex;
	justify-content: space-evenly;
	align-items: center;
	border-radius: 50%;
	box-shadow: 0 0 5px gray;
	cursor: pointer;
	width: 50px;
	height: 50px;
	z-index: 1;
	margin-bottom: 15px;
}
/* 原有的菜单样式已移除，现在使用TreeMenu组件的样式 */
</style>
