<template>
  <view class="tree-menu">
    <view v-for="(item, index) in treeData" :key="index" class="tree-node">
      <!-- 第一层：父级菜单分类 -->
      <view class="tree-level-1" @click="toggleExpand(item, 1)">
        <view class="tree-node-content">
          <uni-icons 
            :type="item.expanded ? 'down' : 'right'" 
            size="16" 
            class="expand-icon"
          ></uni-icons>
          <text class="node-text">{{ item.text }}</text>
          <uv-switch 
            v-if="item.showSwitch" 
            v-model="item.checked" 
            @change="onNodeChange(item, 1)"
            class="node-switch"
          ></uv-switch>
        </view>
      </view>
      
      <!-- 第二层：具体菜单 -->
      <view v-if="item.expanded && item.children" class="tree-level-2-container">
        <view 
          v-for="(child, childIndex) in item.children" 
          :key="childIndex" 
          class="tree-level-2"
        >
          <view class="tree-node-content" @click="toggleExpand(child, 2)">
            <view class="indent-line"></view>
            <uni-icons 
              :type="child.expanded ? 'down' : 'right'" 
              size="14" 
              class="expand-icon"
              v-if="child.children && child.children.length > 0"
            ></uni-icons>
            <view class="expand-icon" v-else></view>
            <text class="node-text">{{ child.text }}</text>
            <uv-switch 
              v-model="child.checked" 
              @change="onNodeChange(child, 2)"
              class="node-switch"
            ></uv-switch>
          </view>
          
          <!-- 第三层：菜单权限 -->
          <view v-if="child.expanded && child.children" class="tree-level-3-container">
            <view 
              v-for="(permission, permIndex) in child.children" 
              :key="permIndex" 
              class="tree-level-3"
            >
              <view class="tree-node-content">
                <view class="indent-line"></view>
                <view class="indent-line"></view>
                <uni-icons type="circle" size="8" class="permission-icon"></uni-icons>
                <text class="node-text permission-text">{{ permission.text }}</text>
                <uv-switch 
                  v-model="permission.checked" 
                  @change="onNodeChange(permission, 3)"
                  class="node-switch"
                ></uv-switch>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'TreeMenu',
  props: {
    // 树形数据
    data: {
      type: Array,
      default: () => []
    },
    // 是否显示开关
    showSwitch: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      treeData: []
    }
  },
  watch: {
    data: {
      handler(newData) {
        this.treeData = this.processTreeData(newData);
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 处理树形数据，添加展开状态
    processTreeData(data) {
      return data.map(item => ({
        ...item,
        expanded: item.expanded !== undefined ? item.expanded : false,
        children: item.children ? this.processTreeData(item.children) : null
      }));
    },
    
    // 切换展开/收起状态
    toggleExpand(node, level) {
      this.$set(node, 'expanded', !node.expanded);
      this.$emit('node-expand', { node, level, expanded: node.expanded });
    },
    
    // 节点选中状态改变
    onNodeChange(node, level) {
      console.log('节点状态改变:', node.text, node.checked, level);
      
      // 处理父子节点联动
      if (level === 1) {
        // 第一层改变时，影响所有子节点
        this.updateChildrenChecked(node, node.checked);
      } else if (level === 2) {
        // 第二层改变时，影响子权限节点
        this.updateChildrenChecked(node, node.checked);
        // 同时检查是否需要更新父节点状态
        this.updateParentChecked(node);
      } else if (level === 3) {
        // 第三层改变时，检查是否需要更新父节点状态
        this.updateParentChecked(node);
      }
      
      // 强制更新视图
      this.$forceUpdate();
      
      // 获取最新的选中数据并发送给父组件
      const selectedData = this.getSelectedData();
      console.log('发送选中数据:', selectedData);
      
      this.$emit('node-change', { node, level, checked: node.checked });
      this.$emit('data-change', selectedData);
    },
    
    // 更新子节点选中状态
    updateChildrenChecked(node, checked) {
      if (node.children) {
        node.children.forEach(child => {
          this.$set(child, 'checked', checked);
          this.updateChildrenChecked(child, checked);
        });
      }
    },
    
    // 更新父节点选中状态
    updateParentChecked(node) {
      // 这里需要找到父节点，由于数据结构的限制，暂时简化处理
      // 实际项目中可以通过维护父子关系映射来实现
    },
    
    // 获取选中的数据
    getSelectedData() {
      const selected = [];
      
      const collectSelected = (nodes, level = 1) => {
        nodes.forEach(node => {
          if (node.checked) {
            selected.push({
              ...node,
              level: level
            });
          }
          if (node.children) {
            collectSelected(node.children, level + 1);
          }
        });
      };
      
      collectSelected(this.treeData);
      return selected;
    },
    
    // 设置选中数据
    setSelectedData(selectedIds) {
      const setChecked = (nodes) => {
        nodes.forEach(node => {
          this.$set(node, 'checked', selectedIds.includes(node.value));
          if (node.children) {
            setChecked(node.children);
          }
        });
      };
      
      setChecked(this.treeData);
    },
    
    // 获取当前树形数据（供父组件调用）
    getCurrentTreeData() {
      return this.treeData;
    }
  }
}
</script>

<style scoped>
.tree-menu {
  width: 100%;
}

.tree-node {
  margin-bottom: 8px;
}

.tree-node-content {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background-color 0.2s;
}

.tree-node-content:active {
  background-color: #f5f5f5;
}

/* 第一层样式 */
.tree-level-1 .tree-node-content {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  font-weight: 600;
  font-size: 16px;
}

/* 第二层样式 */
.tree-level-2-container {
  margin-left: 20px;
  border-left: 2px solid #e9ecef;
}

.tree-level-2 .tree-node-content {
  background-color: #ffffff;
  border: 1px solid #f0f0f0;
  font-size: 15px;
  margin-bottom: 4px;
}

/* 第三层样式 */
.tree-level-3-container {
  margin-left: 20px;
}

.tree-level-3 .tree-node-content {
  background-color: #fafafa;
  font-size: 14px;
  color: #666;
  margin-bottom: 2px;
}

/* 缩进线 */
.indent-line {
  width: 20px;
  height: 1px;
  background-color: #ddd;
  margin-right: 8px;
  position: relative;
}

.indent-line::before {
  content: '';
  position: absolute;
  left: 0;
  top: -8px;
  width: 1px;
  height: 16px;
  background-color: #ddd;
}

/* 图标样式 */
.expand-icon {
  margin-right: 8px;
  color: #666;
  cursor: pointer;
}

.permission-icon {
  margin-right: 8px;
  color: #999;
}

/* 文本样式 */
.node-text {
  flex: 1;
  margin-right: 12px;
}

.permission-text {
  color: #666;
  font-size: 13px;
}

/* 开关样式 */
.node-switch {
  flex-shrink: 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .tree-node-content {
    padding: 6px 8px;
  }
  
  .tree-level-1 .tree-node-content {
    font-size: 15px;
  }
  
  .tree-level-2 .tree-node-content {
    font-size: 14px;
  }
  
  .tree-level-3 .tree-node-content {
    font-size: 13px;
  }
}
</style>