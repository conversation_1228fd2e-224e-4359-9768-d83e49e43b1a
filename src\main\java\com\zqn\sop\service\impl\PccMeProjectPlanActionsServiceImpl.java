package com.zqn.sop.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.zqn.sop.entity.PccMeProjectPlanActions;
import com.zqn.sop.entity.PccSopCont;
import com.zqn.sop.mapper.PccMeProjectPlanActionsMapper;
import com.zqn.sop.mapper.PccSopContMapper;
import com.zqn.sop.service.PccMeProjectPlanActionsService;
import com.zqn.sop.vo.PccMeProjectPlanDtVo;
import com.zqn.sop.vo.PccMeProjectPlanHdVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Service
@DS("app")
public class PccMeProjectPlanActionsServiceImpl implements PccMeProjectPlanActionsService {

    private static final Logger logger = LoggerFactory.getLogger(PccMeProjectPlanActionsServiceImpl.class);

    @Resource
    private PccSopContMapper pccSopContMapper;

    @Resource
    private PccMeProjectPlanActionsMapper actionsMapper;

    private static void dealCont(List<PccSopCont> pccSopConts, PccMeProjectPlanDtVo vo) {
        for (PccSopCont pccSopCont : pccSopConts) {
            String content = pccSopCont.getContent();
            String lang = pccSopCont.getLang();
            Integer type = pccSopCont.getType();

            if (StrUtil.equals(lang, "zh-Hans")) {
                if (type == 1) {
                    vo.setOp_std(content);
                } else if (type == 2) {
                    vo.setSelf_check_points(content);
                }
            }
            if (StrUtil.equals(lang, "en")) {
                if (type == 1) {
                    vo.setOp_std_en(content);
                } else if (type == 2) {
                    vo.setSelf_check_points_en(content);
                }
            }
            if (StrUtil.equals(lang, "vi")) {
                if (type == 1) {
                    vo.setOp_std_vn(content);
                } else if (type == 2) {
                    vo.setSelf_check_points_vn(content);
                }
            }
            if (StrUtil.equals(lang, "id")) {
                if (type == 1) {
                    vo.setOp_std_id(content);
                } else if (type == 2) {
                    vo.setSelf_check_points_id(content);
                }
            }
            if (StrUtil.equals(lang, "bn")) {
                if (type == 1) {
                    vo.setOp_std_bd(content);
                } else if (type == 2) {
                    vo.setSelf_check_points_bd(content);
                }
            }
            if (StrUtil.equals(lang, "fil")) {
                if (type == 1) {
                    vo.setOp_std_ph(content);
                } else if (type == 2) {
                    vo.setSelf_check_points_ph(content);
                }
            }

            if (type == 3) {
                vo.setProtective_gear(content);
            } else if (type == 4) {
                vo.setTools(content);
            } else if (type == 5) {
                vo.setChemical_substance(content);
            } else if (type == 6) {
                vo.setMachine(content);
            } else if (type == 7) {
                vo.setTemp(content);
            } else if (type == 8) {
                vo.setPressure(content);
            } else if (type == 9) {
                vo.setTime(content);
            }
        }
    }

    @Override
    public PccMeProjectPlanDtVo queryContByAction(String ids) {
        PccMeProjectPlanDtVo pccMeProjectPlanDtVo = new PccMeProjectPlanDtVo();
        String[] idArray = ids.split("-");
        List<String> idList = Arrays.asList(idArray);
        List<PccSopCont> pccSopConts = pccSopContMapper.queryContByAction(idList);
        dealCont(pccSopConts, pccMeProjectPlanDtVo);
        return pccMeProjectPlanDtVo;
    }

    @Override
    @Transactional
    public Integer save(PccMeProjectPlanHdVo pccMeProjectPlanHdVo) {
        PccMeProjectPlanDtVo vo = pccMeProjectPlanHdVo.getPccMeProjectPlanDts();
        List<PccSopCont> pccSopConts = new ArrayList<>();
        //先保存动作
        PccMeProjectPlanActions pccMeProjectPlanActions = new PccMeProjectPlanActions();
        pccMeProjectPlanActions.setType(Integer.valueOf(pccMeProjectPlanHdVo.getDept()));
        pccMeProjectPlanActions.setAction_cn(vo.getActions());
        //查询是否已经存在相同的动作
        Integer parent_id = 0;
        Integer exist = actionsMapper.exist(pccMeProjectPlanActions);
        if (exist == null) {
            actionsMapper.add(pccMeProjectPlanActions);
            parent_id = pccMeProjectPlanActions.getId();
        } else {
            parent_id = exist;
        }
        //再保存内容
        //操作标准
        if (StrUtil.isNotEmpty(vo.getOp_std())) {
            PccSopCont pccSopCont = new PccSopCont();
            pccSopCont.setContent(vo.getOp_std());
            pccSopCont.setType(1);
            pccSopCont.setLang(vo.getLang());
            pccSopConts.add(pccSopCont);
        }
        //自检点
        if (StrUtil.isNotEmpty(vo.getSelf_check_points())) {
            PccSopCont pccSopCont = new PccSopCont();
            pccSopCont.setContent(vo.getSelf_check_points());
            pccSopCont.setType(2);
            pccSopCont.setLang(vo.getLang());
            pccSopConts.add(pccSopCont);
        }
        //防护用品
        if (StrUtil.isNotEmpty(vo.getProtective_gear())) {
            PccSopCont pccSopCont = new PccSopCont();
            pccSopCont.setContent(vo.getProtective_gear());
            pccSopCont.setType(3);
            pccSopCont.setLang(vo.getLang());
            pccSopConts.add(pccSopCont);
        }
        //工具
        if (StrUtil.isNotEmpty(vo.getTools())) {
            PccSopCont pccSopCont = new PccSopCont();
            pccSopCont.setContent(vo.getTools());
            pccSopCont.setType(4);
            pccSopCont.setLang(vo.getLang());
            pccSopConts.add(pccSopCont);
        }
        //化学品
        if (StrUtil.isNotEmpty(vo.getChemical_substance())) {
            PccSopCont pccSopCont = new PccSopCont();
            pccSopCont.setContent(vo.getChemical_substance());
            pccSopCont.setType(5);
            pccSopCont.setLang(vo.getLang());
            pccSopConts.add(pccSopCont);
        }
        //机器
        if (StrUtil.isNotEmpty(vo.getMachine())) {
            PccSopCont pccSopCont = new PccSopCont();
            pccSopCont.setContent(vo.getMachine());
            pccSopCont.setType(6);
            pccSopCont.setLang(vo.getLang());
            pccSopConts.add(pccSopCont);
        }
        //温度
        if (StrUtil.isNotEmpty(vo.getTemp())) {
            PccSopCont pccSopCont = new PccSopCont();
            pccSopCont.setContent(vo.getTemp());
            pccSopCont.setType(7);
            pccSopCont.setLang(vo.getLang());
            pccSopConts.add(pccSopCont);
        }
        //压力
        if (StrUtil.isNotEmpty(vo.getPressure())) {
            PccSopCont pccSopCont = new PccSopCont();
            pccSopCont.setContent(vo.getPressure());
            pccSopCont.setType(8);
            pccSopCont.setLang(vo.getLang());
            pccSopConts.add(pccSopCont);
        }
        //时间
        if (StrUtil.isNotEmpty(vo.getTime())) {
            PccSopCont pccSopCont = new PccSopCont();
            pccSopCont.setContent(vo.getTime());
            pccSopCont.setType(9);
            pccSopCont.setLang(vo.getLang());
            pccSopConts.add(pccSopCont);
        }
        for (PccSopCont pccSopCont : pccSopConts) {
            pccSopCont.setParent_id(0);
            pccSopCont.setAction_id(parent_id);
            pccSopContMapper.add(pccSopCont);
        }

        return null;
    }

    @Override
    public List<JSONObject> queryTag(String type) {
        List<JSONObject> result = new ArrayList<>();
        List<PccMeProjectPlanActions> pccMeProjectPlanActions = actionsMapper.queryTag(type);
        int i = 0;
        for (PccMeProjectPlanActions pccactions : pccMeProjectPlanActions) {
            i++;
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("value", pccactions.getTag());
            jsonObject.put("text", pccactions.getTag());
            result.add(jsonObject);
        }
        return result;
    }
}
