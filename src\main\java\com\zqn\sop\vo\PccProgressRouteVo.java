package com.zqn.sop.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.Date;


@Data
public class PccProgressRouteVo   {

    private Integer id;
    //主题
    @NotBlank(message = "主题不能为空")
    private String theme;
    // 厂别
    @NotBlank(message = "厂别不能为空")
    private String factory;
    // 部门
    @NotBlank(message = "部门不能为空")
    private String dept;
    //    问题明细
    @NotBlank(message = "问题明细不能为空")
    private String problem_statement;
    //  改善的目的
    @NotBlank(message = "改善的目的不能为空")
    private String purpose;
    // 现状把握
    @NotBlank(message = "现状把握不能为空")
    private String current_states;
    //     目标设定
    @NotBlank(message = "目标设定不能为空")
    private String target_setting;

    //      要素分析
    @NotBlank(message = "要素分析不能为空")
    private String cause_analysis;
    //    对策立案
    @NotBlank(message = "对策立案不能为空")
    private String contemeasures;

    // 实施计划
    @NotBlank(message = "实施计划不能为空")
    private String action_plan;
    //    结果确认
    @NotBlank(message = "结果确认不能为空")
    private String results_evaluation;

    //       标准化
    @NotBlank(message = "标准化不能为空")
    private String standardization;

    //     创建人
    private String create_by;
    //            创建日期
    @JsonFormat(pattern = "YYYY-MM-dd HH:mm:ss")
    private Date create_date;
    //  修改人
    private String update_by;
    //修改日期
    @JsonFormat(pattern = "YYYY-MM-dd HH:mm:ss")
    private Date update_date;

    @NotBlank(message = "提案人不能为空")
    private String proposer;

}
