<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zqn.modeldata2.mapper.ShoeUpperSchedulingMapper">


    <select id="list" resultType="com.zqn.modeldata2.entity.ShoeUpperScheduling">

        SELECT
        X.EMP_NAME,
        X.ORD_NO,
        Y.MODEL_PIC,
        X.DEV_TYPE,
        X.SHP_DATE,
        X.TOT_QTY,
        X.BAR_QTY,
        Z.PRD_DATE,
        Z.RUN_RATE,
        k.made_dept,
        ROUND( fn_sy_div ( k.bar_qty, Z.RUN_RATE ) * 100, 2 ) rate1,
        RTRIM( ( SELECT F_LINKS ( DISTINCT fn_sy_getxcodeval ( 'NC026-1', PB_DESC ) || '/' ) FROM mk_slastbar WHERE ord_no = X.ord_no ), '/' ) PB_DESC
        FROM
        (
        SELECT
        D.DEPT_NO,
        D.EMP_NO,
        d.emp_name,
        d.made_dept,
        a.ord_no,
        a.dev_type,
        b.shp_date,
        b.tot_qty,
        a.model_no,
        SUM( c.bar_qty ) bar_qty
        FROM
        gc_sorder a,
        gc_sorders b,
        mk_sorderbar c,
        bd_empgrp d
        WHERE
        a.ord_no = b.ord_no
        AND a.ord_no = c.ord_no
        AND c.made_dept = d.made_dept
        AND c.semi_no = 'U'
        AND C.Semi_Su = '2'
        AND b.cl_flag = 'N'
        AND TRUNC( c.bar_date ) = TRUNC( to_date( #{prdDate}, 'yyyy/mm/dd' ) )
        GROUP BY
        D.DEPT_NO,
        D.EMP_NO,
        d.emp_name,
        d.made_dept,
        a.ord_no,
        a.dev_type,
        b.shp_date,
        b.tot_qty,
        a.model_no
        ) x,
        cb_model y,
        BD_EMPGRPd z,
        (
        SELECT
        a.made_dept,
        round(
        SUM( decode( d.ordu_sets, 0, a.bar_qty, nxuser.fn_mk_semiper ( d.ordu_sets, 'BC027' ) * bar_qty ) ),
        2
        ) bar_qty
        FROM
        mk_sorderbar a,
        gc_sorder d
        WHERE
        a.ord_no = d.ord_no
        AND semi_no = 'U'
        AND TYPE = 'O'
        AND semi_su = '2'
        AND TRUNC( bar_date ) = TRUNC( to_date( #{prdDate}, 'yyyy/mm/dd' ) )
        GROUP BY
        a.made_dept
        ) k
        WHERE
        x.model_no = y.model_no
        AND x.dept_no = z.dept_no ( + )
        AND x.made_dept = z.made_dept ( + )
        AND TRUNC( prd_datE ) = TRUNC( to_date( #{prdDate}, 'yyyy/mm/dd' ) )
        AND x.made_dept = k.made_dept ( + )

                        <if test="empName != null and empName != ''">
                            and x.EMP_NAME LIKE '%' || #{empName} || '%'
                        </if>

                        <if test="beginShpDate != null and beginShpDate != ''">
                            and x.SHP_DATE &gt;= TO_DATE(#{beginShpDate}, 'YYYY-MM-DD')
                        </if>

                        <if test="endShpDate != null and endShpDate != ''">
                            and x.SHP_DATE &lt;= TO_DATE(#{endShpDate}, 'YYYY-MM-DD')
                        </if>

    </select>
    <select id="detail" resultType="java.lang.String">

        SELECT
        round(
        SUM( decode( d.ordu_sets, 0, a.bar_qty, nxuser.fn_mk_semiper ( d.ordu_sets, 'BC027' ) * bar_qty ) ),
        2
        ) bar_qty
        FROM
        mk_sorderbar a,
        gc_sorder d
        WHERE
        a.ord_no = d.ord_no
        AND semi_no = 'U'
        AND semi_su = '1'
        AND TRUNC( bar_date ) = TRUNC( to_date(#{prdDate}, 'yyyy-mm-dd' ) )
        AND EXISTS ( SELECT NULL FROM gc_sorders WHERE ord_no = d.ord_no AND u2_qty  &lt; tot_qty )
        AND a.made_dept = #{madeDept}
    </select>
</mapper>