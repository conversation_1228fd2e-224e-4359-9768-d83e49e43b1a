package com.zqn.modeldata2.service;

import java.util.List;
import java.util.Map;

public interface FirstService {
    List<Object> getBrands();

    List<Object> getBrandsPlus();

    Map<String, Object> getModel(String model_no);

    List<Object> getModels(String brand_no, String model_no);

    List<Object> getModelsByShoeLast(String brand_no, String shoe_last);

    Map<String, Object> getModelPicture(String model_no);

    List<String> getSizeTypeList(String brand_no);

    List<String> getSizeOption(String siz_type);

    Integer updateSizeType(String model_no, String siz_type, String upd_user);

    Integer updateBaseSize(String model_no, String bas_size, String upd_user);
}
