package com.zqn.modeldata2.service;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.entity.MaterialMatch;
import com.zqn.modeldata2.entity.MaterialMatchDt;
import com.zqn.modeldata2.entity.Qdpt;
import com.zqn.modeldata2.entity.QdptDt;

import java.util.Date;
import java.util.List;

public interface MaterialMatchService {


    R<PageInfo<MaterialMatch>> query(int pageNo, int pageSize, Date startTime, Date endTime, String brand, String sampleType, Integer fileType, Integer cutComplType);

    List<JSONObject> queryAllDevType();

    List<MaterialMatchDt> queryDetailTableData(String ordNo);

}