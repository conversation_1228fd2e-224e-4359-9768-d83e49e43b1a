package com.zqn.modeldata2.service.impl;

import com.zqn.modeldata2.entity.CkSmodelp;
import com.zqn.modeldata2.entity.Sign;
import com.zqn.modeldata2.entity.SignFile;
import com.zqn.modeldata2.mapper.ReportMapper;
import com.zqn.modeldata2.service.ReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionInterceptor;

import java.util.List;
import java.util.Map;

@Service
public class ReportServiceImpl implements ReportService {
    @Autowired
    private ReportMapper reportMapper;

    @Override
    public Map<String, Object> getPicture(String model_no) {
        return reportMapper.getPicture(model_no);
    }

    @Override
    public Map<String, Object> getInfo(String model_no) {
        return reportMapper.getInfo(model_no);
    }

    @Override
    public List<Object> getTitle(String model_no, String siz_type, double bas_size) {
        return reportMapper.getTitle(model_no, siz_type, bas_size);
    }

    @Override
    public Boolean getFullSize(String model_no) {
        Integer result = reportMapper.getFullSize(model_no);
        return result != null && result == 2;
    }

    @Override
    public Integer updateFullSize(String model_no, Boolean full_size) {
        Integer result = -1;
        try {
            if (!full_size) {
                result = reportMapper.updateFullSize(model_no, 1);
            } else {
                result = reportMapper.updateFullSize(model_no, 2);
            }
        } catch (Exception e) {
            return result;
        }
        return result;
    }

    @Override
    public List<Object> getCount1(String model_no, String siz_type, double bas_size, int procs_type) {
        return reportMapper.getCount1(model_no, siz_type, bas_size, procs_type);
    }

    @Override
    public List<Object> getCount2(String model_no, String siz_type, double bas_size, int procs_type) {
        return reportMapper.getCount2(model_no, siz_type, bas_size, procs_type);
    }

    @Override
    public CkSmodelp getInsertInfo(String model_no) {
        return reportMapper.getInsertInfo(model_no);
    }

    @Override
    public Sign getSignature(Sign sign) {
        Sign s = new Sign();
        byte[] chk_pic = reportMapper.getChkSignature(sign) != null ? reportMapper.getChkSignature(sign).getSignature() : null;
        byte[] upp_pic = reportMapper.getUppSignature(sign) != null ? reportMapper.getUppSignature(sign).getSignature() : null;
        byte[] sol_pic = reportMapper.getSolSignature(sign) != null ? reportMapper.getSolSignature(sign).getSignature() : null;
        s.setChk_pic(chk_pic);
        s.setUpp_pic(upp_pic);
        s.setSol_pic(sol_pic);
        return s;
    }

    @Override
    @Transactional
    public Integer saveChkSignature(Sign sign) {
        Integer result = -1;
        try {
            Sign s = reportMapper.getSignatureById(sign);
            if (s == null || s.getSignature() == null) {
                return result;
            }
            SignFile signFile = new SignFile();
            signFile.setModel_no(sign.getModel_no());
            SignFile sf = reportMapper.getSignatureFile(signFile);
            if (sf == null) {
                sf = new SignFile();
                sf.setModel_no(sign.getModel_no());
                sf.setChk_user(s.getUser_no());
                sf.setIns_user(sign.getIns_user());
                sf.setIns_date(sign.getIns_date());
                sf.setUpd_user(sign.getUpd_user());
                sf.setUpd_date(sign.getUpd_date());
                result = reportMapper.addChkSignature(sf);
            } else {
                sf.setModel_no(sign.getModel_no());
                sf.setChk_user(s.getUser_no());
                sf.setIns_user(sign.getIns_user());
                sf.setIns_date(sign.getIns_date());
                sf.setUpd_user(sign.getUpd_user());
                sf.setUpd_date(sign.getUpd_date());
                result = reportMapper.updateChkSignature(sf);
            }
//            result = 1 / 0;
        } catch (Exception e) {
            TransactionInterceptor.currentTransactionStatus().setRollbackOnly();
            return -1;
        }
        return result;
    }

    @Override
    @Transactional
    public Integer saveUppSignature(Sign sign) {
        Integer result = -1;
        try {
            Sign s = reportMapper.getSignatureById(sign);
            if (s == null || s.getSignature() == null) {
                return result;
            }
            SignFile signFile = new SignFile();
            signFile.setModel_no(sign.getModel_no());
            SignFile sf = reportMapper.getSignatureFile(signFile);
            if (sf == null) {
                sf = new SignFile();
                sf.setModel_no(sign.getModel_no());
                sf.setUpp_user(s.getUser_no());
                sf.setIns_user(sign.getIns_user());
                sf.setIns_date(sign.getIns_date());
                sf.setUpd_user(sign.getUpd_user());
                sf.setUpd_date(sign.getUpd_date());
                result = reportMapper.addUppSignature(sf);
            } else {
                sf.setModel_no(sign.getModel_no());
                sf.setUpp_user(s.getUser_no());
                sf.setIns_user(sign.getIns_user());
                sf.setIns_date(sign.getIns_date());
                sf.setUpd_user(sign.getUpd_user());
                sf.setUpd_date(sign.getUpd_date());
                result = reportMapper.updateUppSignature(sf);
            }
//            result = 1 / 0;
        } catch (Exception e) {
            TransactionInterceptor.currentTransactionStatus().setRollbackOnly();
            return -1;
        }
        return result;
    }

    @Override
    @Transactional
    public Integer saveSolSignature(Sign sign) {
        Integer result = -1;
        try {
            Sign s = reportMapper.getSignatureById(sign);
            if (s == null || s.getSignature() == null) {
                return result;
            }
            SignFile signFile = new SignFile();
            signFile.setModel_no(sign.getModel_no());
            SignFile sf = reportMapper.getSignatureFile(signFile);
            if (sf == null) {
                sf = new SignFile();
                sf.setModel_no(sign.getModel_no());
                sf.setSol_user(s.getUser_no());
                sf.setIns_user(sign.getIns_user());
                sf.setIns_date(sign.getIns_date());
                sf.setUpd_user(sign.getUpd_user());
                sf.setUpd_date(sign.getUpd_date());
                result = reportMapper.addSolSignature(sf);
            } else {
                sf.setModel_no(sign.getModel_no());
                sf.setSol_user(s.getUser_no());
                sf.setIns_user(sign.getIns_user());
                sf.setIns_date(sign.getIns_date());
                sf.setUpd_user(sign.getUpd_user());
                sf.setUpd_date(sign.getUpd_date());
                result = reportMapper.updateSolSignature(sf);
            }
//            result = 1 / 0;
        } catch (Exception e) {
            TransactionInterceptor.currentTransactionStatus().setRollbackOnly();
            return -1;
        }
        return result;
    }

    @Override
    public Integer resetChkSignature(SignFile signFile) {
        Integer result = -1;
        try {
            result = reportMapper.resetChkSignature(signFile);
//            result = 1 / 0;
        } catch (Exception e) {
            return result;
        }
        return result;
    }

    @Override
    public Integer resetUppSignature(SignFile signFile) {
        Integer result = -1;
        try {
            result = reportMapper.resetUppSignature(signFile);
//            result = 1 / 0;
        } catch (Exception e) {
            return result;
        }
        return result;
    }

    @Override
    public Integer resetSolSignature(SignFile signFile) {
        Integer result = -1;
        try {
            result = reportMapper.resetSolSignature(signFile);
//            result = 1 / 0;
        } catch (Exception e) {
            return result;
        }
        return result;
    }
}
