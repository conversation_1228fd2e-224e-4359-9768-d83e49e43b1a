<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zqn.modeldata2.mapper.MbptMapper">
    <!--
    -樣品單號 項次 樣品類型 鞋圖 製鞋組別 姓名  生産線 業務/版師  型體 楦頭編號
    - 派工日  出貨日  品牌  訂單量  面料狀況  底料狀況 副料配套
    -發外投入 發外產出 中底皮 中底 包粘  大底  鞋面投入 副料配套日期  半成品结案日期
    -半成品各欄位顯示”/“ 代表不用生產
    -uin_flag = Y  代表已設定生產組別，用戶可以修改生產組別  = N 代表沒有設定組別-->
    <!--    要求副料配套不显示-->
    <select id="query" resultType="com.zqn.modeldata2.entity.MbptDto">
        select ord_no, item_no, dev_type, emp_name, model_pic, pd_line, dutyer,upper_der,sole_der, model_no,
        last_no,grp_no,
        wo_date, shp_date, brand_no, tot_qty, t1_flag, t2_flag, t3_flag,
        y1_qty, y2_qty, b1_qty, b2_qty, b3_qty, b4_qty, uin_flag,t3_date, b_date,
        (SELECT SUM(a.app_qty)
        FROM bf_lastappd a
        JOIN bf_last b ON a.last_seq = b.last_seq
        WHERE b.last_no = vw.last_no AND b.last_noa = vw.last_no) AS sum_app_qty,
        (select count(*) from MK_SORDERBAR where ORD_NO = vw.ORD_NO and semi_su = '1' and SEMI_NO = 'Y') as in_qty,
        (select count(*) from MK_SORDERBAR where ORD_NO = vw.ORD_NO and semi_su = '2' and SEMI_NO = 'Y') as out_qty,
        GRP_NO || '' || EMP_NAME as made_dept
        FROM vw_mk2_u_frontfitting vw
        <where>
            <if test="brand != null and brand != ''">
                AND UPPER(brand_no) = UPPER(#{brand})
            </if>
            <if test="devType != null and devType != '' and devType != 'select'">
                AND dev_type = #{devType}
            </if>
            <if test="startTime != null and endTime != null">
                and shp_date between #{startTime} and #{endTime}
            </if>
            <if test="ordNo != null and ordNo != null">
                and UPPER(ord_no) like '%' || UPPER(#{ordNo}) || '%'
            </if>
            <if test="pdLine != null and pdLine != ''">
                and pd_line like '%' || #{pdLine} || '%'
            </if>
            <if test="t2Flag != null and t2Flag != ''">
                and UPPER(t2_flag) = UPPER(#{t2Flag})
            </if>

            <if test="grpNO != null and grpNO !=''">
                AND grp_no = #{grpNO}
            </if>
            <if test="bDate != null and bDate.size() > 0">
                AND b_date in
                <foreach item="date" index="index" collection="bDate"
                         open="(" separator="," close=")">
                    #{date}
                </foreach>
            </if>

        </where>
        and t3_flag = 'OK' and (made_dept is null or made_dept = '')
    </select>

    <select id="queryAllDevType" resultType="java.lang.String">
        select dev_type
        FROM vw_mk2_u_frontfitting
        group by dev_type
    </select>

    <!--     -用戶在平板上鞋面投入欄 設定組別，彈出窗口供用戶選擇製作組別
    -製作組別
    -emp_no 工號, emp_name 姓名, made_dept 製作組別,grp_no 組別編號-->
    <select id="queryDetailTableData" resultType="com.zqn.modeldata2.entity.MbptDtDto">
     <!--   SELECT EMP_NO,EMP_NAME,MADE_DEPT,GRP_NO
        FROM bd_empgrp
        WHERE inv_flag ='N' AND INSTR(DEPT_NO,'SP0100303')>0
        and emp_no is not null and emp_name is not null
        <if test="userNo != null and userNo != ''">
            AND EMP_NO like '%' || #{userNo} || '%'
        </if>
        <if test="grpNo != null and grpNo != ''">
            AND UPPER(GRP_NO) like '%' || UPPER(#{grpNo}) || '%'
        </if>
        order by grp_no-->
        SELECT EMP_NO,EMP_NAME,MADE_DEPT,GRP_NO
        FROM bd_employee
        WHERE inv_flag='N' AND PBAR_FLAG='Y'  AND INSTR(DEPT_NO,'SP0100303')>0
        and emp_no is not null and emp_name is not null
        <if test="userNo != null and userNo != ''">
            AND EMP_NO like '%' || #{userNo} || '%'
        </if>
        <if test="grpNo != null and grpNo != ''">
            AND UPPER(GRP_NO) like '%' || UPPER(#{grpNo}) || '%'
        </if>
        order by grp_no
    </select>
    <select id="queryBarDate" resultType="java.util.Date">
        SELECT bar_date
        FROM MK_SORDERBAR
        where ord_no = #{ordNo}
          and semi_no = 'U'
          AND SEMI_SU = '1'
    </select>

    <insert id="addMadeDept" parameterType="map" statementType="CALLABLE">
        {call pd_mk_sorderbar_ins(
                #{i_ord_no, mode=IN, jdbcType=VARCHAR},
                #{i_made_dept, mode=IN, jdbcType=VARCHAR},
                #{i_bar_qty, mode=IN, jdbcType=INTEGER},
                #{i_semi_no, mode=IN, jdbcType=VARCHAR},
                #{i_semi_su, mode=IN, jdbcType=VARCHAR},
                #{o_return, mode=INOUT, jdbcType=VARCHAR}
              )}
    </insert>


    <update id="editMadeDept">
        UPDATE MK_SORDERBAR
        SET MADE_DEPT = #{dto.made_dept}
        WHERE ORD_NO = #{dto.ord_no}
          AND TRUNC(BAR_DATE) = TRUNC(#{barDate})
          AND SEMI_NO = 'U'
          AND SEMI_SU = '1'
          AND TYPE = 'I'
    </update>

    <select id="queryMkSorderbarDtoByOrdNo" resultType="com.zqn.modeldata2.entity.MkSorderbarDto">
        SELECT
        m.*,
       ( select max(b.bar_date) from  MK_SORDERBAR b where b.semi_no='Y' AND b.semi_su ='2'and b.ord_no = #{ordNo}) as outsourced_date
       FROM MK_SORDERBAR m
        where  m.ord_no = #{ordNo}
          and  m.semi_no = 'U'
          AND  m.SEMI_SU = '1'
    </select>

    <select id="fwtrcc" statementType="CALLABLE" resultType="string">
        {call pd_mk_sorderbar_ins(
                #{i_ord_no, mode=IN, jdbcType=VARCHAR},
                #{i_made_dept, mode=IN, jdbcType=VARCHAR},
                #{i_bar_qty, mode=IN, jdbcType=INTEGER},
                #{i_semi_no, mode=IN, jdbcType=VARCHAR},
                #{i_semi_su, mode=IN, jdbcType=VARCHAR},
                #{o_return, mode=INOUT, jdbcType=VARCHAR}
              )}
    </select>


    <delete id="cancel">
        DELETE
        FROM MK_SORDERBAR
        WHERE ORD_NO = #{dto.ord_no}
          AND MADE_DEPT is null
          AND SEMI_NO = #{dto.semi_no}
          AND SEMI_SU = #{dto.semi_su}
    </delete>

    <select id="querycount" resultType="com.zqn.modeldata2.entity.MbptDto">
        select sum(tot_qty) as tot_qty_sum
        FROM vw_mk2_u_frontfitting vw
        <where>
            <if test="brand != null and brand != ''">
                AND UPPER(brand_no) = UPPER(#{brand})
            </if>
            <if test="devType != null and devType != '' and devType != 'select'">
                AND dev_type = #{devType}
            </if>
            <if test="startTime != null and endTime != null">
                and shp_date between #{startTime} and #{endTime}
            </if>
            <if test="ordNo != null and ordNo != null">
                and UPPER(ord_no) like '%' || UPPER(#{ordNo}) || '%'
            </if>
            <if test="pdLine != null and pdLine != ''">
                and pd_line like '%' || #{pdLine} || '%'
            </if>
            <if test="t2Flag != null and t2Flag != ''">
                and UPPER(t2_flag) = UPPER(#{t2Flag})
            </if>

            <if test="grpNO != null and grpNO !=''">
                AND grp_no = #{grpNO}
            </if>

            <if test="bDate != null and bDate.size() > 0">
                AND b_date in
                <foreach item="date" index="index" collection="bDate"
                         open="(" separator="," close=")">
                    #{date}
                </foreach>
            </if>
        </where>
        and t3_flag = 'OK' and (made_dept is null or made_dept = '')
    </select>

</mapper>