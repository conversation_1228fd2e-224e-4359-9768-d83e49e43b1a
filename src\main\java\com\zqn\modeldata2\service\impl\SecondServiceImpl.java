package com.zqn.modeldata2.service.impl;

import com.github.houbb.opencc4j.util.ZhConverterUtil;
import com.zqn.modeldata2.entity.CkSmodelp;
import com.zqn.modeldata2.entity.CkSmodelpPlus;
import com.zqn.modeldata2.mapper.SecondMapper;
import com.zqn.modeldata2.service.SecondService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Base64;
import java.util.List;

@Service
@Slf4j
public class SecondServiceImpl implements SecondService {
    @Autowired
    private SecondMapper secondMapper;

    @Override
    public List<CkSmodelp> getSpecification(CkSmodelp ckSmodelp) {
        return secondMapper.getSpecification(ckSmodelp);
    }

    @Override
    public List<CkSmodelpPlus> getSpecificationPlus(CkSmodelp ckSmodelp) {
        List<CkSmodelp> list = secondMapper.getSpecification(ckSmodelp);
        List<CkSmodelpPlus> result = new ArrayList<>();
        for (CkSmodelp item : list) {
            CkSmodelpPlus ckSmodelpPlus = new CkSmodelpPlus();
            ckSmodelpPlus.setModel_no(item.getModel_no());
            ckSmodelpPlus.setProcs_type(item.getProcs_type());
            ckSmodelpPlus.setS_size(item.getS_size());
            ckSmodelpPlus.setE_size(item.getE_size());
            ckSmodelpPlus.setIns_user(item.getIns_user());
            ckSmodelpPlus.setIns_date(item.getIns_date());
            ckSmodelpPlus.setUpd_user(item.getUpd_user());
            ckSmodelpPlus.setUpd_date(item.getUpd_date());

            if (item.getProcs_pic() != null) {
                byte[] picture = item.getProcs_pic();
                String base64 = Base64.getEncoder().encodeToString(picture);
                ckSmodelpPlus.setPic_base64("data:image/png;base64," + base64);
            }

            result.add(ckSmodelpPlus);
        }
        return result;
    }

    @Override
    public int addSpecification(CkSmodelp ckSmodelp) {
        int result = -1;
        try {
            if (ckSmodelp.getRemark() != null && ckSmodelp.getRemark().length() > 0) {
                ckSmodelp.setRemark(ZhConverterUtil.toTraditional(ckSmodelp.getRemark()));
            }
            result = secondMapper.addSpecification(ckSmodelp);
//            result = 1 / 0;
        } catch (Exception e) {
            return result;
        }
        return result;
    }

    @Override
    public int updateSpecification(CkSmodelp ckSmodelp) {
        int result = -1;
        try {
            if (ckSmodelp.getRemark() != null && ckSmodelp.getRemark().length() > 0) {
                ckSmodelp.setRemark(ZhConverterUtil.toTraditional(ckSmodelp.getRemark()));
            }
            result = secondMapper.updateSpecification(ckSmodelp);
//            result = 1 / 0;
        } catch (Exception e) {
            return result;
        }
        return result;
    }

    @Override
    public int deleteSpecification(CkSmodelp ckSmodelp) {
        int result = -1;
        try {
            result = secondMapper.deleteSpecification(ckSmodelp);
//            result = 1 / 0;
        } catch (Exception e) {
            return result;
        }
        return result;
    }

    @Override
    public int batchDeleteSpecification(List<CkSmodelp> ckSmodelpList) {
        int result = -1;
        try {
            result = secondMapper.batchDeleteSpecification(ckSmodelpList);
//            result = 1 / 0;
        } catch (Exception e) {
            return result;
        }
        return result;
    }

    @Override
    public Integer updateStartSize(CkSmodelp ckSmodelp) {
        Integer result = -1;
        try {
            result = secondMapper.updateStartSize(ckSmodelp);
        } catch (Exception e) {
            return result;
        }
        return result;
    }

    @Override
    public Integer updateEndSize(CkSmodelp ckSmodelp) {
        Integer result = -1;
        try {
            result = secondMapper.updateEndSize(ckSmodelp);
        } catch (Exception e) {
            return result;
        }
        return result;
    }
}
