package com.zqn.studentrecord.controller;

import com.zqn.modeldata2.common.R;
import com.zqn.studentrecord.entity.StudentRecord;
import com.zqn.studentrecord.service.StudentRecordService;
import com.zqn.studentrecord.service.StudentRecordService.PageResult;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 学员记录控制器
 */
@RestController
@RequestMapping("/student-record")
public class StudentRecordController {
    
    @Resource
    private StudentRecordService studentRecordService;
    
    /**
     * 创建学员记录
     * @param record 学员记录
     * @return 操作结果
     */
    @PostMapping
    public R<StudentRecord> createRecord(@RequestBody StudentRecord record) {
        return studentRecordService.createRecord(record);
    }
    
    /**
     * 根据ID获取学员记录
     * @param id 记录ID
     * @return 学员记录
     */
    @GetMapping("/{id}")
    public R<StudentRecord> getRecordById(@PathVariable Long id) {
        return studentRecordService.getRecordById(id);
    }
    
    /**
     * 更新学员记录
     * @param id 记录ID
     * @param record 学员记录
     * @return 操作结果
     */
    @PutMapping("/{id}")
    public R<StudentRecord> updateRecord(@PathVariable Long id, @RequestBody StudentRecord record) {
        return studentRecordService.updateRecord(id, record);
    }
    
    /**
     * 删除学员记录
     * @param id 记录ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    public R<Void> deleteRecord(@PathVariable Long id) {
        return studentRecordService.deleteRecord(id);
    }
    
    /**
     * 分页查询学员记录列表
     * @param page 页码（从1开始，默认1）
     * @param size 每页大小（默认10）
     * @param orderNo 单号（可选）
     * @param student 学员（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 分页结果
     */
    @GetMapping
    public R<PageResult<StudentRecord>> getRecordList(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String orderNo,
            @RequestParam(required = false) String student,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        return studentRecordService.getRecordList(page, size, orderNo, student, startDate, endDate);
    }
    
    /**
     * 根据单号获取鞋图
     * @param orderNo 单号
     * @return 鞋图路径
     */
    @GetMapping("/shoe-image")
    public R<StudentRecord> getShoeImageByOrderNo(@RequestParam String orderNo) {
        R<StudentRecord> result = studentRecordService.getShoeImageByOrderNo(orderNo);
        return result;
    }
    
    /**
     * 健康检查接口
     * @return 服务状态
     */
    @GetMapping("/health")
    public R<String> health() {
        return R.success("学员记录服务运行正常");
    }
} 