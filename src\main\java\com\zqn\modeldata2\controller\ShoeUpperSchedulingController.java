package com.zqn.modeldata2.controller;

import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.entity.Matting;
import com.zqn.modeldata2.entity.ShoeUpperScheduling;
import com.zqn.modeldata2.service.ShoeUpperSchedulingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController
@RequestMapping("/api/shoeUpperScheduling")
public class ShoeUpperSchedulingController {

    @Autowired
    private ShoeUpperSchedulingService shoeUpperSchedulingService;


    @GetMapping("/list")
    public R query(@RequestParam(value = "empName", required = false) String empName,
                   @RequestParam(value = "beginShpDate", required = false) String beginShpDate,
                   @RequestParam(value = "endShpDate", required = false) String endShpDate,
                   @RequestParam(value = "prdDate", required = true) String prdDate) {

        List<ShoeUpperScheduling> result = shoeUpperSchedulingService.list(empName, beginShpDate,endShpDate, prdDate);
        return R.success(result);
    }

}

