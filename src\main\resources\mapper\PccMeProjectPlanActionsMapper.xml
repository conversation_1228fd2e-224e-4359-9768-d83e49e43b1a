<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zqn.sop.mapper.PccMeProjectPlanActionsMapper">


    <insert id="add" useGeneratedKeys="true" keyProperty="id" keyColumn="ID">
        insert into PCC_ME_PROJECT_PLAN_ACTIONS
        (ITEM, ACTION_CN, ACTION_EN, ACTION_VN, TYPE, ACTION_ID, ACTION_BD, ACTION_PH)
        VALUES (0,
                #{vo.action_cn},
                '',
                '',
                #{vo.type},
                '',
                '',
                '')
    </insert>
    <select id="exist" resultType="java.lang.Integer">
        select id
        from PCC_ME_PROJECT_PLAN_ACTIONS
        where ACTION_CN = #{vo.action_cn}
          and type = #{vo.type}
    </select>

    <select id="queryTag" resultType="com.zqn.sop.entity.PccMeProjectPlanActions">
        select tag
        from PCC_ME_PROJECT_PLAN_ACTIONS
        where type = #{type}
          and tag is not null
        group by tag
    </select>
</mapper>