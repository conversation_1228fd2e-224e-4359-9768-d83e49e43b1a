<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zqn.modeldata2.mapper.ProcessMapper">
    <resultMap id="ResultMap" type="java.util.HashMap">
        <result property="brand_no" column="BRAND_NO" jdbcType="VARCHAR"/>
        <result property="model_no" column="MODEL_NO" jdbcType="VARCHAR"/>
        <result property="model_name" column="MODEL_NAME" jdbcType="VARCHAR"/>
        <result property="model_desc" column="MODEL_DESC" jdbcType="VARCHAR"/>
        <result property="last_nos" column="LAST_NOS" jdbcType="VARCHAR"/>
        <result property="model_pic" column="MODEL_PIC" jdbcType="BLOB"
                typeHandler="org.apache.ibatis.type.BlobTypeHandler"/>
    </resultMap>

    <!-- 品牌客户 -->
    <select id="getBrand" resultType="string">
        select a.brand_no
        from be_brand a,
             sy_userbrand b
        where a.brand_no = b.brand_no(+)
          and b.user_no(+) = '00006'
          and (
            bitand(16, 16) > 0 and
            bitand(a.made_factory, 128 + 256) > 0 or
            bitand(a.dev_factory + a.made_factory * 4, 1024) > 0
            )
        order by a.brand_no
    </select>

    <!-- 型体编号 -->
    <select id="getModel" resultType="string">
        select a.model_no
        from ck_smodel a, cb_model b, bf_last c
        where a.model_no = b.model_no(+)
        and b.last_seq = c.last_seq(+)
        <if test="brand_no.length() > 0">
            and a.brand_no = #{brand_no}
        </if>
        order by a.model_no
    </select>

    <!-- 制程：1-加工、2-裁断、3-准备、4-针车、5-半成品、6-成型 -->
    <select id="getOperation" resultType="integer">
        select distinct(operation)
        from ma_rtgc_pub
        order by operation
    </select>

    <!-- 楦头编号 -->
    <select id="getLast" resultMap="ResultMap">
        select a.brand_no,
               nvl(c.last_nos, (select z.last_nos
                                from ck_smodel x,
                                     be_moudel y,
                                     bf_last z
                                where x.module_no = y.module_no
                                  and y.last_seq = z.last_seq(+)
                                  and x.model_no = a.model_no)
               ) last_nos
        from ck_smodel a,
             cb_model b,
             bf_last c
        where a.model_no = b.model_no(+)
          and b.last_seq = c.last_seq(+)
          and a.model_no = #{model_no}
    </select>

    <!-- 主要代码 -->
    <select id="getCode" resultType="string">
        select nvl2(max(rtg_code), to_char(max(rtg_code) + 1, 'fm00'), '01') as rtg_code
        from ma_rtgc_pub
        where model_no = #{model_no}
          and operation = #{operation}
    </select>

    <!-- 鞋图 -->
    <select id="getPicture" resultMap="ResultMap">
        select model_pic
        from ck_smodel
        where model_no = #{model_no}
    </select>

    <!-- 生产类型 -->
    <select id="getType" resultType="string">
        select column_no
        from sy_xcodeval
        <if test="operation != 5">
            where code_no = 'MC002'
            order by instr('小型線, 中型線, 大型線', column_no)
        </if>
        <if test="operation == 5">
            where code_no = 'MC005'
            order by instr('半成品-層皮, 半成品-單底, 半成品-包跟, 半成品-粘跟, 半成品-貼合磨臺, 半成品-包中底, 半成品-中底皮,
            半成品-照射', column_no)
        </if>
    </select>

    <!-- 材质 -->
    <select id="getMaterial" resultType="string">
        select column_no
        from sy_xcodeval
        where code_no = 'MC003'
    </select>

    <!-- 工序信息 -->
    <select id="getProcessInfo" resultType="com.zqn.modeldata2.entity.ProcessInfo">
        select model_no, operation, rtg_code, rtg_type, material
        from ma_rtgc_pub
        where model_no = #{processInfo.model_no}
          and operation = #{processInfo.operation}
          and revision = '預估版0'
    </select>

    <!-- 添加工序信息 -->
    <insert id="addProcessInfo">
        insert into ma_rtgc_pub (model_no,
                                 operation,
                                 rtg_code,
                                 rtg_type,
                                 material,
                                 revision,
                                 allow,
                                 ins_user,
                                 ins_date,
                                 upd_user,
                                 upd_date)
        values (#{processInfo.model_no},
                #{processInfo.operation},
                #{processInfo.rtg_code},
                #{processInfo.rtg_type},
                #{processInfo.material},
                '預估版0',
                0.06,
                #{processInfo.ins_user},
                sysdate,
                #{processInfo.upd_user},
                sysdate)
    </insert>

    <!-- 修改工序信息 -->
    <update id="updateProcessInfo">
        update ma_rtgc_pub
        set rtg_type = #{processInfo.rtg_type},
            material = #{processInfo.material},
            upd_user = #{processInfo.upd_user},
            upd_date = sysdate
        where model_no = #{processInfo.model_no}
          and operation = #{processInfo.operation}
          and rtg_code = #{processInfo.rtg_code}
    </update>

    <!-- 删除工序信息 -->
    <delete id="deleteProcessInfo">
        delete
        from ma_rtgc_pub
        where model_no = #{processInfo.model_no}
          and operation = #{processInfo.operation}
          and rtg_code = #{processInfo.rtg_code}
    </delete>

    <!-- 批量删除工序信息 -->
    <delete id="batchDeleteProcessInfo">
        delete from ma_rtgc_pub
        where
        <foreach collection="processInfoList" item="processInfo" separator="or">
            (
            model_no = #{processInfo.model_no}
            and operation = #{processInfo.operation}
            and rtg_code = #{processInfo.rtg_code}
            )
        </foreach>
    </delete>

    <!-- 工序编号 -->
    <select id="getNo" resultType="string">
        select nvl2(max(seq_no), to_char(max(seq_no) + 1, 'fm000'), '001') as seq_no
        from ma_rtgd_pub
        where model_no = #{processStep.model_no}
          and operation = #{processStep.operation}
          and rtg_code = #{processStep.rtg_code}
    </select>

    <!-- 排序号 -->
    <select id="getKey" resultType="string">
        select nvl2(max(rownum), max(rownum) + 1, 1) as skey
        from ma_rtgd_pub
        where model_no = #{processStep.model_no}
          and operation = #{processStep.operation}
          and rtg_code = #{processStep.rtg_code}
    </select>

    <!-- 模板 -->
    <select id="getTemplate" resultType="com.zqn.modeldata2.entity.ProcessTemplate">
        select doc_no, pro_seq, type_desc, material
        from ma_seqdat
        where operation = #{operation}
    </select>

    <!-- 模板工序步骤 -->
    <select id="getTemplateStep" resultType="com.zqn.modeldata2.entity.ProcessStep">
        select seq_no, rownum as skey, wk_group, seq_name
        from ma_seqdatb
        where doc_no = #{doc_no}
    </select>

    <!-- 模板导入 -->
    <insert id="templateImport">
        insert all
        <foreach collection="processStepList" item="processStep" separator=" ">
            into ma_rtgd_pub (
            model_no,
            operation,
            rtg_code,
            seq_no,
            skey,
            wk_group,
            seq_name,
            ins_user,
            ins_date,
            upd_user,
            upd_date
            ) values (
            #{processStep.model_no},
            #{processStep.operation},
            #{processStep.rtg_code},
            #{processStep.seq_no},
            #{processStep.skey},
            #{processStep.wk_group},
            #{processStep.seq_name},
            #{processStep.ins_user},
            sysdate,
            #{processStep.upd_user},
            sysdate
            )
        </foreach>
        select * from dual
    </insert>

    <!-- 工序步骤 -->
    <select id="getProcessStep" resultType="com.zqn.modeldata2.entity.ProcessStep">
        select model_no,
               operation,
               rtg_code,
               seq_no,
               skey,
               wk_group,
               seq_name,
               remark
        from ma_rtgd_pub
        where model_no = #{processStep.model_no}
          and operation = #{processStep.operation}
          and rtg_code = #{processStep.rtg_code}
        order by wk_group, skey, seq_no
    </select>

    <!-- 添加工序步骤 -->
    <insert id="addProcessStep">
        insert into ma_rtgd_pub (model_no,
                                 operation,
                                 rtg_code,
                                 seq_no,
                                 skey,
                                 wk_group,
                                 seq_name,
                                 remark,
                                 ins_user,
                                 ins_date,
                                 upd_user,
                                 upd_date)
        values (#{processStep.model_no},
                #{processStep.operation},
                #{processStep.rtg_code},
                #{processStep.seq_no},
                #{processStep.skey},
                #{processStep.wk_group},
                #{processStep.seq_name},
                #{processStep.remark},
                #{processStep.ins_user},
                sysdate,
                #{processStep.upd_user},
                sysdate)
    </insert>

    <!-- 修改工序步骤 -->
    <update id="updateProcessStep">
        update ma_rtgd_pub
        set skey     = #{processStep.skey},
            wk_group = #{processStep.wk_group},
            seq_name = #{processStep.seq_name},
            remark   = #{processStep.remark},
            upd_user = #{processStep.upd_user},
            upd_date = sysdate
        where model_no = #{processStep.model_no}
          and operation = #{processStep.operation}
          and rtg_code = #{processStep.rtg_code}
          and seq_no = #{processStep.seq_no}
    </update>

    <!-- 删除工序步骤 -->
    <delete id="deleteProcessStep">
        delete
        from ma_rtgd_pub
        where model_no = #{processStep.model_no}
          and operation = #{processStep.operation}
          and rtg_code = #{processStep.rtg_code}
          and seq_no = #{processStep.seq_no}
    </delete>

    <!-- 批量删除工序步骤 -->
    <delete id="batchDeleteProcessStep">
        delete from ma_rtgd_pub
        where
        <foreach collection="processStepList" item="processStep" separator="or">
            (
            model_no = #{processStep.model_no}
            and operation = #{processStep.operation}
            and rtg_code = #{processStep.rtg_code}
            and seq_no = #{processStep.seq_no}
            )
        </foreach>
    </delete>

    <!-- 型体列表 -->
    <select id="getModelList" resultType="string">
        select a.model_no
        from ck_smodel a,
             cb_model b,
             bf_last c
        where a.model_no = b.model_no(+)
          and b.last_seq = c.last_seq(+)
        order by a.model_no
    </select>

    <!-- 复制工序信息 -->
    <insert id="copyProcessInfo">
        insert into ma_rtgc_pub (model_no, operation, rtg_code, rtg_type, material,
                                 ins_user, ins_date, upd_user, upd_date)
        select #{copyProcess.targetModel} as model_no,
               operation,
               rtg_code,
               rtg_type,
               material,
               #{copyProcess.insUser}     as ins_user,
               sysdate                    as ins_date,
               #{copyProcess.updUser}     as upd_user,
               sysdate                    as upd_date
        from ma_rtgc_pub
        where model_no = #{copyProcess.originalModel}
          and operation = #{copyProcess.operation}
    </insert>

    <!-- 复制工序步骤 -->
    <insert id="copyProcessStep">
        insert into ma_rtgd_pub (model_no, operation, rtg_code, seq_no, skey, wk_group, seq_name, remark,
                                 ins_user, ins_date, upd_user, upd_date)
        select #{copyProcess.targetModel} as model_no,
               operation,
               rtg_code,
               seq_no,
               skey,
               wk_group,
               seq_name,
               remark,
               #{copyProcess.insUser}     as ins_user,
               sysdate                    as ins_date,
               #{copyProcess.updUser}     as upd_user,
               sysdate                    as upd_date
        from ma_rtgd_pub
        where model_no = #{copyProcess.originalModel}
          and operation = #{copyProcess.operation}
    </insert>
</mapper>