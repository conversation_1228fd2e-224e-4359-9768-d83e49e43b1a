package com.zqn.prodctrl.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.zqn.modeldata2.common.R;
import com.zqn.prodctrl.entity.PendingProd;
import com.zqn.prodctrl.entity.SchedProd;
import com.zqn.prodctrl.service.PendingProdService;
import com.zqn.prodctrl.service.SchedProdService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:新型体指令单
 * @date 2024/8/6 8:02
 */
@RestController
@RequestMapping("/pending")
public class PendingProdController {

    @Resource
    private PendingProdService pendingProdService;

    @GetMapping("/query")
    public R<PageInfo<PendingProd>> query(@RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                          @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
                                          @RequestParam(value = "startTime", required = false) long startTimeTamp,
                                          @RequestParam(value = "endTime", required = false) long endTimeTamp,
                                          @RequestParam(value = "brand", required = false) String brand,
                                          @RequestParam(value = "ordNo", required = false) String ordNo,
                                          @RequestParam(value = "devType", required = false) String devType,
                                           @RequestParam(value = "phase", required = false)String phase,
                                          @RequestParam(value = "pdLine", required = false)String pdLine
    ) {
        Date startTime = new Date(startTimeTamp); // 将时间戳转换为Date对象
        Date endTime = new Date(endTimeTamp); // 将时间戳转换为Date对象
        PageInfo<PendingProd> pendingProdPageInfo = pendingProdService.query(pageNo, pageSize, startTime, endTime, brand, ordNo, devType,phase,pdLine);
        return R.success(pendingProdPageInfo);
    }

    /**
     * @description: 查询所有样品类型
     * @param:
     * @return: com.zqn.modeldata2.common.R<java.util.List < com.alibaba.fastjson.JSONObject>>
     * <AUTHOR> Yang
     * @date: 2024/8/21 14:27
     */
    @GetMapping("/queryAllDevType")
    public R<List<JSONObject>> queryAllDevType() {
        List<JSONObject> result = pendingProdService.queryAllDevType();
        return R.success(result);
    }

    @PostMapping("/edit")
    public R<String> edit(@RequestBody PendingProd pendingProd) throws Exception {
        String result = pendingProdService.edit(pendingProd);
        return R.success(result);
    }
}
