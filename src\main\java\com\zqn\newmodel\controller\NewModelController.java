package com.zqn.newmodel.controller;

import com.zqn.modeldata2.common.R;
import com.zqn.newmodel.entity.NewModel;
import com.zqn.newmodel.service.NewModelService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:新型体指令单
 * @date 2024/8/6 8:02
 */
@RestController
@RequestMapping("/newmodel")
public class NewModelController {

    @Resource
    private NewModelService newModelService;

    @GetMapping("/query")
    public R<NewModel> query(@RequestParam("ordNo") String ordNo) {
        NewModel newModel = newModelService.query(ordNo);
        return R.success(newModel);
    }
}
