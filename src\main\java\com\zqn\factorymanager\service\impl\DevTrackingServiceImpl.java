package com.zqn.factorymanager.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zqn.factorymanager.entity.DevTracking;
import com.zqn.factorymanager.mapper.DevTrackingMapper;
import com.zqn.factorymanager.service.DevTrackingService;
import com.zqn.modeldata2.common.R;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/11/15 10:36
 */
@Service
public class DevTrackingServiceImpl implements DevTrackingService {

    @Resource
    private DevTrackingMapper devTrackingMapper;

    @Override
    public R<PageInfo<DevTracking>> query(int pageNo, int pageSize, Date startTime, Date endTime, Integer type) {
        PageHelper.startPage(pageNo, pageSize);
        List<DevTracking> list = new ArrayList<>();
        if (type == 1) {
            list = devTrackingMapper.queryTop(startTime, endTime);
        } else if (type == 2) {
            list = devTrackingMapper.queryBottom(startTime, endTime);
        } else if (type == 3) {
            list = devTrackingMapper.queryWarnTop(startTime, endTime);
        } else if (type == 4) {
            list = devTrackingMapper.queryWarnBottom(startTime, endTime);
        }
        PageInfo<DevTracking> pageInfo = new PageInfo<>(list);
        return R.success(pageInfo);
    }

    @Override
    public R<Void> updateBrandFn(DevTracking devTracking) {
        int result = devTrackingMapper.updateBrandFn(devTracking);
        return result > 0 ? R.success(null) : R.error("更新A级师傅失败");
    }
}
