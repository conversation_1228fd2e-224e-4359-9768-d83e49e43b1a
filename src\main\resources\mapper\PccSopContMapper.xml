<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zqn.sop.mapper.PccSopContMapper">

    <insert id="add" parameterType="com.zqn.sop.entity.PccSopCont" useGeneratedKeys="true"
            keyProperty="id" keyColumn="ID">
        insert into PCC_SOP_CONT (PARENT_ID, CONTENT, LANG, TYPE, ACTION_ID)
        VALUES (#{cont.parent_id},
                #{cont.content},
                #{cont.lang},
                #{cont.type},
                #{cont.action_id})
    </insert>

    <select id="query" resultType="com.zqn.sop.entity.PccSopCont">
        select *
        from PCC_SOP_CONT
        where parent_id = #{parentId}
    </select>

    <select id="queryContByAction" resultType="com.zqn.sop.entity.PccSopCont">
        select content,lang,type from PCC_SOP_CONT where ACTION_ID in
        <foreach item="id" index="index" collection="idList" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>