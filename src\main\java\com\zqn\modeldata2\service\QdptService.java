package com.zqn.modeldata2.service;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.entity.Qdpt;
import com.zqn.modeldata2.entity.QdptDt;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

public interface QdptService {

    R<PageInfo<Qdpt>> query(int pageNo, int pageSize, Date startTime, Date endTime, String brand, String sampleType, Integer fileType, Integer cutComplType,String sortType);

    Integer update(Qdpt qdpt) throws Exception;

    List<JSONObject> queryAllDevType();

    List<QdptDt> queryDetailTableData(String ordNo);

    String manualClose(Qdpt qdpt);

    List<Qdpt> queryLog();

    void insertLog(String ordNo, String itemNo);

    Integer batchUpdate(List<Qdpt> qdpts) throws Exception;

    BigDecimal findTouRuLogTotQtyCount(List<String> orderNos);




}