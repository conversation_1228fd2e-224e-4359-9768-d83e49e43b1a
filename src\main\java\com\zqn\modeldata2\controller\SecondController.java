package com.zqn.modeldata2.controller;

import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.entity.CkSmodelp;
import com.zqn.modeldata2.entity.CkSmodelpPlus;
import com.zqn.modeldata2.service.SecondService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/second")
public class SecondController {
    @Autowired
    private SecondService secondService;

    @PostMapping("/getSpecification")
    public R<List<CkSmodelp>> getSpecification(@RequestBody CkSmodelp ckSmodelp) {
        List<CkSmodelp> result = secondService.getSpecification(ckSmodelp);
        return R.success(result);
    }

    @PostMapping("/getSpecificationPlus")
    public R<List<CkSmodelpPlus>> getSpecificationPlus(@RequestBody CkSmodelp ckSmodelp) {
        List<CkSmodelpPlus> result = secondService.getSpecificationPlus(ckSmodelp);
        return R.success(result);
    }

    @PostMapping("/addSpecification")
    public R<String> addSpecification(@RequestBody CkSmodelp ckSmodelp) {
        int result = secondService.addSpecification(ckSmodelp);
        if (result < 0) {
            return R.error("规格数据添加失败！");
        }
        return R.success("规格数据添加成功！");
    }

    @PostMapping("/updateSpecification")
    public R<String> updateSpecification(@RequestBody CkSmodelp ckSmodelp) {
        int result = secondService.updateSpecification(ckSmodelp);
        if (result < 0) {
            return R.error("规格数据修改失败！");
        }
        return R.success("规格数据修改成功！");
    }

    @PostMapping("/deleteSpecification")
    public R<String> deleteSpecification(@RequestBody CkSmodelp ckSmodelp) {
        int result = secondService.deleteSpecification(ckSmodelp);
        if (result < 0) {
            return R.error("规格数据删除失败！");
        }
        return R.success("规格数据删除成功！");
    }

    @PostMapping("/batchDeleteSpecification")
    public R<String> batchDeleteSpecification(@RequestBody List<CkSmodelp> ckSmodelpList) {
        int result = secondService.batchDeleteSpecification(ckSmodelpList);
        if (result < 0) {
            return R.error("规格数据删除失败！");
        }
        return R.success("规格数据删除成功！");
    }

    @PostMapping("/updateStartSize")
    public R<String> updateStartSize(@RequestBody CkSmodelp ckSmodelp) {
        Integer result = secondService.updateStartSize(ckSmodelp);
        if (result < 0) {
            return R.error("起始号码修改失败！");
        }
        return R.success("起始号码修改成功！");
    }

    @PostMapping("/updateEndSize")
    public R<String> updateEndSize(@RequestBody CkSmodelp ckSmodelp) {
        Integer result = secondService.updateEndSize(ckSmodelp);
        if (result < 0) {
            return R.error("终止号码修改失败！");
        }
        return R.success("终止号码修改成功！");
    }
}
