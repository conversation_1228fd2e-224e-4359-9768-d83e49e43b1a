<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zqn.modeldata2.mapper.QdptMapper">

    <select id="query" resultType="com.zqn.modeldata2.entity.Qdpt">
        SELECT ORD_SEQ, shp_date, brand_no, dev_type, ord_no, item_no, model_pic, dutyer, model_no, last_no, wo_date,
        tot_qty,
        upp_flag, sole_flag, t1_flag, t2_flag, t3_flag, c3_qty, c4_qty, c_f_status, u_f_status, cin_flag, cin_date,
        cin_area,
        (CASE WHEN upp_flag = 'OK' THEN 1 ELSE 0 END +
        CASE WHEN sole_flag = 'OK' THEN 1 ELSE 0 END +
        CASE WHEN t1_flag = 'OK' THEN 1 ELSE 0 END +
        CASE WHEN t2_flag = 'OK' THEN 1 ELSE 0 END +
        CASE WHEN t3_flag = 'OK' THEN 1 ELSE 0 END) as ok_count,
        (CASE WHEN c_f_status IS NOT NULL THEN 1 ELSE 0 END) AS c_f_status_exists,
        (CASE WHEN u_f_status IS NOT NULL THEN 1 ELSE 0 END) AS u_f_status_exists
        FROM VW_MK2_C_FRONTFITTING
        <where>
            <if test="brand != null and brand != ''">
                AND UPPER(brand_no) = UPPER(#{brand})
            </if>
            <if test="devType != null and devType != '' and devType != 'select'">
                AND dev_type = #{devType}
            </if>
            <if test="startTime != null and endTime != null">
                and shp_date between #{startTime} and #{endTime}
            </if>
            <if test="fileType != null and fileType == 1">
                and c_f_status is not null and u_f_status is not null
            </if>
            <if test="fileType != null and fileType == 2">
                and upp_flag = 'OK'
                and sole_flag = 'OK'
                and t1_flag = 'OK'
                and t2_flag = 'OK'
                and t3_flag = 'OK'
            </if>
            <if test="fileType != null and fileType == 3">
                and upp_flag = 'OK'
                and sole_flag = 'OK'
                and t1_flag = 'OK'
                and t2_flag is null
                and t3_flag = 'OK'
            </if>
            <if test="fileType != null and fileType == 4">
                and upp_flag = 'OK'
                and sole_flag = 'OK'
                and t1_flag = 'OK'
                and t2_flag is null
                and t3_flag is null
            </if>
            <if test="cutComplType != null and cutComplType == 1">
                and c3_qty = 'OK'
                and c4_qty = 'OK'
                and t2_flag = 'OK'
                and t3_flag = 'OK'
            </if>
            <if test="cutComplType != null and cutComplType == 2">
                and (c3_qty is null
                or c4_qty is null
                or t2_flag is null
                or t3_flag is null)
            </if>
        </where>
        and (cin_flag is null or cin_flag = 'N')
        ORDER BY
        <choose>
            <when test="sortType=='asc'">
                shp_date asc
            </when>
            <otherwise>
                shp_date desc
            </otherwise>
        </choose>
    </select>

    <select id="queryAllDevType" resultType="java.lang.String">
        select dev_type
        FROM VW_MK2_C_FRONTFITTING
        group by dev_type
    </select>

    <!--   排入生产
    当用户点击投入按钮时-->
    <update id="update">
        UPDATE gc_sorders
        SET cin_flag = DECODE(NVL(cin_flag, 'N'), 'N', 'Y', 'N'),
            cin_date = sysdate,
            cin_area = '' --此处用户说先不用赋值，后续会用
        WHERE ord_no = #{ordNo}
          and item_no = #{itemNo}
    </update>

    <select id="queryDetailTableData" resultType="com.zqn.modeldata2.entity.QdptDt">
        --ord_no 樣品單號,remark 序號, bar_date 條碼日期,semi_su 製程部位,key_flag 必掃註記,
        --made_dept 製作組別,emp_name  製作人員 ,bar_qty 條碼雙數 ,ins_user 建立人,ins_date 建立日期
        SELECT ORD_NO,
               REMARK,
               BAR_DATE,
               SEMI_SU,
               KEY_FLAG,
               MADE_DEPT,
               EMP_NAME,
               BAR_QTY,
               INS_USER,
               INS_DATE
        FROM VW_MK2_ORDBARS t
        WHERE ORD_NO = #{ordNo}
          AND SEMI_NO IN ('C', '1', '2', '3')
    </select>

    <select id="selectManualClose" resultType="com.zqn.modeldata2.entity.Qdpt">
        SELECT ORD_NO, ITEM_NO, SHP_DATE, C_FLAG, C_date
        FROM gc_sorders
        WHERE ord_no = #{ordNo}
    </select>

    <select id="manualClose" statementType="CALLABLE" resultType="string">
        {call pd_mk_close_orders_c(
                #{ord_no, mode=IN, jdbcType=VARCHAR},
                #{item_no, mode=IN, jdbcType=VARCHAR},
                #{o_return, mode=OUT, jdbcType=VARCHAR}
              )}
    </select>

    <select id="queryTouRuLogTotQtyCount" resultType="java.math.BigDecimal">
        SELECT sum(tot_qty)
        FROM vw_mk2_c_pschedule
        WHERE ord_no IN
              (select ord_no
               from gc_sorders
               WHERE TRUNC(cin_date) = TRUNC(SYSDATE))
    </select>


    <select id="queryTotQtyCount" resultType="java.math.BigDecimal">
        SELECT sum(tot_qty)
        FROM VW_MK2_C_FRONTFITTING
        <where>
            <if test="brand != null and brand != ''">
                AND UPPER(brand_no) = UPPER(#{brand})
            </if>
            <if test="devType != null and devType != '' and devType != 'select'">
                AND dev_type = #{devType}
            </if>
            <if test="startTime != null and endTime != null">
                and shp_date between #{startTime} and #{endTime}
            </if>
            <if test="fileType != null and fileType == 1">
                and c_f_status is not null and u_f_status is not null
            </if>
            <if test="fileType != null and fileType == 2">
                and upp_flag = 'OK'
                and sole_flag = 'OK'
                and t1_flag = 'OK'
                and t2_flag = 'OK'
                and t3_flag = 'OK'
            </if>
            <if test="fileType != null and fileType == 3">
                and upp_flag = 'OK'
                and sole_flag = 'OK'
                and t1_flag = 'OK'
                and t2_flag is null
                and t3_flag = 'OK'
            </if>
            <if test="fileType != null and fileType == 4">
                and upp_flag = 'OK'
                and sole_flag = 'OK'
                and t1_flag = 'OK'
                and t2_flag is null
                and t3_flag is null
            </if>
            <if test="cutComplType != null and cutComplType == 1">
                and c3_qty = 'OK'
                and c4_qty = 'OK'
                and t2_flag = 'OK'
                and t3_flag = 'OK'
            </if>
            <if test="cutComplType != null and cutComplType == 2">
                and (c3_qty is null
                or c4_qty is null
                or t2_flag is null
                or t3_flag is null)
            </if>
        </where>
        and (cin_flag is null or cin_flag = 'N')
    </select>

    <insert id="insertLog">
        INSERT INTO pcc_qdpt_log(ord_no, item_no, created_date)
        VALUES (#{ordNo}, #{itemNo}, sysdate)
    </insert>

    <select id="queryLog" resultType="com.zqn.modeldata2.entity.Qdpt">
        select ord_no, created_date, item_no
        from pcc_qdpt_log
        WHERE created_date >= TRUNC(SYSDATE)
        order by created_date
    </select>
</mapper>