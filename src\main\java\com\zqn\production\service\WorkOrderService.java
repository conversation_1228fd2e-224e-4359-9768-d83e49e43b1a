package com.zqn.production.service;

import com.zqn.modeldata2.common.R;
import com.zqn.production.entity.WorkOrder;
import com.zqn.production.entity.WorkOrderGroup;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 工单服务接口
 */
public interface WorkOrderService {
    
    /**
     * 获取组别列表
     * @param deptNo 部门编号
     * @param date 日期
     * @return 组别列表
     */
    R<List<WorkOrderGroup>> getGroupListWithDate(String deptNo, Date date);
    
    /**
     * 获取组别列表（不带日期参数，保留向后兼容）
     * @param deptNo 部门编号
     * @return 组别列表
     */
    R<List<WorkOrderGroup>> getGroupList(String deptNo);
    
    /**
     * 获取工单列表
     * @param madeDept 制作部门
     * @param date 日期
     * @return 工单列表
     */
    R<List<WorkOrder>> getOrderList(String madeDept, Date date);
    
    /**
     * 更新工单选择状态
     * @param ordNo 工单号
     * @param seFlag 选择标志
     * @param ordQty 工单数量
     * @return 操作结果
     */
    R<Void> updateOrderSelection(String ordNo, String seFlag, String ordQty);
    
    /**
     * 更新组别目标PPH
     * @param deptNo 部门编号
     * @param madeDept 制作部门
     * @param runRate 目标PPH
     * @return 操作结果
     */
    R<Void> updateGroupTarget(String deptNo, String madeDept, String runRate, String date);
} 