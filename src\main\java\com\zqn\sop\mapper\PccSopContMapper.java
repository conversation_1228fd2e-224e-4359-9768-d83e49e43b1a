package com.zqn.sop.mapper;

import com.zqn.sop.entity.PccSopCont;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PccSopContMapper {

    int add(@Param("cont") PccSopCont PccSopCont);

    List<PccSopCont> query(@Param("parentId") Integer parentId);

    List<PccSopCont> queryContByAction(@Param("idList") List<String> idList);
}