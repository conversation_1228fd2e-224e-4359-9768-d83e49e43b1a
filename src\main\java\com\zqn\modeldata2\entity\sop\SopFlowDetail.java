package com.zqn.modeldata2.entity.sop;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 工序流程详情
 */
@Data
public class SopFlowDetail {
    // 型体
    private String model;
    // 制程
    private String operation;
    // 主要代码
    private String rtgCode;
    // 工序编号
    private String seqNo;
    // 序号
    private int skey;
    // 加工段
    private String wkGroup;
    // 工序名称
    private String seqName;
    // 动作
    private String actions;
    // 图片备注
    private String imgTit1;
    // 图片列表
    private List<SopFlowPicture> imgList;
    // 操作标准 ID
    private long standardId;
    // 操作标准
    private String standard;
    // 自检点 ID
    private long checkPointId;
    // 自检点
    private String checkPoint;
    // 工具
    private String tools;
    // 机器
    private String machine;
    // 边距
    private String margin;
    // 温度
    private String temp;
    // 压力
    private String pressure;
    // 胶水
    private String glue;
    // 车线
    private String carLine;
    // 化学品
    private String chemical;
    // 针距
    private String needleSpacing;
    // 间距
    private String spacing;
    // 车针
    private String needle;
    // 时间
    private String time;
    // 防护用品
    private String defence;
    // 备注
    private String remark;
    // 版本
    private int version;
    // 添加者
    private String insUser;
    // 添加时间
    private Date insDate;
    // 修改者
    private String updUser;
    // 修改时间
    private Date updDate;
    // 加工选项
    private String processOption1;
    private String processOption2;
    private String processOption3;
    private String processOption4;
    private String processOption5;
    private String processOption6;
    private String processOption7;
    private String processOption8;
    private String processOption9;
    private String processOption10;
}
