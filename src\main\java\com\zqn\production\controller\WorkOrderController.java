package com.zqn.production.controller;

import com.zqn.modeldata2.common.R;
import com.zqn.production.entity.WorkOrder;
import com.zqn.production.entity.WorkOrderGroup;
import com.zqn.production.service.WorkOrderService;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 工单控制器
 */
@RestController
@RequestMapping("/workorder")
public class WorkOrderController {
    
    @Resource
    private WorkOrderService workOrderService;
    
    /**
     * 获取组别列表
     * @param deptNo 部门编号
     * @param date 日期
     * @return 组别列表
     */
    @GetMapping("/groups")
    public R<List<WorkOrderGroup>> getGroupList(
            @RequestParam String deptNo,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
        if (date != null) {
            return workOrderService.getGroupListWithDate(deptNo, date);
        } else {
            return workOrderService.getGroupList(deptNo);
        }
    }
    
    /**
     * 获取工单列表
     * @param madeDept 制作部门
     * @param date 日期
     * @return 工单列表
     */
    @GetMapping("/orders")
    public R<List<WorkOrder>> getOrderList(
            @RequestParam(value = "madeDept", required = false ) String madeDept,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date date) {
        return workOrderService.getOrderList(madeDept, date);
    }
    
    /**
     * 更新工单选择状态
     * @param params 参数
     * @return 操作结果
     */
    @PostMapping("/selection")
    public R<Void> updateOrderSelection(@RequestBody OrderSelectionParam params) {
        return workOrderService.updateOrderSelection(
                params.getOrdNo(), 
                params.getSeFlag(), 
                params.getOrdQty());
    }
    
    /**
     * 更新组别目标PPH
     * @param params 参数
     * @return 操作结果
     */
    @PostMapping("/target")
    public R<Void> updateGroupTarget(@RequestBody GroupTargetParam params) {
        return workOrderService.updateGroupTarget(
                params.getDeptNo(), 
                params.getMadeDept(), 
                params.getRunRate(),
                params.getDate());
    }
    
    /**
     * 工单选择参数
     */
    static class OrderSelectionParam {
        private String ordNo;
        private String seFlag;
        private String ordQty;
        
        public String getOrdNo() {
            return ordNo;
        }
        
        public void setOrdNo(String ordNo) {
            this.ordNo = ordNo;
        }
        
        public String getSeFlag() {
            return seFlag;
        }
        
        public void setSeFlag(String seFlag) {
            this.seFlag = seFlag;
        }
        
        public String getOrdQty() {
            return ordQty;
        }
        
        public void setOrdQty(String ordQty) {
            this.ordQty = ordQty;
        }
    }
    
    /**
     * 组别目标参数
     */
    static class GroupTargetParam {
        private String deptNo;
        private String madeDept;
        private String runRate;

        private String date;

        public String getDeptNo() {
            return deptNo;
        }
        
        public void setDeptNo(String deptNo) {
            this.deptNo = deptNo;
        }
        
        public String getMadeDept() {
            return madeDept;
        }
        
        public void setMadeDept(String madeDept) {
            this.madeDept = madeDept;
        }
        
        public String getRunRate() {
            return runRate;
        }
        
        public void setRunRate(String runRate) {
            this.runRate = runRate;
        }

        public String getDate() {
            return date;
        }

        public void setDate(String date) {
            this.date = date;
        }
    }
} 