package com.zqn.studentrecord.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.toolkit.DynamicDataSourceContextHolder;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zqn.modeldata2.common.R;
import com.zqn.studentrecord.entity.StudentRecord;
import com.zqn.studentrecord.mapper.StudentRecordMapper;
import com.zqn.studentrecord.service.StudentRecordService;
import com.zqn.studentrecord.service.StudentRecordService.PageResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 学员记录服务实现类
 */
@Service
public class StudentRecordServiceImpl implements StudentRecordService {
    
    @Resource
    private StudentRecordMapper studentRecordMapper;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    @Transactional
    @DS("app")
    public R<StudentRecord> createRecord(StudentRecord record) {
        try {
            // 参数验证
            if (record.getRecordDate() == null) {
                return R.error("记录日期不能为空");
            }
            if (!StringUtils.hasText(record.getOrderNo())) {
                return R.error("单号不能为空");
            }
            
            // 获取下一个序列值作为ID
            Long nextId = studentRecordMapper.getNextSequenceValue();
            record.setId(nextId);
            
            // 设置创建和更新时间
            Date now = new Date();
            record.setCreateTime(now);
            record.setUpdateTime(now);
            record.setStatus(1);
            
            // 转换图片列表为JSON字符串
            convertListsToJson(record);
            
            // 转换Boolean为Integer
            convertBooleanToInt(record);
            
            // 插入记录
            int result = studentRecordMapper.insert(record);
            if (result > 0) {
                // 查询并返回完整记录
                StudentRecord savedRecord = studentRecordMapper.selectById(nextId);
                if (savedRecord != null) {
                    // 转换JSON字符串为List
                    convertJsonToLists(savedRecord);
                    // 转换Integer为Boolean
                    convertIntToBoolean(savedRecord);
                    return R.success(savedRecord);
                }
                return R.error("保存成功但查询失败");
            } else {
                return R.error("保存失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return R.error("创建学员记录失败：" + e.getMessage());
        }
    }
    
    @Override
    @DS("app")
    public R<StudentRecord> getRecordById(Long id) {
        try {
            if (id == null) {
                return R.error("记录ID不能为空");
            }
            
            StudentRecord record = studentRecordMapper.selectById(id);
            if (record == null) {
                return R.error("记录不存在");
            }
            
            // 转换JSON字符串为List
            convertJsonToLists(record);
            // 转换Integer为Boolean
            convertIntToBoolean(record);
            
            // 获取订单数量和型体信息
            if (record.getOrderNo() != null) {
                List<String> orderNos = new ArrayList<>();
                orderNos.add(record.getOrderNo());
                
                DynamicDataSourceContextHolder.push("master");
                List<StudentRecord> orderInfos = studentRecordMapper.getOrderInfoByOrderNos(orderNos);
                DynamicDataSourceContextHolder.poll();
                
                if (!orderInfos.isEmpty()) {
                    StudentRecord orderInfo = orderInfos.get(0);
                    record.setOrdQty(orderInfo.getOrdQty());
                    record.setModelNo(orderInfo.getModelNo());
                }
            }
            
            return R.success(record);
        } catch (Exception e) {
            e.printStackTrace();
            return R.error("获取学员记录失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional
    @DS("app")
    public R<StudentRecord> updateRecord(Long id, StudentRecord record) {
        try {
            if (id == null) {
                return R.error("记录ID不能为空");
            }
            
            // 参数验证
            if (record.getRecordDate() == null) {
                return R.error("记录日期不能为空");
            }
            if (!StringUtils.hasText(record.getOrderNo())) {
                return R.error("单号不能为空");
            }
            
            // 检查记录是否存在
            StudentRecord existingRecord = studentRecordMapper.selectById(id);
            if (existingRecord == null) {
                return R.error("记录不存在");
            }
            
            // 设置ID和更新时间
            record.setId(id);
            record.setUpdateTime(new Date());
            
            // 转换图片列表为JSON字符串
            convertListsToJson(record);
            
            // 转换Boolean为Integer
            convertBooleanToInt(record);
            
            // 更新记录
            int result = studentRecordMapper.updateById(record);
            if (result > 0) {
                // 查询并返回更新后的记录
                StudentRecord updatedRecord = studentRecordMapper.selectById(id);
                if (updatedRecord != null) {
                    // 转换JSON字符串为List
                    convertJsonToLists(updatedRecord);
                    // 转换Integer为Boolean
                    convertIntToBoolean(updatedRecord);
                    return R.success(updatedRecord);
                }
                return R.error("更新成功但查询失败");
            } else {
                return R.error("更新失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return R.error("更新学员记录失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional
    @DS("app")
    public R<Void> deleteRecord(Long id) {
        try {
            if (id == null) {
                return R.error("记录ID不能为空");
            }
            
            // 检查记录是否存在
            StudentRecord existingRecord = studentRecordMapper.selectById(id);
            if (existingRecord == null) {
                return R.error("记录不存在");
            }
            
            // 逻辑删除
            int result = studentRecordMapper.deleteById(id);
            if (result > 0) {
                return R.success(null);
            } else {
                return R.error("删除失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return R.error("删除学员记录失败：" + e.getMessage());
        }
    }
    
    @Override
    @DS("app")
    public R<PageResult<StudentRecord>> getRecordList(int page, int size, String orderNo, String student,
                                                     Date startDate, Date endDate) {
        try {
            // 参数验证
            if (page < 1) {
                page = 1;
            }
            if (size < 1) {
                size = 10;
            }
            
            int offset = (page - 1) * size;
            
            // 查询总数
            long total = studentRecordMapper.countTotal(orderNo, student, startDate, endDate);
            
            // 查询分页数据
            List<StudentRecord> records = studentRecordMapper.selectPage(offset, size, orderNo, student, startDate, endDate);
            
            // 转换数据格式
            for (StudentRecord record : records) {
                convertJsonToLists(record);
                convertIntToBoolean(record);
            }
            
            // 批量获取鞋图
            if (!records.isEmpty()) {
                // 提取所有单号
                List<String> orderNos = records.stream()
                    .map(StudentRecord::getOrderNo)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
                
                if (!orderNos.isEmpty()) {
                    // 批量查询订单信息(订单数量和型体)
                    DynamicDataSourceContextHolder.push("master");
                    List<StudentRecord> orderInfos = studentRecordMapper.getOrderInfoByOrderNos(orderNos);
                    
                    // 批量查询鞋图
                    List<StudentRecord> shoeImages = studentRecordMapper.getShoeImagesByOrderNos(orderNos);
                    DynamicDataSourceContextHolder.poll();
                    
                    // 创建单号到订单信息的映射
                    Map<String, StudentRecord> orderInfoMap = orderInfos.stream()
                        .filter(info -> info.getOrderNo() != null)
                        .collect(Collectors.toMap(
                            StudentRecord::getOrderNo, 
                            info -> info,
                            (existing, replacement) -> existing // 如果有重复的单号，保留第一个
                        ));
                    
                    // 创建单号到鞋图的映射
                    Map<String, byte[]> shoeImageMap = shoeImages.stream()
                        .filter(img -> img.getOrderNo() != null && img.getShoePic() != null)
                        .collect(Collectors.toMap(
                            StudentRecord::getOrderNo, 
                            StudentRecord::getShoePic,
                            (existing, replacement) -> existing // 如果有重复的单号，保留第一个
                        ));
                    
                    // 将订单信息和鞋图数据填充到记录中
                    for (StudentRecord record : records) {
                        if (record.getOrderNo() != null) {
                            // 填充订单数量和型体
                            if (orderInfoMap.containsKey(record.getOrderNo())) {
                                StudentRecord orderInfo = orderInfoMap.get(record.getOrderNo());
                                record.setOrdQty(orderInfo.getOrdQty());
                                record.setModelNo(orderInfo.getModelNo());
                            }
                            
                            // 填充鞋图
                            if (shoeImageMap.containsKey(record.getOrderNo())) {
                                record.setShoePic(shoeImageMap.get(record.getOrderNo()));
                            }
                        }
                    }
                }
            }
            
            PageResult<StudentRecord> pageResult = new PageResult<>(records, total, page, size);
            return R.success(pageResult);
        } catch (Exception e) {
            e.printStackTrace();
            return R.error("获取学员记录列表失败：" + e.getMessage());
        }
    }
    
    @Override
    @DS("master")
    public R<StudentRecord> getShoeImageByOrderNo(String orderNo) {
        try {
            if (!StringUtils.hasText(orderNo)) {
                return R.error("单号不能为空");
            }
            
            // 创建一个列表来批量查询
            List<String> orderNos = new ArrayList<>();
            orderNos.add(orderNo);
            
            // 获取订单信息(订单数量和型体)
            List<StudentRecord> orderInfos = studentRecordMapper.getOrderInfoByOrderNos(orderNos);
            
            // 获取鞋图信息
            List<StudentRecord> shoeImages = studentRecordMapper.getShoeImagesByOrderNos(orderNos);
            
            // 创建返回对象
            StudentRecord result = new StudentRecord();
            result.setOrderNo(orderNo);
            
            // 填充订单信息
            if (!orderInfos.isEmpty()) {
                StudentRecord orderInfo = orderInfos.get(0);
                result.setOrdQty(orderInfo.getOrdQty());
                result.setModelNo(orderInfo.getModelNo());
            }
            
            // 填充鞋图信息
            if (!shoeImages.isEmpty() && shoeImages.get(0).getShoePic() != null) {
                result.setShoePic(shoeImages.get(0).getShoePic());
            }
            
            // 检查是否有任何有用的数据
            if (result.getShoePic() != null || result.getOrdQty() != null || result.getModelNo() != null) {
                return R.success(result);
            } else {
                return R.error("未找到对应的订单信息");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return R.error("获取订单信息失败：" + e.getMessage());
        }
    }
    
    /**
     * 将图片列表转换为JSON字符串
     */
    private void convertListsToJson(StudentRecord record) {
        try {
            if (record.getActionPics() != null) {
                record.setActionPicsJson(objectMapper.writeValueAsString(record.getActionPics()));
            }
            if (record.getImprovePics() != null) {
                record.setImprovePicsJson(objectMapper.writeValueAsString(record.getImprovePics()));
            }
            if (record.getProductPics() != null) {
                record.setProductPicsJson(objectMapper.writeValueAsString(record.getProductPics()));
            }
        } catch (Exception e) {
            throw new RuntimeException("转换图片列表为JSON失败", e);
        }
    }
    
    /**
     * 将JSON字符串转换为图片列表
     */
    private void convertJsonToLists(StudentRecord record) {
        try {
            TypeReference<List<String>> typeRef = new TypeReference<List<String>>() {};
            
            if (StringUtils.hasText(record.getActionPicsJson())) {
                record.setActionPics(objectMapper.readValue(record.getActionPicsJson(), typeRef));
            } else {
                record.setActionPics(new ArrayList<>());
            }
            
            if (StringUtils.hasText(record.getImprovePicsJson())) {
                record.setImprovePics(objectMapper.readValue(record.getImprovePicsJson(), typeRef));
            } else {
                record.setImprovePics(new ArrayList<>());
            }
            
            if (StringUtils.hasText(record.getProductPicsJson())) {
                record.setProductPics(objectMapper.readValue(record.getProductPicsJson(), typeRef));
            } else {
                record.setProductPics(new ArrayList<>());
            }
        } catch (Exception e) {
            // 如果解析失败，设置为空列表
            record.setActionPics(new ArrayList<>());
            record.setImprovePics(new ArrayList<>());
            record.setProductPics(new ArrayList<>());
        }
    }
    
    /**
     * 将Boolean转换为Integer（用于数据库存储）
     */
    private void convertBooleanToInt(StudentRecord record) {
        if (record.getLearned() != null) {
            record.setLearnedInt(record.getLearned() ? 1 : 0);
        } else {
            record.setLearnedInt(0);
        }
    }
    
    /**
     * 将Integer转换为Boolean（用于返回给前端）
     */
    private void convertIntToBoolean(StudentRecord record) {
        if (record.getLearnedInt() != null) {
            record.setLearned(record.getLearnedInt() == 1);
        } else {
            record.setLearned(false);
        }
    }
} 