<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zqn.modeldata2.mapper.SopMapper">
    <!-- SOP 持久层 -->
    <!-- 获取制程列表 -->
    <select id="getOperationList" resultType="com.zqn.modeldata2.entity.sop.SopOperation">
        select operation, fn_sy_getxcodeval('MC001-5', operation) as operationDesc,
               pro_seq as proSeq, process
        from mg_process
        where pro_seq in ('1F', '1G', '1H', '1M', '1E', '1D', '3B', '5G', '5B', '5M', '5H', '5K', '8D', '8P', '8T', '8Q', '8R', '8U', '8S', '8Y', '6A')
        order by instr('1F, 1G, 1H, 1M, 1E, 1D, 3B, 5G, 5B, 5M, 5H, 5K, 8D, 8P, 8T, 8Q, 8R, 8U, 8S, 8Y, 6A', pro_seq)
    </select>

    <!-- 获取品牌列表 -->
    <select id="getBrandList" resultType="string">
        select distinct a.brand_no
        from be_brand a, sy_userbrand b
        where a.brand_no = b.brand_no(+)
          and b.user_no(+) = '00006'
          and (
              bitand(16, 16) > 0 and
              bitand(a.made_factory, 128 + 256) > 0 or
              bitand(a.dev_factory + a.made_factory * 4, 1024) > 0
          )
        order by a.brand_no
    </select>

    <!-- 获取型体列表 -->
    <select id="getModelList" resultType="com.zqn.modeldata2.entity.sop.SopModel">
        select distinct a.model_no as modelNo, a.model_desc as modelDesc
        from ck_smodel a, be_moudel b, bf_last c, cb_model d
        where a.module_no = b.module_no(+)
          and b.last_seq = c.last_seq(+)
          and a.model_no = d.model_no(+)
          and a.brand_no = #{brand}
        order by a.model_no
    </select>

    <!-- 获取所有型体列表 -->
    <select id="getAllModelList" resultType="string">
        select distinct a.model_no
        from ck_smodel a, be_moudel b, bf_last c, cb_model d
        where a.module_no = b.module_no(+)
          and b.last_seq = c.last_seq(+)
          and a.model_no = d.model_no(+)
        order by a.model_no
    </select>

    <!-- 获取型体鞋图 -->
    <select id="getModelPicture" resultType="com.zqn.modeldata2.entity.sop.SopPicture">
        select model_pic as modelPicture
        from ck_smodel
        where model_no = #{model}
          and model_pic is not null
    </select>

    <!-- 获取工序信息生产类型列表 -->
    <select id="getInfoTypeList" resultType="string">
        select column_no
        from sy_xcodeval
        <if test="operation != 5">
        where code_no = 'MC002'
        </if>
        <if test="operation == 5">
        where code_no = 'MC005'
        </if>
        order by column_seq
    </select>

    <!-- 获取工序信息材质列表 -->
    <select id="getInfoMaterialList" resultType="com.zqn.modeldata2.entity.sop.SopMaterial">
        select column_no as material
        from sy_xcodeval
        where code_no = 'MC003'
        order by column_seq
    </select>

    <!-- 鞋面 获取工序信息材质列表 -->
    <select id="getInfoMaterialList1" resultType="com.zqn.modeldata2.entity.sop.SopMaterial">
        select distinct c.material, a.model_ver as modelVer
        from gc_sorder a, vck_smodel b, vcm_spros c
        where a.model_no = b.model_no
          and a.pro_no = c.pro_no
          and a.frm_flag = c.frm_flag
          and a.dev_type = '刊版樣品'
          and a.model_no = #{model}
        order by a.model_ver, c.material
    </select>

    <!-- 半成品、成型、皮底 获取工序信息材质列表 -->
    <select id="getInfoMaterialList2" resultType="com.zqn.modeldata2.entity.sop.SopMaterial">
        select model_ver as modelVer, material
        from cb_modelver
        where model_no = #{model}
          and material is not null
          and model_ver &lt;&gt; 'X'
        order by model_ver, material
    </select>

    <!-- 获取工序信息部位名称列表 -->
    <select id="getInfoPartList" resultType="string">
        select part_nm
        from ba_shoepartc
        order by part_no
    </select>

    <!-- 获取楦头编号列表 -->
    <select id="getLastNosList" resultType="string">
        select distinct c.last_nos
        from ck_smodel a, be_moudel b, bf_last c
        where a.module_no = b.module_no(+)
          and b.last_seq = c.last_seq(+)
          and a.brand_no = #{brand}
        order by c.last_nos nulls first
    </select>

    <!-- 获取 Outsole 列表 -->
    <select id="getOsNoList" resultType="string">
        select distinct a.os_no
        from ck_smodel a, be_moudel b, bf_last c
        where a.module_no = b.module_no(+)
          and b.last_seq = c.last_seq(+)
          and a.brand_no = #{brand}
        order by a.os_no nulls first
    </select>

    <!-- 获取工序信息列表 -->
    <select id="getProcessInfoList" resultType="com.zqn.modeldata2.entity.sop.SopProcessInfo">
        select *
        from (
            select rownum as rn, t.*
            from (
                select t1.model_no as model, model_desc as modelDesc, last_nos as lastNos,
                       os_no as osNo, operation, rtg_code as rtgCode, pro_seq as proSeq,
                       rtg_type as rtgType, material, cpl_flag as cplFlag, cpl_date as cplDate,
                       part_name as partName, created_tag as createType, sku,
                       ins_user as insUser, ins_date as insDate,
                       fn_sy_userdesc(ins_user) as insName, (
                           select count(*)
                           from ma_rtgd_pub t2
                           where t2.model_no = t1.model_no
                             and t2.operation = t1.operation
                             and t2.rtg_code = t1.rtg_code
                       ) as flowNumber
                from ma_rtgc_pub t1
                left join (
                    select a.model_no, a.model_desc, a.os_no, c.last_nos
                    from ck_smodel a, be_moudel b, bf_last c
                    where a.module_no = b.module_no(+)
                      and b.last_seq = c.last_seq(+)
                      and a.brand_no = #{brand}
                ) t3
                on t1.model_no = t3.model_no
                where t1.model_no in (
                    select distinct a.model_no
                    from ck_smodel a, cb_model b, bf_last c
                    where a.model_no = b.model_no(+)
                      and b.last_seq = c.last_seq(+)
                      and a.brand_no = #{brand}
                )
                  and operation = #{operation}
                  and revision = '預估版0'
                <if test="model != null and model.length() > 0">
                  and t1.model_no = #{model}
                </if>
                <if test="insUser != null and insUser.length() > 0">
                  and ins_user = #{insUser}
                </if>
                <if test="createType == 'ERP'">
                  and created_tag is null
                </if>
                <if test="createType == 'APP'">
                  and created_tag = 'APP'
                </if>
                <if test="lastNos != null and lastNos.length() > 0 and lastNos != '全部'">
                  and last_nos = #{lastNos}
                </if>
                <if test="lastNos == null or lastNos.length() == 0">
                  and last_nos is null
                </if>
                <if test="osNo != null and osNo.length() > 0 and osNo != '全部'">
                  and os_no = #{osNo}
                </if>
                <if test="osNo == null or osNo.length() == 0">
                  and os_no is null
                </if>
                order by ins_date desc, t1.model_no, model_desc, last_nos, os_no
            ) t
        )
        where rn &gt; (#{pageNo} - 1) * #{pageSize}
          and rn &lt;= #{pageNo} * #{pageSize}
    </select>

    <!-- 修改工序信息生产类型 -->
    <update id="updateInfoType">
        update ma_rtgc_pub
        set rtg_type = #{rtgType},
            upd_user = #{updUser},
            upd_date = sysdate
        where model_no = #{model}
          and operation = #{operation}
          and rtg_code = #{rtgCode}
    </update>

    <!-- 修改工序信息材质 -->
    <update id="updateInfoMaterial">
        update ma_rtgc_pub
        set material = #{material},
            upd_user = #{updUser},
            upd_date = sysdate
        where model_no = #{model}
          and operation = #{operation}
          and rtg_code = #{rtgCode}
    </update>

    <!-- 修改工序信息部位 -->
    <update id="updateInfoPart">
        update ma_rtgc_pub
        set part_name = #{partName},
            upd_user = #{updUser},
            upd_date = sysdate
        where model_no = #{model}
          and operation = #{operation}
          and rtg_code = #{rtgCode}
    </update>

    <!-- 添加工序信息 -->
    <insert id="addProcessInfo">
        insert into ma_rtgc_pub (
            model_no, operation, rtg_code, rtg_type, material, pro_seq,
            revision, allow, part_name, created_tag, ins_user, ins_date, upd_user, upd_date
        ) values (
            #{model}, #{operation}, (
                select lpad(nvl2(max(to_number(rtg_code)), max(to_number(rtg_code)) + 1, 1), 2, '0') as rtgCode
                from ma_rtgc_pub
                where model_no = #{model}
                  and operation = #{operation}
            ), #{rtgType}, #{material}, #{proSeq}, '預估版0', 0.06, #{partName}, 'APP', #{insUser}, sysdate, #{updUser}, sysdate
        )
    </insert>

    <!-- 查询备份工序信息 -->
    <select id="getBackupInfo" resultType="com.zqn.modeldata2.entity.sop.SopBackupInfo">
        select *
        from ma_rtgc_pub
        where model_no = #{model}
          and operation = #{operation}
          and rtg_code = #{rtgCode}
    </select>

    <!-- 备份工序信息 -->
    <insert id="addBackupInfo">
        insert into pcc_sop_backup_info (
            model_no, operation, rtg_code, rtg_type, material, revision, allow,
            setup_hr, hr_cap, req_qty, std_emp, pps_emp, pps_time, pps_rate,
            remark, inv_flag, inv_user, inv_date, ins_user, ins_date, upd_user, upd_date,
            loo_time, pts_time, est_rate, model_ver, dmaterial, cut_cap, cut_cap2,
            cut_cap3, cut_cap4, sign_name, chk_flag, chk_user, chk_date, dmat_update,
            rtg_class, pro_seq, shoe_make_head, printmaker, senior_technician, delete_time,
            cpl_flag, cpl_date, part_name, created_tag
        ) values (
            #{model_no}, #{operation}, #{rtg_code}, #{rtg_type}, #{material}, #{revision}, #{allow},
            #{setup_hr}, #{hr_cap}, #{req_qty}, #{std_emp}, #{pps_emp}, #{pps_time}, #{pps_rate},
            #{remark}, #{inv_flag}, #{inv_user}, #{inv_date}, #{ins_user}, #{ins_date}, #{upd_user}, #{upd_date},
            #{loo_time}, #{pts_time}, #{est_rate}, #{model_ver}, #{dmaterial}, #{cut_cap}, #{cut_cap2},
            #{cut_cap3}, #{cut_cap4}, #{sign_name}, #{chk_flag}, #{chk_user}, #{chk_date}, #{dmat_update},
            #{rtg_class}, #{pro_seq}, #{shoe_make_head}, #{printmaker}, #{senior_technician}, sysdate,
            #{cpl_flag}, #{cpl_date}, #{part_name}, #{created_tag}
        )
    </insert>

    <!-- 删除工序信息 -->
    <delete id="deleteProcessInfo">
        delete from ma_rtgc_pub
        where
        <foreach collection="processInfoList" item="processInfo" separator="or">
            (
                model_no = #{processInfo.model} and
                operation = #{processInfo.operation} and
                rtg_code = #{processInfo.rtgCode}
            )
        </foreach>
    </delete>

    <!-- 获取工序流程列表 -->
    <select id="getProcessFlowList" resultType="com.zqn.modeldata2.entity.sop.SopProcessFlow">
        select model_no as model, operation, rtg_code as rtgCode,
               seq_no as seqNo, skey, wk_group as wkGroup,
               seq_name as seqName, actions, version,
               ins_user as insUser, ins_date as insDate,
               upd_user as updUser, upd_date as updDate
        from ma_rtgd_pub t1
        where model_no = #{model}
          and operation = #{operation}
          and rtg_code = #{rtgCode}
        order by skey
    </select>

    <!-- 添加工序流程 -->
    <insert id="addProcessFlow">
        insert into ma_rtgd_pub (
            model_no, operation, rtg_code,
            seq_no, skey, wk_group,
            seq_name, actions, version,
            ins_user, ins_date, upd_user, upd_date
        ) values (
            #{model}, #{operation}, #{rtgCode}, (
                select lpad(nvl2(max(to_number(seq_no)), max(to_number(seq_no)) + 1, 1), 3, '0') as seqNo
                from ma_rtgd_pub
                where model_no = #{model}
                  and operation = #{operation}
                  and rtg_code = #{rtgCode}
            ), (
                select nvl2(max(skey), max(to_number(skey)) + 1, 1) as skey
                from ma_rtgd_pub
                where model_no = #{model}
                  and operation = #{operation}
                  and rtg_code = #{rtgCode}
            ), (
                select lpad(nvl2(max(to_number(wk_group)), max(to_number(wk_group)) + 1, 1), 3, '0') as wkGroup
                from ma_rtgd_pub
                where model_no = #{model}
                  and operation = #{operation}
                  and rtg_code = #{rtgCode}
            ), '新工序', '新工序', 1,
            #{insUser}, sysdate, #{updUser}, sysdate
        )
    </insert>

    <!-- 插入工序流程 -->
    <insert id="insertProcessFlow">
        insert into ma_rtgd_pub (
            model_no, operation, rtg_code,
            seq_no, skey, wk_group,
            seq_name, actions, version,
            ins_user, ins_date, upd_user, upd_date
        ) values (
            #{model}, #{operation}, #{rtgCode}, (
                select lpad(nvl2(max(to_number(seq_no)), max(to_number(seq_no)) + 1, 1), 3, '0') as seqNo
                from ma_rtgd_pub
                where model_no = #{model}
                  and operation = #{operation}
                  and rtg_code = #{rtgCode}
            ), #{skey} + 1, lpad(#{skey} + 1, 3, '0'), '新工序', '新工序', 1,
            #{insUser}, sysdate, #{updUser}, sysdate
        )
    </insert>

    <!-- 插入时序号加一 -->
    <update id="sequencePlus">
        update ma_rtgd_pub
        set skey = skey + 1
        where model_no = #{model}
          and operation = #{operation}
          and rtg_code = #{rtgCode}
          and skey &gt; #{skey}
    </update>

    <!-- 获取模板列表 -->
    <select id="getTemplateList" resultType="com.zqn.modeldata2.entity.sop.SopTemplate">
        select doc_no as docNo, pro_seq as proSeq, type_desc as typeDesc, material
        from ma_seqdat
        where operation = #{operation}
        order by pro_seq
    </select>

    <!-- 模板导入工序流程 -->
    <insert id="templateImportFlow">
        insert into ma_rtgd_pub (
            model_no, operation, rtg_code,
            seq_no, skey, wk_group,
            seq_name, actions, version,
            ins_user, ins_date, upd_user, upd_date
        )
        select #{model} as model_no, #{operation} as operation, #{rtgCode} as rtg_code,
               seq_no, rownum as skey, wk_group,
               seq_name, seq_name as actions, 1 as version,
               #{insUser} as ins_user, sysdate, #{updUser} as upd_user, sysdate
        from ma_seqdatb
        where doc_no = #{docNo}
    </insert>

    <!-- 修改工序流程加工段 -->
    <update id="updateFlowSection">
        update ma_rtgd_pub
        set wk_group = #{targetWkGroup}
        where model_no = #{model}
          and operation = #{operation}
          and rtg_code = #{rtgCode}
          and skey = #{skey}
          and wk_group = #{wkGroup}
    </update>

    <!-- 序号加一 -->
    <update id="sequencePlusOne">
        update ma_rtgd_pub
        set skey = skey + 1
        where model_no = #{model}
          and operation = #{operation}
          and rtg_code = #{rtgCode}
          and skey &gt;= #{targetSkey}
          and skey &lt; #{skey}
    </update>

    <!-- 序号减一 -->
    <update id="sequenceMinusOne">
        update ma_rtgd_pub
        set skey = skey - 1
        where model_no = #{model}
          and operation = #{operation}
          and rtg_code = #{rtgCode}
          and skey &gt; #{skey}
          and skey &lt;= #{targetSkey}
    </update>

    <!-- 修改工序流程序号 -->
    <update id="updateFlowSequence">
        update ma_rtgd_pub
        set skey = #{targetSkey}
        where model_no = #{model}
          and operation = #{operation}
          and rtg_code = #{rtgCode}
          and skey = #{skey}
          and wk_group = #{wkGroup}
    </update>

    <!-- 获取流程详情 -->
    <select id="getFlowDetail" resultType="com.zqn.modeldata2.entity.sop.SopFlowDetail">
        select model_no as model, operation, rtg_code as rtgCode,
               seq_no as seqNo, actions, img_tit1 as imgTit1, version,
               tools, description as machine, margin, temp, pressure,
               glue, car_line as carLine, chemical_substance as chemical,
               needle_spacing as needleSpacing, spacing, needle, time, defence,
               process_option1 as processOption1, process_option2 as processOption2,
               process_option3 as processOption3, process_option4 as processOption4,
               process_option5 as processOption5, process_option6 as processOption6,
               process_option7 as processOption7, process_option8 as processOption8,
               process_option9 as processOption9, process_option10 as processOption10
        from ma_rtgd_pub
        where model_no = #{model}
          and operation = #{operation}
          and rtg_code = #{rtgCode}
          and seq_no = #{seqNo}
    </select>

    <!-- 获取流程图片列表 -->
    <select id="getFlowPictureList" resultType="com.zqn.modeldata2.entity.sop.SopFlowPicture">
        select id, img_url as imgUrl
        from pcc_me_project_plan_img
        where model_no = #{model}
          and operation = #{operation}
          and rtg_code = #{rtgCode}
          and seq_no = #{seqNo}
        order by id
    </select>

    <!-- 获取工序流程操作标准 -->
    <select id="getFlowStandard" resultType="com.zqn.modeldata2.entity.sop.SopFlowDetail">
        select id as standardId, content as standard
        from pcc_sop_cont
        where model_no = #{model}
          and operation = #{operation}
          and rtg_code = #{rtgCode}
          and seq_no = #{seqNo}
          and type = 1
    </select>

    <!-- 获取工序流程自检点 -->
    <select id="getFlowCheckPoint" resultType="com.zqn.modeldata2.entity.sop.SopFlowDetail">
        select id as checkPointId, content as checkPoint
        from pcc_sop_cont
        where model_no = #{model}
          and operation = #{operation}
          and rtg_code = #{rtgCode}
          and seq_no = #{seqNo}
          and type = 2
    </select>

    <!-- 修改工序流程详情 -->
    <update id="updateFlowDetail">
        update ma_rtgd_pub
        set seq_name = #{seqName},
            actions = #{actions},
            img_tit1 = #{imgTit1},
            tools = #{tools},
            description = #{machine},
            margin = #{margin},
            temp = #{temp},
            pressure = #{pressure},
            glue = #{glue},
            car_line = #{carLine},
            chemical_substance = #{chemical},
            needle_spacing = #{needleSpacing},
            spacing = #{spacing},
            needle = #{needle},
            time = #{time},
            defence = #{defence},
            remark = #{remark},
            upd_user = #{updUser},
            upd_date = sysdate,
            process_option1 = #{processOption1},
            process_option2 = #{processOption2},
            process_option3 = #{processOption3},
            process_option4 = #{processOption4},
            process_option5 = #{processOption5},
            process_option6 = #{processOption6},
            process_option7 = #{processOption7},
            process_option8 = #{processOption8},
            process_option9 = #{processOption9},
            process_option10 = #{processOption10}
        where model_no = #{model}
          and operation = #{operation}
          and rtg_code = #{rtgCode}
          and seq_no = #{seqNo}
    </update>

    <!-- 查询备份图片 -->
    <select id="getBackupImage" resultType="com.zqn.modeldata2.entity.sop.SopBackupImage">
        select *
        from pcc_me_project_plan_img
        where model_no = #{model}
          and operation = #{operation}
          and rtg_code = #{rtgCode}
        <if test="seqNo != null and seqNo.length > 0">
          and seq_no = #{seqNo}
        </if>
    </select>

    <!-- 备份图片 -->
    <insert id="addBackupImage">
        insert into pcc_sop_backup_image (
            model_no, operation, rtg_code, seq_no, img_url, remark, delete_time
        ) values (
            #{model_no}, #{operation}, #{rtg_code}, #{seq_no}, #{img_url}, #{remark}, sysdate
        )
    </insert>

    <!-- 删除工序流程图片 -->
    <delete id="deleteFlowPicture">
        delete from pcc_me_project_plan_img
        where model_no = #{model}
          and operation = #{operation}
          and rtg_code = #{rtgCode}
          and seq_no = #{seqNo}
    </delete>

    <!-- 添加工序流程图片 -->
    <insert id="addFlowPicture">
        insert into pcc_me_project_plan_img (
            model_no, operation, rtg_code, seq_no, img_url
        ) values (
            #{model}, #{operation}, #{rtgCode}, #{seqNo}, #{imgUrl}
        )
    </insert>

    <!-- 查询备份 操作标准 和 自检点 -->
    <select id="getBackupContent" resultType="com.zqn.modeldata2.entity.sop.SopBackupContent">
        select *
        from pcc_sop_cont
        where model_no = #{model}
          and operation = #{operation}
          and rtg_code = #{rtgCode}
        <if test="seqNo != null and seqNo.length > 0">
          and seq_no = #{seqNo}
        </if>
    </select>

    <!-- 备份 操作标准 和 自检点 -->
    <insert id="addBackupContent">
        insert into pcc_sop_backup_content (
            model_no, operation, rtg_code, seq_no, content, lang, type, delete_time
        ) values (
            #{model_no}, #{operation}, #{rtg_code}, #{seq_no}, #{content}, #{lang}, #{type}, sysdate
        )
    </insert>

    <!-- 删除工序流程 操作标准 和 自检点 -->
    <delete id="deleteStandardAndCheckPoint">
        delete from pcc_sop_cont
        where model_no = #{model}
          and operation = #{operation}
          and rtg_code = #{rtgCode}
          and seq_no = #{seqNo}
    </delete>

    <!-- 添加工序流程操作标准 -->
    <insert id="addFlowStandard">
        insert into pcc_sop_cont (
            model_no, operation, rtg_code, seq_no,
            content, lang, type
        ) values (
            #{model}, #{operation}, #{rtgCode}, #{seqNo},
            #{standard}, 'zh-Hans', 1
        )
    </insert>

    <!-- 添加工序流程自检点 -->
    <insert id="addFlowCheckPoint">
        insert into pcc_sop_cont (
            model_no, operation, rtg_code, seq_no,
            content, lang, type
        ) values (
            #{model}, #{operation}, #{rtgCode}, #{seqNo},
            #{checkPoint}, 'zh-Hans', 2
        )
    </insert>

    <!-- 查询备份工序详情 -->
    <select id="getBackupDetail" resultType="com.zqn.modeldata2.entity.sop.SopBackupDetail">
        select *
        from ma_rtgd_pub
        where model_no = #{model}
          and operation = #{operation}
          and rtg_code = #{rtgCode}
        <if test="seqNo != null and seqNo.length > 0">
          and seq_no = #{seqNo}
        </if>
    </select>

    <!-- 备份工序详情 -->
    <insert id="addBackupDetail">
        insert into pcc_sop_backup_detail (
            model_no, operation, rtg_code, seq_no, skey, seq_name, wk_group, material,
            piece, layer, cut_die, normal, add_time, std_time, std_emp, pps_emp,
            level_code, lab_cost, pline, item_no, description, use_qty, defence, remark,
            inv_flag, inv_user, inv_date, ins_user, ins_date, upd_user, upd_date, szr_flag,
            novalue, normal1, seq_name2, model_ver, cut_seq, am_action, seq_pic, class_name,
            spec, pmodule, ppmodule, matwht, whtpair, actions, tools, margin, temp,
            pressure, glue, car_line, chemical_substance, needle_spacing, spacing, needle,
            img_tit1, img_tit2, time, version, delete_time,
            process_option1, process_option2, process_option3, process_option4, process_option5,
            process_option6, process_option7, process_option8, process_option9, process_option10
        ) values (
            #{model_no}, #{operation}, #{rtg_code}, #{seq_no}, #{skey}, #{seq_name}, #{wk_group}, #{material},
            #{piece}, #{layer}, #{cut_die}, #{normal}, #{add_time}, #{std_time}, #{std_emp}, #{pps_emp},
            #{level_code}, #{lab_cost}, #{pline}, #{item_no}, #{description}, #{use_qty}, #{defence}, #{remark},
            #{inv_flag}, #{inv_user}, #{inv_date}, #{ins_user}, #{ins_date}, #{upd_user}, #{upd_date}, #{szr_flag},
            #{novalue}, #{normal1}, #{seq_name2}, #{model_ver}, #{cut_seq}, #{am_action}, #{seq_pic}, #{class_name},
            #{spec}, #{pmodule}, #{ppmodule}, #{matwht}, #{whtpair}, #{actions}, #{tools}, #{margin}, #{temp},
            #{pressure}, #{glue}, #{car_line}, #{chemical_substance}, #{needle_spacing}, #{spacing}, #{needle},
            #{img_tit1}, #{img_tit2}, #{time}, #{version}, sysdate,
            #{process_option1}, #{process_option2}, #{process_option3}, #{process_option4}, #{process_option5},
            #{process_option6}, #{process_option7}, #{process_option8}, #{process_option9}, #{process_option10}
        )
    </insert>

    <!-- 删除工序流程 -->
    <delete id="deleteProcessFlow">
        delete from ma_rtgd_pub
        where
        <foreach collection="processFlowList" item="processFlow" separator="or">
            (
                model_no = #{processFlow.model} and
                operation = #{processFlow.operation} and
                rtg_code = #{processFlow.rtgCode} and
                seq_no = #{processFlow.seqNo}
            )
        </foreach>
    </delete>

    <!-- 获取所有工序流程图片 -->
    <select id="getAllFlowPictureList" resultType="com.zqn.modeldata2.entity.sop.SopFlowPicture">
        select id, img_url as imgUrl
        from pcc_me_project_plan_img
        where model_no = #{model}
          and operation = #{operation}
          and rtg_code = #{rtgCode}
    </select>

    <!-- 重置工序流程图片 -->
    <delete id="resetFlowPicture">
        delete from pcc_me_project_plan_img
        where model_no = #{model}
          and operation = #{operation}
          and rtg_code = #{rtgCode}
    </delete>

    <!-- 重置工序流程 操作标准 和 自检点 -->
    <delete id="resetStandardAndCheckPoint">
        delete from pcc_sop_cont
        where model_no = #{model}
          and operation = #{operation}
          and rtg_code = #{rtgCode}
    </delete>

    <!-- 重置工序流程 -->
    <delete id="resetProcessFlow">
        delete from ma_rtgd_pub
        where model_no = #{model}
          and operation = #{operation}
          and rtg_code = #{rtgCode}
    </delete>

    <!-- 获取选项列表 -->
    <select id="getFlowOptionList" resultType="string">
        select content
        from pcc_sop_option
        where type = #{type}
        <if test="dept != null">
          and dept = #{dept}
        </if>
          and action_id = 0
          and lang = 'zh-Hans'
    </select>

    <!-- 获取动作列表 -->
    <select id="getFlowActionList" resultType="com.zqn.modeldata2.entity.sop.SopFlowAction">
        select id, item, action_cn as actionCn, type, tag
        from pcc_me_project_plan_actions
        where pro_seq = #{proSeq}
        order by tag desc, item
    </select>

    <!-- 选择动作 -->
    <select id="selectFlowAction" resultType="com.zqn.modeldata2.entity.sop.SopActionDetail">
        select a.id, a.content, a.type, a.action_id
        from pcc_sop_option a, pcc_me_project_plan_actions b
        where a.action_id = b.id
          and a.action_id = #{actionId}
        order by a.type
    </select>

    <!-- 获取流程预览列表 -->
    <select id="getPreviewList" resultType="com.zqn.modeldata2.entity.sop.SopPreview">
        select t1.model_no as model, t1.operation, t1.rtg_code as rtgCode,
               t1.seq_no as seqNo, t1.skey, t1.wk_group as wkGroup,
               t1.seq_name as seqName, t1.actions, t1.img_tit1 as imgTit1, t1.version,
               t1.tools, t1.description as machine, t1.margin, t1.temp, t1.pressure,
               t1.glue, t1.car_line as carLine, t1.chemical_substance as chemical,
               t1.needle_spacing as needleSpacing, t1.spacing, t1.needle, t1.time, t1.defence,
               t1.process_option1 as processOption1, t1.process_option2 as processOption2,
               t1.process_option3 as processOption3, t1.process_option4 as processOption4,
               t1.process_option5 as processOption5, t1.process_option6 as processOption6,
               t1.process_option7 as processOption7, t1.process_option8 as processOption8,
               t1.process_option9 as processOption9, t1.process_option10 as processOption10,
               t2.model_desc as modelDesc, t2.last_nos as last, t2.os_no as osNo,
               fn_sy_userdesc(t1.ins_user) as tab,
               t1.ins_user as insUser, t1.ins_date as insDate, t1.upd_user as updUser, t1.upd_date as updDate
        from ma_rtgd_pub t1
        left join (
            select a.model_no, a.model_desc, a.os_no, c.last_nos
            from ck_smodel a, be_moudel b, bf_last c
            where a.module_no = b.module_no(+)
              and b.last_seq = c.last_seq(+)
              and a.model_no = #{model}
        ) t2
        on t1.model_no = t2.model_no
        where t1.model_no = #{model}
          and t1.operation = #{operation}
          and t1.rtg_code = #{rtgCode}
        order by t1.skey
    </select>

    <!-- 获取最大主要代码 -->
    <select id="getMaxRtgCode" resultType="string">
        select lpad(nvl2(max(to_number(rtg_code)), max(to_number(rtg_code)), 1), 2, '0') as rtgCode
        from ma_rtgc_pub
        where model_no = #{model}
          and operation = #{operation}
    </select>

    <!-- 复制工序流程 -->
    <insert id="copyProcessFlow">
        insert into ma_rtgd_pub (
            model_no, operation, rtg_code, seq_no, skey, wk_group, seq_name, actions, img_tit1,
            tools, description, margin, temp, pressure, glue, car_line, chemical_substance,
            needle_spacing, spacing, needle, time, defence, ins_user, ins_date, upd_user, upd_date, version,
            process_option1, process_option2, process_option3, process_option4, process_option5,
            process_option6, process_option7, process_option8, process_option9, process_option10
        ) values (
            #{model}, #{operation}, #{rtgCode}, #{seqNo}, #{skey}, #{wkGroup}, #{seqName}, #{actions}, #{imgTit1},
            #{tools}, #{machine}, #{margin}, #{temp}, #{pressure}, #{glue}, #{carLine}, #{chemical},
            #{needleSpacing}, #{spacing}, #{needle}, #{time}, #{defence}, #{insUser}, sysdate, #{updUser}, sysdate, 1,
            #{processOption1}, #{processOption2}, #{processOption3}, #{processOption4}, #{processOption5},
            #{processOption6}, #{processOption7}, #{processOption8}, #{processOption9}, #{processOption10}
        )
    </insert>

    <!-- 复制 操作标准 和 自检点 -->
    <insert id="copyStandardAndCheckPoint">
        insert into pcc_sop_cont (
            model_no, operation, rtg_code, seq_no, content, lang, type
        )
        select #{targetModel} as model_no, operation, #{targetRtgCode} as rtg_code, seq_no, content, lang, type
        from pcc_sop_cont
        where model_no = #{model}
          and operation = #{operation}
          and rtg_code = #{rtgCode}
    </insert>

    <!-- 获取整体流程 -->
    <select id="getOverallFlow" resultType="com.zqn.modeldata2.entity.sop.SopFlowDetail">
        select model_no as model, operation, rtg_code as rtgCode,
               seq_no as seqNo, skey, wk_group as wkGroup,
               version, actions, img_tit1 as imgTit1,
               tools, description as machine, margin, temp, pressure,
               glue, car_line as carLine, chemical_substance as chemical,
               needle_spacing as needleSpacing, spacing, needle, time, defence,
               process_option1 as processOption1, process_option2 as processOption2,
               process_option3 as processOption3, process_option4 as processOption4,
               process_option5 as processOption5, process_option6 as processOption6,
               process_option7 as processOption7, process_option8 as processOption8,
               process_option9 as processOption9, process_option10 as processOption10,
               ins_user as insUser, ins_date as insDate,
               upd_user as updUser, upd_date as updDate
        from ma_rtgd_pub t1
        where model_no = #{model}
          and operation = #{operation}
          and rtg_code = #{rtgCode}
        order by skey
    </select>

    <!-- 添加整体流程 -->
    <insert id="addOverallFlow">
        insert into ma_rtgd_pub (
            model_no, operation, rtg_code,
            seq_no, skey, wk_group,
            seq_name, actions, version,
            ins_user, ins_date, upd_user, upd_date
        ) values (
            #{model}, #{operation}, #{rtgCode}, (
                select lpad(nvl2(max(to_number(seq_no)), max(to_number(seq_no)) + 1, 1), 3, '0') as seqNo
                from ma_rtgd_pub
                where model_no = #{model}
                  and operation = #{operation}
                  and rtg_code = #{rtgCode}
            ), (
                select nvl2(max(skey), max(to_number(skey)) + 1, 1) as skey
                from ma_rtgd_pub
                where model_no = #{model}
                  and operation = #{operation}
                  and rtg_code = #{rtgCode}
            ), (
                select lpad(nvl2(max(to_number(wk_group)), max(to_number(wk_group)) + 1, 1), 3, '0') as wkGroup
                from ma_rtgd_pub
                where model_no = #{model}
                  and operation = #{operation}
                and rtg_code = #{rtgCode}
            ), #{actions}, #{actions}, 1,
            #{insUser}, sysdate, #{updUser}, sysdate
        )
    </insert>

    <!-- 获取最大工序编号 -->
    <select id="getMaxSeqNo" resultType="string">
        select lpad(nvl2(max(to_number(seq_no)), max(to_number(seq_no)), 1), 3, '0') as seqNo
        from ma_rtgd_pub
        where model_no = #{model}
          and operation = #{operation}
          and rtg_code = #{rtgCode}
    </select>
    
    <!-- 修改工序完成状态 -->
    <update id="updateCompleteState">
        update ma_rtgc_pub
        set cpl_flag = #{cplFlag},
            cpl_date = sysdate
        where model_no = #{model}
          and operation = #{operation}
          and rtg_code = #{rtgCode}
    </update>

    <!-- 获取创建人列表 -->
    <select id="getCreatorList" resultType="com.zqn.modeldata2.entity.sop.SopCreator">
        select user_no as userNo, (user_id || ': ' || user_desc) as userDesc
        from sy_user
    </select>

    <!-- 获取制程选项 -->
    <select id="getOperationOption" resultType="string">
        select distinct pro_seq
        from pcc_me_project_plan_actions
        order by pro_seq
    </select>

    <!-- 获取标签选项 -->
    <select id="getTagOption" resultType="com.zqn.modeldata2.entity.sop.SopTagOption">
        select distinct pro_seq as proSeq, tag
        from pcc_me_project_plan_actions
        order by pro_seq
    </select>

    <!-- 获取翻译型体列表 -->
    <select id="getTranslationModelList" resultType="string">
        select distinct model_no
        from ma_rtgc_pub
        <if test="isCompleted == true">
            where cpl_flag = 'Y'
        </if>
        order by model_no
    </select>

    <!-- 获取翻译制程列表 -->
    <select id="getTranslationOperationList" resultType="string">
        select distinct operation
        from ma_rtgc_pub
        <if test="isCompleted == true">
            where cpl_flag = 'Y'
        </if>
        order by operation
    </select>

    <!-- 获取翻译详细制程列表 -->
    <select id="getTranslationProSeqList" resultType="string">
        select distinct pro_seq
        from ma_rtgc_pub
        <if test="isCompleted == true">
            where cpl_flag = 'Y'
        </if>
        order by pro_seq
    </select>

    <!-- 获取需要翻译的工序列表 -->
    <select id="getTranslationList" resultType="com.zqn.modeldata2.entity.sop.SopTranslation">
        select *
        from (
            select rownum as rn, t.*
            from (
                select a.model_no, a.operation, a.pro_seq, a.rtg_code, a.seq_no, a.skey,
                       a.actions as action_cn, b.action_en, b.action_vi, b.action_id, b.action_bn,
                       b.standard_cn, b.standard_en, b.standard_vi, b.standard_id, b.standard_bn,
                       b.check_point_cn, b.check_point_en, b.check_point_vi, b.check_point_id, b.check_point_bn
                from (
                    select t1.model_no, t1.operation, t2.pro_seq, t1.rtg_code, t1.seq_no, t1.skey, t1.actions
                    from ma_rtgd_pub t1
                    left join ma_rtgc_pub t2
                    on t1.model_no = t2.model_no
                    and t1.operation = t2.operation
                    and t1.rtg_code = t2.rtg_code
                    where t1.actions is not null
                    <if test="isCompleted == true">
                        and t2.cpl_flag = 'Y'
                    </if>
                ) a left join (
                    select model_no, operation, rtg_code, seq_no,
                           action_en, action_vi, action_id, action_bn,
                           standard_cn, standard_en, standard_vi, standard_id, standard_bn,
                           check_point_cn, check_point_en, check_point_vi, check_point_id, check_point_bn
                    from (
                        select model_no, operation, rtg_code, seq_no, type || '_' || lang as type_lang, content
                        from pcc_sop_cont
                    ) t
                    pivot (
                        max(content)
                        for type_lang in (
                            '0_en' as action_en,
                            '0_vi' as action_vi,
                            '0_id' as action_id,
                            '0_bn' as action_bn,
                            '1_zh-Hans' as standard_cn,
                            '1_en' as standard_en,
                            '1_vi' as standard_vi,
                            '1_id' as standard_id,
                            '1_bn' as standard_bn,
                            '2_zh-Hans' as check_point_cn,
                            '2_en' as check_point_en,
                            '2_vi' as check_point_vi,
                            '2_id' as check_point_id,
                            '2_bn' as check_point_bn
                        )
                    )
                ) b
                on a.model_no = b.model_no
                and a.operation = b.operation
                and a.rtg_code = b.rtg_code
                and a.seq_no = b.seq_no
                <where>
                    <if test="model != null and model != ''">
                        and a.model_no = #{model}
                    </if>
                    <if test="operation != null and operation != ''">
                        and a.operation = #{operation}
                    </if>
                    <if test="proSeq != null and proSeq != ''">
                        and a.pro_seq = #{proSeq}
                    </if>
                    <if test="needTranslation == true">
                        and ((
                            b.action_en is null or
                            b.action_vi is null or
                            b.action_id is null or
                            b.action_bn is null
                        ) or (
                            b.standard_cn is not null and (
                                b.standard_en is null or
                                b.standard_vi is null or
                                b.standard_id is null or
                                b.standard_bn is null
                            )
                        ) or (
                            b.check_point_cn is not null and (
                                b.check_point_en is null or
                                b.check_point_vi is null or
                                b.check_point_id is null or
                                b.check_point_bn is null
                            )
                        ))
                    </if>
                </where>
                order by a.model_no, a.operation, a.rtg_code, a.skey
            ) t
        )
        where rn &gt; (#{pageNo} - 1) * #{pageSize}
          and rn &lt;= #{pageNo} * #{pageSize}
    </select>

    <!-- 获取已完成待翻译数量 -->
    <select id="getTranslationCount" resultType="integer">
        select count(*)
        from (
            select t1.model_no, t1.operation, t2.pro_seq, t1.rtg_code, t1.seq_no, t1.skey, t1.actions
            from ma_rtgd_pub t1
            left join ma_rtgc_pub t2
            on t1.model_no = t2.model_no
            and t1.operation = t2.operation
            and t1.rtg_code = t2.rtg_code
            where t1.actions is not null
              and t2.cpl_flag = 'Y'
        ) a left join (
            select model_no, operation, rtg_code, seq_no,
            action_en, action_vi, action_id, action_bn,
            standard_cn, standard_en, standard_vi, standard_id, standard_bn,
            check_point_cn, check_point_en, check_point_vi, check_point_id, check_point_bn
            from (
                select model_no, operation, rtg_code, seq_no, type || '_' || lang as type_lang, content
                from pcc_sop_cont
            ) t
            pivot (
                max(content)
                for type_lang in (
                    '0_en' as action_en,
                    '0_vi' as action_vi,
                    '0_id' as action_id,
                    '0_bn' as action_bn,
                    '1_zh-Hans' as standard_cn,
                    '1_en' as standard_en,
                    '1_vi' as standard_vi,
                    '1_id' as standard_id,
                    '1_bn' as standard_bn,
                    '2_zh-Hans' as check_point_cn,
                    '2_en' as check_point_en,
                    '2_vi' as check_point_vi,
                    '2_id' as check_point_id,
                    '2_bn' as check_point_bn
                )
            )
        ) b
        on a.model_no = b.model_no
        and a.operation = b.operation
        and a.rtg_code = b.rtg_code
        and a.seq_no = b.seq_no
        and ((
            b.action_en is null or
            b.action_vi is null or
            b.action_id is null or
            b.action_bn is null
        ) or (
            b.standard_cn is not null and (
                b.standard_en is null or
                b.standard_vi is null or
                b.standard_id is null or
                b.standard_bn is null
            )
        ) or (
            b.check_point_cn is not null and (
                b.check_point_en is null or
                b.check_point_vi is null or
                b.check_point_id is null or
                b.check_point_bn is null
            )
        ))
    </select>

    <!-- 修改工序翻译 -->
    <update id="updateTranslation">
        update pcc_sop_cont
        set content = #{content}
        where model_no = #{model_no}
          and operation = #{operation}
          and rtg_code = #{rtg_code}
          and seq_no = #{seq_no}
          and type = #{type}
          and lang = #{lang}
    </update>

    <!-- 添加工序翻译 -->
    <insert id="addTranslation">
        insert into pcc_sop_cont (
            model_no, operation, rtg_code, seq_no, content, lang, type
        ) values (
            #{model_no}, #{operation}, #{rtg_code}, #{seq_no}, #{content}, #{lang}, #{type}
        )
    </insert>

    <!-- 获取热冷压规格列表 -->
    <select id="getPressList" resultType="com.zqn.modeldata2.entity.sop.SopPress">
        select model_no, operation, rtg_code, press_type,
               component_name, vendor, process_condition,
               top_thickness, top_hardness, top_color, top_change_frequency, top_mc_set_temp, top_actual_temp,
               bottom_thickness, bottom_hardness, bottom_color, bottom_change_frequency, bottom_mc_set_temp, bottom_actual_temp,
               time, size1, size2, size3, size4, size5, size6, size7,
               mc_setting1, mc_setting2, mc_setting3, mc_setting4, mc_setting5, mc_setting6, mc_setting7,
               load_cell1, load_cell2, load_cell3, load_cell4, load_cell5, load_cell6, load_cell7,
               probe_location_image, probe_location_remark, ins_user, ins_date, upd_user, upd_date
        from ma_rtgspc_pub
        where model_no = #{model_no}
          and operation = #{operation}
          and rtg_code = #{rtg_code}
        order by press_type
    </select>

    <!-- 添加热冷压规格 -->
    <insert id="addPress">
        insert into ma_rtgspc_pub (
            model_no, operation, rtg_code, press_type,
            component_name, vendor, process_condition,
            top_thickness, top_hardness, top_color, top_change_frequency, top_mc_set_temp, top_actual_temp,
            bottom_thickness, bottom_hardness, bottom_color, bottom_change_frequency, bottom_mc_set_temp, bottom_actual_temp,
            time, size1, size2, size3, size4, size5, size6, size7,
            mc_setting1, mc_setting2, mc_setting3, mc_setting4, mc_setting5, mc_setting6, mc_setting7,
            load_cell1, load_cell2, load_cell3, load_cell4, load_cell5, load_cell6, load_cell7,
            probe_location_image, probe_location_remark, ins_user, ins_date, upd_user, upd_date
        ) values (
            #{model_no}, #{operation}, #{rtg_code}, #{press_type},
            #{component_name}, #{vendor}, #{process_condition},
            #{top_thickness}, #{top_hardness}, #{top_color}, #{top_change_frequency}, #{top_mc_set_temp}, #{top_actual_temp},
            #{bottom_thickness}, #{bottom_hardness}, #{bottom_color}, #{bottom_change_frequency}, #{bottom_mc_set_temp}, #{bottom_actual_temp},
            #{time}, #{size1}, #{size2}, #{size3}, #{size4}, #{size5}, #{size6}, #{size7},
            #{mc_setting1}, #{mc_setting2}, #{mc_setting3}, #{mc_setting4}, #{mc_setting5}, #{mc_setting6}, #{mc_setting7},
            #{load_cell1}, #{load_cell2}, #{load_cell3}, #{load_cell4}, #{load_cell5}, #{load_cell6}, #{load_cell7},
            #{probe_location_image}, #{probe_location_remark}, #{ins_user}, sysdate, #{upd_user}, sysdate
        )
    </insert>

    <!-- 修改冷热压规格部位 -->
    <update id="updatePressPart">
        update ma_rtgspc_pub
        set component_name = #{partName}
        where model_no = #{model}
          and operation = #{operation}
          and rtg_code = #{rtgCode}
    </update>

    <!-- 修改热冷压规格 -->
    <update id="updatePress">
        update ma_rtgspc_pub
        set component_name = #{component_name},
            vendor = #{vendor},
            process_condition = #{process_condition},
            top_thickness = #{top_thickness},
            top_hardness = #{top_hardness},
            top_color = #{top_color},
            top_change_frequency = #{top_change_frequency},
            top_mc_set_temp = #{top_mc_set_temp},
            top_actual_temp = #{top_actual_temp},
            bottom_thickness = #{bottom_thickness},
            bottom_hardness = #{bottom_hardness},
            bottom_color = #{bottom_color},
            bottom_change_frequency = #{bottom_change_frequency},
            bottom_mc_set_temp = #{bottom_mc_set_temp},
            bottom_actual_temp = #{bottom_actual_temp},
            time = #{time},
            size1 = #{size1},
            size2 = #{size2},
            size3 = #{size3},
            size4 = #{size4},
            size5 = #{size5},
            size6 = #{size6},
            size7 = #{size7},
            mc_setting1 = #{mc_setting1},
            mc_setting2 = #{mc_setting2},
            mc_setting3 = #{mc_setting3},
            mc_setting4 = #{mc_setting4},
            mc_setting5 = #{mc_setting5},
            mc_setting6 = #{mc_setting6},
            mc_setting7 = #{mc_setting7},
            load_cell1 = #{load_cell1},
            load_cell2 = #{load_cell2},
            load_cell3 = #{load_cell3},
            load_cell4 = #{load_cell4},
            load_cell5 = #{load_cell5},
            load_cell6 = #{load_cell6},
            load_cell7 = #{load_cell7},
            probe_location_image = #{probe_location_image},
            probe_location_remark = #{probe_location_remark},
            upd_user = #{upd_user},
            upd_date = sysdate
        where model_no = #{model_no}
          and operation = #{operation}
          and rtg_code = #{rtg_code}
          and press_type = #{press_type}
    </update>

    <!-- 备份热冷压规格 -->
    <insert id="addBackupPress">
        insert into pcc_sop_backup_press (
            model_no, operation, rtg_code, press_type,
            component_name, vendor, process_condition,
            top_thickness, top_hardness, top_color, top_change_frequency, top_mc_set_temp, top_actual_temp,
            bottom_thickness, bottom_hardness, bottom_color, bottom_change_frequency, bottom_mc_set_temp, bottom_actual_temp,
            time, size1, size2, size3, size4, size5, size6, size7,
            mc_setting1, mc_setting2, mc_setting3, mc_setting4, mc_setting5, mc_setting6, mc_setting7,
            load_cell1, load_cell2, load_cell3, load_cell4, load_cell5, load_cell6, load_cell7,
            probe_location_image, probe_location_remark, ins_user, ins_date, upd_user, upd_date, delete_time
        ) values (
            #{model_no}, #{operation}, #{rtg_code}, #{press_type},
            #{component_name}, #{vendor}, #{process_condition},
            #{top_thickness}, #{top_hardness}, #{top_color}, #{top_change_frequency}, #{top_mc_set_temp}, #{top_actual_temp},
            #{bottom_thickness}, #{bottom_hardness}, #{bottom_color}, #{bottom_change_frequency}, #{bottom_mc_set_temp}, #{bottom_actual_temp},
            #{time}, #{size1}, #{size2}, #{size3}, #{size4}, #{size5}, #{size6}, #{size7},
            #{mc_setting1}, #{mc_setting2}, #{mc_setting3}, #{mc_setting4}, #{mc_setting5}, #{mc_setting6}, #{mc_setting7},
            #{load_cell1}, #{load_cell2}, #{load_cell3}, #{load_cell4}, #{load_cell5}, #{load_cell6}, #{load_cell7},
            #{probe_location_image}, #{probe_location_remark}, #{ins_user}, #{ins_date}, #{upd_user}, #{upd_date}, sysdate
        )
    </insert>

    <!-- 删除热冷压规格 -->
    <delete id="deletePress">
        delete from ma_rtgspc_pub
        where
        <foreach collection="processInfoList" item="processInfo" separator="or">
            (
                model_no = #{processInfo.model} and
                operation = #{processInfo.operation} and
                rtg_code = #{processInfo.rtgCode}
            )
        </foreach>
    </delete>

    <!-- 复制热冷压规格 -->
    <insert id="copyPress">
        insert into ma_rtgspc_pub (
            model_no, operation, rtg_code, press_type,
            component_name, vendor, process_condition,
            top_thickness, top_hardness, top_color, top_change_frequency, top_mc_set_temp, top_actual_temp,
            bottom_thickness, bottom_hardness, bottom_color, bottom_change_frequency, bottom_mc_set_temp, bottom_actual_temp,
            time, size1, size2, size3, size4, size5, size6, size7,
            mc_setting1, mc_setting2, mc_setting3, mc_setting4, mc_setting5, mc_setting6, mc_setting7,
            load_cell1, load_cell2, load_cell3, load_cell4, load_cell5, load_cell6, load_cell7,
            probe_location_image, probe_location_remark, ins_user, ins_date, upd_user, upd_date
        )
        select #{targetModel} as model_no, operation, #{targetRtgCode} as rtg_code, press_type,
               component_name, vendor, process_condition,
               top_thickness, top_hardness, top_color, top_change_frequency, top_mc_set_temp, top_actual_temp,
               bottom_thickness, bottom_hardness, bottom_color, bottom_change_frequency, bottom_mc_set_temp, bottom_actual_temp,
               time, size1, size2, size3, size4, size5, size6, size7,
               mc_setting1, mc_setting2, mc_setting3, mc_setting4, mc_setting5, mc_setting6, mc_setting7,
               load_cell1, load_cell2, load_cell3, load_cell4, load_cell5, load_cell6, load_cell7,
               <if test="isCopyPicture == true">
                   probe_location_image,
               </if>
               <if test="isCopyPicture == false">
                   '' as probe_location_image,
               </if>
               probe_location_remark, ins_user, sysdate, upd_user, sysdate
        from ma_rtgspc_pub
        where model_no = #{model}
          and operation = #{operation}
          and rtg_code = #{rtgCode}
    </insert>
</mapper>