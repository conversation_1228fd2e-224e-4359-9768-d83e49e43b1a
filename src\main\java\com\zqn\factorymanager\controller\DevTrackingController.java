package com.zqn.factorymanager.controller;

import com.github.pagehelper.PageInfo;
import com.zqn.factorymanager.entity.DevTracking;
import com.zqn.factorymanager.service.DevTrackingService;
import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.entity.CutProGress;
import com.zqn.newmodel.entity.NewModel;
import com.zqn.newmodel.service.NewModelService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:新型体指令单
 * @date 2024/8/6 8:02
 */
@RestController
@RequestMapping("/devTracking")
public class DevTrackingController {

    @Resource
    private DevTrackingService devTrackingService;

    @GetMapping("/query")
    public R<PageInfo<DevTracking>> query(@RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                          @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
                                          @RequestParam(value = "startTime", required = false) long startTimeTamp,
                                          @RequestParam(value = "endTime", required = false) long endTimeTamp,
                                          @RequestParam(value = "type") Integer type) {
        Date startTime = new Date(startTimeTamp); // 将时间戳转换为Date对象
        Date endTime = new Date(endTimeTamp); // 将时间戳转换为Date对象
        return devTrackingService.query(pageNo, pageSize, startTime, endTime, type);
    }

    //修改A级师傅
    @PostMapping("/updateBrandFn")
    public R<Void> updateBrandFn(@RequestBody DevTracking devTracking) {
        return devTrackingService.updateBrandFn(devTracking);
    }

}
