package com.zqn.studentrecord.service;

import com.zqn.modeldata2.common.R;
import com.zqn.studentrecord.entity.StudentRecord;

import java.util.Date;
import java.util.List;

/**
 * 学员记录服务接口
 */
public interface StudentRecordService {
    
    /**
     * 创建学员记录
     * @param record 学员记录
     * @return 操作结果
     */
    R<StudentRecord> createRecord(StudentRecord record);
    
    /**
     * 根据ID获取学员记录
     * @param id 记录ID
     * @return 学员记录
     */
    R<StudentRecord> getRecordById(Long id);
    
    /**
     * 更新学员记录
     * @param id 记录ID
     * @param record 学员记录
     * @return 操作结果
     */
    R<StudentRecord> updateRecord(Long id, StudentRecord record);
    
    /**
     * 删除学员记录
     * @param id 记录ID
     * @return 操作结果
     */
    R<Void> deleteRecord(Long id);
    
    /**
     * 分页查询学员记录列表
     * @param page 页码（从1开始）
     * @param size 每页大小
     * @param orderNo 单号（可选）
     * @param student 学员（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 分页结果
     */
    R<PageResult<StudentRecord>> getRecordList(int page, int size, String orderNo, String student,
                                              Date startDate, Date endDate);
    
    /**
     * 根据单号获取鞋图
     *
     * @param orderNo 单号
     * @return 鞋图信息
     */
    R<StudentRecord> getShoeImageByOrderNo(String orderNo);
    
    /**
     * 分页结果包装类
     */
    class PageResult<T> {
        private List<T> records;
        private long total;
        private int page;
        private int size;
        private long pages;
        
        public PageResult() {}
        
        public PageResult(List<T> records, long total, int page, int size) {
            this.records = records;
            this.total = total;
            this.page = page;
            this.size = size;
            this.pages = (total + size - 1) / size;
        }
        
        // Getters and Setters
        public List<T> getRecords() {
            return records;
        }
        
        public void setRecords(List<T> records) {
            this.records = records;
        }
        
        public long getTotal() {
            return total;
        }
        
        public void setTotal(long total) {
            this.total = total;
        }
        
        public int getPage() {
            return page;
        }
        
        public void setPage(int page) {
            this.page = page;
        }
        
        public int getSize() {
            return size;
        }
        
        public void setSize(int size) {
            this.size = size;
        }
        
        public long getPages() {
            return pages;
        }
        
        public void setPages(long pages) {
            this.pages = pages;
        }
    }
} 