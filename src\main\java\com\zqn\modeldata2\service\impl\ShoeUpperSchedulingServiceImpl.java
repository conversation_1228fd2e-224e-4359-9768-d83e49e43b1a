package com.zqn.modeldata2.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.zqn.modeldata2.entity.*;
import com.zqn.modeldata2.mapper.ShoeUpperSchedulingMapper;
import com.zqn.modeldata2.service.ShoeUpperSchedulingService;
import org.apache.xmlbeans.impl.regex.REUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ShoeUpperSchedulingServiceImpl implements ShoeUpperSchedulingService {


    @Resource
    private ShoeUpperSchedulingMapper mapper;


    @Override
    public List<ShoeUpperScheduling> list(String empName, String  beginShpDate, String endShpDate, String prdDate) {
        List<ShoeUpperScheduling> list = mapper.list(empName, beginShpDate,endShpDate,  prdDate);
        if (CollectionUtil.isEmpty(list)) {
            return Collections.emptyList();
        }

        list = list.stream().map(item -> {
            String detail = mapper.detail(item.getMade_dept(), prdDate);
            item.setBar_qty2(detail);
            return item;

        }).collect(Collectors.toList());


        return  list.stream()
                .peek(item -> {
                    byte[] bytes = item.getModel_pic();
                    if (bytes != null) {
                        item.setModel_pic(null);
                        String base64 = Base64.getEncoder().encodeToString(bytes);
                        item.setPic("data:image/png;base64," + base64);
                    }
                })
                .collect(Collectors.groupingBy(ShoeUpperScheduling::getEmp_name))
                .entrySet()
                .stream()
                .map(entry -> {
                    Double totalBarQty = entry.getValue().stream()
                            .mapToDouble(ShoeUpperScheduling::getBar_qty)
                            .sum();
                    entry.getValue().forEach(item -> item.setTotal(totalBarQty));
                    return entry.getValue();
                })
                .flatMap(List::stream)
                .collect(Collectors.toList());


    }
}
