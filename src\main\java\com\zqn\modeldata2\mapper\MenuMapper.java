package com.zqn.modeldata2.mapper;

import com.zqn.modeldata2.entity.Menu;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MenuMapper {
    List<Menu> findByPart(@Param("partNo") String partNo);

    List<Menu> findAllMenu();

    List<Menu> findCommonyUsedMenuByUser(@Param("userNo") String userNo);

    Integer addCommonyUsed(@Param("menu") Menu menu);

    Integer deleteByUser(@Param("userNo") String userNo, @Param("delNos") List<Integer> delNos);

    List<Menu> findMenuByUser(String userNo);

    Integer deleteCommyUsedByUser(@Param("userNo") String userNo, @Param("delNos") List<Integer> hasMenuNo);

    List<Menu> findReportByUser(@Param("userNo") String userNo);

    List<Menu> findSecondMenuByUser(@Param("menu") Menu menu, @Param("userNo") String userNo);

    List<Menu> buttonQuery(String url, String loginUser);
}