package com.zqn.modeldata2.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/7/22 15:23
 */
@Data
public class MaterialMatchDt {
    //--倉位
    private String mat_type;

    //出貨日期
    @JsonFormat(pattern = "YY/MM/dd")
    private Date sh_date;

    //--樣品單號
    private String ord_no;

    //--品牌簡稱
    private String brand_no;

    //--季節序號
    private String season_no;

    //--階段
    private String phase;

    //--樣品類型
    private String dev_type;

    //--品牌料號
    private String mat_seq;

    //--材料描述
    private String mat_desc;

    //--需求數量
    private String req_qty;

    //--庫存單位
    private String suom;

    //--庫存數量
    private String bat_qty;

    //--存放架位
    private String shelf_pos;

    //--交貨日期
    @JsonFormat(pattern = "YY/MM/dd")
    private Date give_cdate;

    //--驗收日期
    @JsonFormat(pattern = "YY/MM/dd")
    private Date str_date;
}
