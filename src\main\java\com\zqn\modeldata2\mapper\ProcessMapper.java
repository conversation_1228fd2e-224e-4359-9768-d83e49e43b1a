package com.zqn.modeldata2.mapper;

import com.zqn.modeldata2.entity.CopyProcess;
import com.zqn.modeldata2.entity.ProcessInfo;
import com.zqn.modeldata2.entity.ProcessStep;
import com.zqn.modeldata2.entity.ProcessTemplate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface ProcessMapper {
    List<Integer> getOperation();

    List<String> getBrand();

    List<String> getModel(@Param("brand_no") String brand_no);

    Map<String, Object> getLast(@Param("model_no") String model_no);

    String getCode(@Param("model_no") String model_no, @Param("operation") String operation);

    Map<String, Object> getPicture(@Param("model_no") String model_no);

    List<String> getType(@Param("operation") String operation);

    List<String> getMaterial();

    List<ProcessInfo> getProcessInfo(@Param("processInfo") ProcessInfo processInfo);

    Integer addProcessInfo(@Param("processInfo") ProcessInfo processInfo);

    Integer updateProcessInfo(@Param("processInfo") ProcessInfo processInfo);

    Integer deleteProcessInfo(@Param("processInfo") ProcessInfo processInfo);

    Integer batchDeleteProcessInfo(@Param("processInfoList") List<ProcessInfo> processInfoList);

    String getNo(@Param("processStep") ProcessStep processStep);

    String getKey(@Param("processStep") ProcessStep processStep);

    List<ProcessTemplate> getTemplate(@Param("operation") String operation);

    List<ProcessStep> getTemplateStep(@Param("doc_no") String doc_no);

    Integer templateImport(@Param("processStepList") List<ProcessStep> processStepList);

    List<ProcessStep> getProcessStep(@Param("processStep") ProcessStep processStep);

    Integer addProcessStep(@Param("processStep") ProcessStep processStep);

    Integer updateProcessStep(@Param("processStep") ProcessStep processStep);

    Integer deleteProcessStep(@Param("processStep") ProcessStep processStep);

    Integer batchDeleteProcessStep(@Param("processStepList") List<ProcessStep> processStepList);

    List<String> getModelList();

    Integer copyProcessInfo(@Param("copyProcess") CopyProcess copyProcess);

    Integer copyProcessStep(@Param("copyProcess") CopyProcess copyProcess);
}