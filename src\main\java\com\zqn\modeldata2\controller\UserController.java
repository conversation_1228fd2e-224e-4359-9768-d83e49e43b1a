package com.zqn.modeldata2.controller;

import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.entity.UserDto;
import com.zqn.modeldata2.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/user")
public class UserController {
    @Autowired
    private UserService userService;


    @GetMapping("getUser")
    public R<List<UserDto>> getUser(@RequestParam("userNo") String userNo) {
        return R.success(userService.getUser(userNo));
    }
}
