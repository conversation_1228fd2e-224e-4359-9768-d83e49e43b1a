package com.zqn.sop.vo;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.zqn.sop.entity.PccMeProjectPlanDt;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:ME工程编制明细表
 * @date 2024/5/8 8:36
 */
@Data
public class PccMeProjectPlanDtVo extends PccMeProjectPlanDt {

    private List<JSONObject> items;

    private List<JSONObject> versions;

    //操作流程
    private String op_std;

    //自检点
    private String self_check_points;

    //操作流程
    private String op_std_en;

    //自检点
    private String self_check_points_en;

    //操作流程
    private String op_std_vn;

    //自检点
    private String self_check_points_vn;

    //操作流程
    private String op_std_id;

    //自检点
    private String self_check_points_id;

    //操作流程
    private String op_std_bd;

    //自检点
    private String self_check_points_bd;

    //操作流程
    private String op_std_ph;

    //自检点
    private String self_check_points_ph;

    //该记录对应的语言
    private String lang;

    //制鞋排头
    private String shoe_make_head;

    //版师
    private String printmaker;

    //高级技师
    private String senior_technician;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> type1Imgs;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private  List<String> type2Imgs;

    private String createDate;
}
