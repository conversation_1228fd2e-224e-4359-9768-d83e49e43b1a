package com.zqn.sop.controller;

import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.zqn.modeldata2.common.R;
import com.zqn.sop.entity.PccMeProjectPlanHd;
import com.zqn.sop.service.PccMeProjectPlanHdService;
import com.zqn.sop.vo.PccMeProjectPlanCopyVo;
import com.zqn.sop.vo.PccMeProjectPlanDtVo;
import com.zqn.sop.vo.PccMeProjectPlanHdVo;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/5/8 10:02
 */
@RestController
@RequestMapping("/pccmeprjplanhd")
@Validated
public class PccMeProjectPlanHdController {

    @Resource
    private PccMeProjectPlanHdService pccMeProjectPlanHdService;

    @GetMapping("/query")
    public R<PageInfo<PccMeProjectPlanHd>> query(@RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                 @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
                                                 @RequestParam(value = "model", required = false) String model,
                                                 @RequestParam(value = "dept", required = false) String dept,
                                                 @RequestParam(value = "brand", required = false) String brand,
                                                 @RequestParam(value = "loginUser") String loginUser,
                                                 //用户的厂别
                                                 @RequestParam(value = "userFactory",required = false,defaultValue = "") String userFactory,
                                                 //选择的厂别
                                                 @RequestParam(value = "selectFactory",required = false,defaultValue = "") String selectFactory

    ) {

        if(selectFactory.equals("所有廠別")){
            selectFactory = "ALL";
        }

        if (StrUtil.isNotEmpty(dept)) {
            if (StrUtil.equals(dept, "所有部門")) {
                dept = null;
            }
        }

        if (StrUtil.isNotEmpty(brand)) {
            if (StrUtil.equals(brand, "所有品牌")) {
                brand = null;
            }
        }
        PageInfo<PccMeProjectPlanHd> result = pccMeProjectPlanHdService.query(pageNo, pageSize, model, dept, brand, loginUser,userFactory,selectFactory);
        return R.success(result);
    }


    @GetMapping("/queryFactory")
    public R queryFactory(){
        System.out.println("111");
        List<String> allPlanFactory = pccMeProjectPlanHdService.findAllPlanFactory();
        return R.success(allPlanFactory);
    }



    /**
     * @description:
     * @param: model
     * @return: com.zqn.modeldata2.common.R<com.github.pagehelper.PageInfo < com.zqn.sop.entity.PccMeProjectPlanHd>>
     * <AUTHOR> Yang
     * @date: 2024/5/9 10:43
     */
    @GetMapping("/queryModel")
    public R<List<PccMeProjectPlanHd>> queryModel(@RequestParam(value = "model", required = false) String model) {
        List<PccMeProjectPlanHd> result = pccMeProjectPlanHdService.queryModel(model);
        return R.success(result);
    }

    @GetMapping("/queryShoeLastByModelNo")
    public R<List<String>> queryShoeLastByModelNo(@RequestParam(value = "modelNo", required = true) String model,
                                                  @RequestParam(value = "brand", required = true) String brand) {
        List<String> result = pccMeProjectPlanHdService.queryShoeLastByModelNo(model, brand);
        return R.success(result);
    }

    @GetMapping("/selectMax")
    public R<Integer> selectMax(@RequestParam(value = "modelNo", required = true) String model,
                                @RequestParam(value = "dept", required = true) String dept,
                                @RequestParam(value = "factory", required = true) String factory
                                ) {
        Integer result = pccMeProjectPlanHdService.selectMax(model, dept,factory);
        return R.success(result);
    }


    @PostMapping("create")
    public R<Integer> create(@RequestBody PccMeProjectPlanHdVo vo) throws Exception {
        int result = pccMeProjectPlanHdService.create(vo);
        return R.success(result);
    }

    @PostMapping("update")
    public R<Integer> update(@RequestBody PccMeProjectPlanHdVo vo) throws Exception {
        int result = pccMeProjectPlanHdService.update(vo);
        return R.success(result);
    }

    @DeleteMapping("delete")
    public R<Integer> delete(@RequestBody String ids) throws Exception {
        List<Integer> idList = Arrays.stream(ids.split(","))
                .map(Integer::parseInt)
                .collect(Collectors.toList());
        Integer result = 0;
        for (Integer id : idList) {
            result = pccMeProjectPlanHdService.delete(id);
        }
        return R.success(result);
    }

    @PostMapping("copy")
    public R<Integer> copy(@RequestBody PccMeProjectPlanCopyVo vo) throws Exception {
        List<String> shoeList = pccMeProjectPlanHdService.queryShoeLastByModelNo(vo.getModel(), vo.getBrand());
        if (CollectionUtils.isEmpty(shoeList)) {
            throw new Exception("未找到楦头编号！");
        }
        if (vo.getModel().equals(vo.getSourceModel())) {
            throw new Exception("不能复制相同型体");
        }
        vo.setShoeLost(shoeList.get(0));
        int result = pccMeProjectPlanHdService.copy(vo);
        return R.success(result);
    }

    @PostMapping("audit")
    public R<Integer> audit(@RequestBody PccMeProjectPlanHdVo vo) throws Exception {
        return R.success(pccMeProjectPlanHdService.audit(vo));
    }

    @PostMapping("revAudit")
    public R<Integer> revAudit(@RequestBody PccMeProjectPlanHdVo vo) throws Exception {
        return R.success(pccMeProjectPlanHdService.revAudit(vo));
    }

}
