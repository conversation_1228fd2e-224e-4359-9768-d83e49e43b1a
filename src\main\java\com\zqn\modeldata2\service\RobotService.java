package com.zqn.modeldata2.service;

import org.apache.http.HttpEntity;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;

@Service
public class RobotService {


    public String  getRobotInfo() throws Exception{
        String gsessionid = getSessionId("https://navi.rmbot.cn/openapispring/user/login", "dgxx", "123456a");
        String response = getData("https://navi.rmbot.cn/openapispring/deliveryrobots/sum", gsessionid);
//        System.out.println(response);
        return  response;
    }




    private  String getSessionId(String loginUrl, String account, String password) throws IOException {
        HttpClient client = HttpClients.createDefault();
        HttpPost postRequest = new HttpPost(loginUrl);
        StringEntity input = new StringEntity("{\"account\":\"" + account + "\",\"password\":\"" + password + "\"}");
        input.setContentType("application/json");
        postRequest.setEntity(input);
        // Set headers
        postRequest.setHeader(HttpHeaders.ACCEPT, "/");
        postRequest.setHeader(HttpHeaders.ACCEPT_LANGUAGE, "zh-CN,zh;q0.9");
        postRequest.setHeader(HttpHeaders.CONTENT_TYPE, "application/json");
        postRequest.setHeader("Origin", "https://navi.rmbot.cn");
        postRequest.setHeader(HttpHeaders.USER_AGENT, "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.36");
        postRequest.setHeader("X-Requested-With", "XMLHttpRequest");

        HttpResponse postResponse = client.execute(postRequest);
        String gsessionid = "";
        String setCookie = postResponse.getFirstHeader("Set-Cookie").getValue();
        if (setCookie != null) {
            String[] cookies = setCookie.split(";");
            for (String cookie : cookies) {
                if (cookie.trim().startsWith("GSESSIONID")) {
                    gsessionid = cookie.split("=")[1];
                    break;
                }
            }
        }
        return gsessionid;
    }

    private  String getData(String getUrl, String gsessionid) throws IOException {
        HttpClient client = HttpClients.createDefault();
        HttpGet getRequest = new HttpGet(getUrl);
        // Set headers
        getRequest.setHeader(HttpHeaders.ACCEPT, "*/*");
        getRequest.setHeader(HttpHeaders.ACCEPT_LANGUAGE, "zh-CN,zh;q=0.9");
        getRequest.setHeader("Cookie", "GSESSIONID=" + gsessionid);
        getRequest.setHeader(HttpHeaders.REFERER, "https://navi.rmbot.cn/");
        getRequest.setHeader(HttpHeaders.USER_AGENT, "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36");
        getRequest.setHeader("X-Requested-With", "XMLHttpRequest");

        HttpResponse getResponse = client.execute(getRequest);
        HttpEntity entity = getResponse.getEntity();
        return EntityUtils.toString(entity);
    }

}