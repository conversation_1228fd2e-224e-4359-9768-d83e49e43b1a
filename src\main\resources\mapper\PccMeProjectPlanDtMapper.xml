<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zqn.sop.mapper.PccMeProjectPlanDtMapper">

    <insert id="add" parameterType="com.zqn.sop.entity.PccMeProjectPlanDt" useGeneratedKeys="true"
            keyProperty="id" keyColumn="ID">
        insert into PCC_ME_PROJECT_PLAN_DT (PARENT_ID, ITEM_NO, ACTIONS, TOOLS, MARGIN, REMARK, CREATE_BY, CREATE_DATE,
                                            UPDATE_BY, UPDATE_DATE, MACHINE, TEMP, PRESSURE, GLUE, CAR_LINE,
                                            CHEMICAL_SUBSTANCE, NEEDLE_SPACING, SPACING, NEEDLE, img_tit1, img_tit2,
                                            TIME, VERSION, protective_gear, speed)
        values (#{dt.parent_id},
                #{dt.item_no},
                #{dt.actions},
                #{dt.tools},
                #{dt.margin},
                #{dt.remark},
                #{dt.create_by},
                #{dt.create_date},
                #{dt.update_by},
                #{dt.update_date},
                #{dt.machine},
                #{dt.temp},
                #{dt.pressure},
                #{dt.glue},
                #{dt.car_line},
                #{dt.chemical_substance},
                #{dt.needle_spacing},
                #{dt.spacing},
                #{dt.needle},
                #{dt.img_tit1},
                #{dt.img_tit2},
                #{dt.time},
                #{dt.version},
                #{dt.protective_gear},
                #{dt.speed})
    </insert>

    <select id="query" resultType="com.zqn.sop.vo.PccMeProjectPlanDtVo">
        select a.id,
               a.parent_id,
               a.item_no,
               a.tools,
               a.actions,
               a.margin,
               a.remark,
               a.machine,
               a.temp,
               a.pressure,
               a.glue,
               a.car_line,
               a.chemical_substance,
               a.needle_spacing,
               a.spacing,
               a.needle,
               a.img_tit1,
               a.img_tit2,
               a.time,
               a.create_by,
               a.create_date,
               a.update_by,
               a.update_date,
               a.protective_gear,
               a.version,
               a.speed
        from PCC_ME_PROJECT_PLAN_DT a
                 left join PCC_ME_PROJECT_PLAN_HD b on a.parent_id = b.id
        where  a.parent_id = #{parentId}
    </select>

    <select id="queryTools" resultType="java.lang.String">
        select tool_name from PCC_ME_PROJECT_PLAN_TOOL
        WHERE id IN
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="queryImg" resultType="com.zqn.sop.entity.PccMeProjectPlanImg">
        select img_url, id, type
        from PCC_ME_PROJECT_PLAN_IMG
        where parent_id = #{id}
          and type = 1
    </select>

    <select id="queryByItem" resultType="com.zqn.sop.vo.PccMeProjectPlanDtVo">
        SELECT a.id,
        a.parent_id,
        a.item_no,
        a.tools,
        a.actions,
        a.margin,
        a.remark,
        a.machine,
        a.temp,
        a.pressure,
        a.glue,
        a.car_line,
        a.chemical_substance,
        a.needle_spacing,
        a.spacing,
        a.needle,
        a.img_tit1,
        a.img_tit2,
        a.time,
        a.create_by,
        a.create_date,
        a.update_by,
        a.update_date,
        a.protective_gear,
        a.version,
        a.speed,
        b.shoe_make_head,
        b.printmaker,
        b.senior_technician
        FROM PCC_ME_PROJECT_PLAN_DT a
        LEFT JOIN PCC_ME_PROJECT_PLAN_HD b ON a.parent_id = b.id
        WHERE a.parent_id = #{parentId}
        AND a.item_no = #{item}
        <if test="version != null and version != ''">
            and a.version = #{version}
        </if>
        <if test="version == null or version == ''">
            AND a.version = (SELECT MAX(a2.version)
            FROM PCC_ME_PROJECT_PLAN_DT a2
            WHERE a2.item_no = a.item_no
            AND a2.parent_id = a.parent_id)
        </if>
    </select>

    <select id="queryAllDt" resultType="com.zqn.sop.vo.PccMeProjectPlanDtVo">
        SELECT a.id,
               a.parent_id,
               a.item_no,
               a.tools,
               a.actions,
               a.margin,
               a.remark,
               a.machine,
               a.temp,
               a.pressure,
               a.glue,
               a.car_line,
               a.chemical_substance,
               a.needle_spacing,
               a.spacing,
               a.needle,
               a.img_tit1,
               a.img_tit2,
               a.time,
               a.create_by,
               a.create_date,
               a.update_by,
               a.update_date,
               a.protective_gear,
               a.version,
               a.speed,
               b.shoe_make_head,
               b.printmaker,
               b.senior_technician
        FROM PCC_ME_PROJECT_PLAN_DT a
                 LEFT JOIN PCC_ME_PROJECT_PLAN_HD b ON a.parent_id = b.id
        WHERE a.parent_id = #{parentId}
          AND a.version = (SELECT MAX(a2.version)
                           FROM PCC_ME_PROJECT_PLAN_DT a2
                           WHERE a2.item_no = a.item_no
                             AND a2.parent_id = a.parent_id)
        order by ITEM_NO
    </select>

    <select id="queryAllTools" resultType="java.lang.String">
        select distinct content as tool_name from PCC_SOP_CONT
        <where>
            <if test="content != null and content != ''">
                AND content LIKE '%' || #{content} || '%'
            </if>
            <if test="dept != null and dept != ''">
                AND dept = #{dept}
            </if>
            and type = #{searchType} and action_id = 0 and parent_id = 0
        </where>
    </select>

    <!--    查询最大项次-->
    <select id="selectMax" resultType="java.lang.Integer">
        select max(ITEM_NO)
        from PCC_ME_PROJECT_PLAN_DT a
                 left join PCC_ME_PROJECT_PLAN_HD b on a.parent_id = b.id
        where b.model = #{model}
        and b.dept = #{dept}
        <if test="factory != null and factory != ''">
            AND b.factory = #{factory}
        </if>
    </select>

    <!--    查询型体的所有项次-->
    <select id="queryAllItem" resultType="java.lang.Integer">
        SELECT A.ITEM_NO
        FROM PCC_ME_PROJECT_PLAN_DT A
                 LEFT JOIN PCC_ME_PROJECT_PLAN_HD B ON A.PARENT_ID = B.ID
        WHERE a.parent_id = #{parentId}
        GROUP BY A.ITEM_NO
        ORDER BY A.ITEM_NO
    </select>

    <!--    查询型体项次下的所有版本-->
    <select id="queryAllVersion" resultType="java.lang.Integer">
        SELECT A.VERSION
        FROM PCC_ME_PROJECT_PLAN_DT A
                 LEFT JOIN PCC_ME_PROJECT_PLAN_HD B ON A.PARENT_ID = B.ID
        WHERE
          a.parent_id = #{parentId}
          and A.ITEM_NO = #{itemNo}
        ORDER BY A.ITEM_NO
    </select>

    <!--    查询所有动作-->
    <select id="queryAllActions" resultType="com.zqn.sop.entity.PccMeProjectPlanActions">
        select id,item,action_cn,action_en,action_vn from PCC_ME_PROJECT_PLAN_ACTIONS
        <where>
            <if test="item != null and item != ''">
                AND action_cn like '%' || #{item} || '%'
            </if>
            <if test="type != null and type != ''">
                AND type = #{type}
            </if>
        </where>
        order by item
    </select>
    <select id="queryActionsByTags" resultType="com.zqn.sop.entity.PccMeProjectPlanActions">
        select id,item,action_cn,action_en,action_vn from PCC_ME_PROJECT_PLAN_ACTIONS
        <where>
            <if test="item != null and item != ''">
                AND action_cn like '%' || #{item} || '%'
            </if>
            <if test="type != null and type != ''">
                AND type = #{type}
            </if>
            <if test="tag != null and tag != ''">
                AND tag = #{tag}
            </if>
        </where>
        order by item
    </select>

    <select id="queryVideo" resultType="com.zqn.sop.entity.PccMeProjectPlanVideo">
        select ID, URL, NAME
        from PCC_ME_PROJECT_PLAN_VIDEO
        where parent_id = #{id}
    </select>

    <select id="selectMin" resultType="java.lang.Integer">
        select min(ITEM_NO)
        from PCC_ME_PROJECT_PLAN_DT a
        left join PCC_ME_PROJECT_PLAN_HD b on a.parent_id = b.id
        where b.model = #{model}
        and b.dept = #{dept}
        <if test="factory != null and factory != ''">
            AND b.factory = #{factory}
        </if>
    </select>

    <!--    插入图片-->
    <insert id="insertImg">
        INSERT INTO PCC_ME_PROJECT_PLAN_IMG (PARENT_ID, IMG_URL, REMARK, TYPE)
        VALUES (#{parentId}, #{imgUrl}, #{remark}, #{type})
    </insert>
    <!--插入视频-->
    <insert id="insertVideo">
        INSERT INTO PCC_ME_PROJECT_PLAN_VIDEO (PARENT_ID, URL, NAME)
        VALUES (#{parentId}, #{url}, #{name})
    </insert>
    <!--    插入步骤信息 -->
    <insert id="insertContent">
        insert into PCC_ME_PROJECT_PLAN_PROCESS (PARENT_ID, LANG, OP_STD, SELF_CHECK_POINTS)
        values (#{parentId}, #{lang}, #{opStd}, #{selfCheckPoints})
    </insert>

    <update id="incrementItemWithOffset">
        update PCC_ME_PROJECT_PLAN_DT
        set ITEM_NO = ITEM_NO + 10000
        where parent_id in
              (select id from PCC_ME_PROJECT_PLAN_HD where id = #{parentId})
          and item_no >= #{itemNo}
    </update>

    <update id="finalizeItemUpdate">
        update PCC_ME_PROJECT_PLAN_DT
        set ITEM_NO = ITEM_NO - 9999
        where parent_id in
              (select id from PCC_ME_PROJECT_PLAN_HD  where id = #{parentId})
          and item_no >= #{itemNo}
    </update>

    <update id="deleteItemWithOffset">
        update PCC_ME_PROJECT_PLAN_DT
        set ITEM_NO = ITEM_NO + 10000
        where parent_id =
              (select id from PCC_ME_PROJECT_PLAN_HD   where id = #{parentId})
          and item_no > #{itemNo}
    </update>

    <update id="deleteItemUpdate">
        update PCC_ME_PROJECT_PLAN_DT
        set ITEM_NO = ITEM_NO - 10001
        where parent_id =
              (select id from PCC_ME_PROJECT_PLAN_HD  where id = #{parentId})
          and item_no > #{itemNo}
    </update>

    <!--    删除项次-->
    <delete id="deleteItem">
        delete
        from PCC_ME_PROJECT_PLAN_DT
        where parent_id in (select id
                            from PCC_ME_PROJECT_PLAN_HD
                             where id = #{parentId})
          and item_no = #{itemNo}
    </delete>

    <!--    删除项次-->
    <delete id="deleteContent">
        delete
        from PCC_ME_PROJECT_PLAN_DT
        where parent_id in (select id
                            from PCC_ME_PROJECT_PLAN_HD
                            where  id = #{parentId})
          and item_no = #{itemNo}
    </delete>

    <!--    删除项次-->
    <delete id="deleteImg">
        delete
        from PCC_ME_PROJECT_PLAN_DT
        where parent_id in (select id
                            from PCC_ME_PROJECT_PLAN_HD
                            where parent_id = #{parentId})
          and item_no = #{itemNo}
    </delete>
</mapper>