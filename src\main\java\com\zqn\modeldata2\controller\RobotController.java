package com.zqn.modeldata2.controller;

import com.zqn.modeldata2.service.RobotService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/robot")
public class RobotController {

    @Autowired
    private RobotService robotService;

    @GetMapping("/robotInfo")
    public String robotInfo()  {

        try {
            return robotService.getRobotInfo();
        } catch (Exception e) {
            return "Error retrieving robot info: " + e.getMessage();
        }

    }
}
