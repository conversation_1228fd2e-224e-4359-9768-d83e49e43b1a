<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zqn.factorymanager.mapper.DevTrackingMapper">

    <select id="queryTop" resultType="com.zqn.factorymanager.entity.DevTracking">
          SELECT x.brand_no,x.model_no,y.model_pic,x.last_no,x.brand_fn,x.emp_name,x.ord_no,z.shp_date
               FROM (
               SELECT b.brand_no,b.model_no,d.last_no,c.brand_fn,a.emp_no,a.emp_name,a.made_dept,MAX(a.ord_no) ord_no
               FROM mk_sorderbar a ,gc_sorder  b ,be_brand c ,vck_smodel d 
               WHERE a.semi_no='V' AND a.TYPE ='I'
               AND a.ord_no = b.ord_no  AND b.brand_no= c.brand_no
               AND b.model_no = d.model_no
               AND a.bar_date between #{startTime} and #{endTime}
               GROUP BY b.brand_no,b.model_no,d.last_no,c.brand_fn,a.emp_no,a.emp_name,a.made_dept
               ) x ,vck_smodel y  ,(SELECT ord_no ,MIN(shp_date) shp_date  FROM gc_sorders GROUP BY ord_no) z
          WHERE x.model_no = y.model_no AND x.ord_no = z.ord_no  
          order by x.brand_no
    </select>

    <select id="queryBottom" resultType="com.zqn.factorymanager.entity.DevTracking">
          SELECT x.brand_no,x.model_no,y.model_pic,x.last_no,x.mold_no,x.brand_fn,x.emp_name,z.shp_date
               FROM (
               SELECT b.brand_no,b.model_no,d.last_no,c.brand_fn,a.emp_no,a.emp_name,a.made_dept,e.mold_no,MAX(a.ord_no) ord_no
               FROM mk_sorderbar a ,gc_sorder  b ,be_brand c ,vck_smodel d ,
               (SELECT a.model_no, b.mold_no
                    FROM bg_moldd a, bg_mold b
                    WHERE a.mold_seq = b.mold_seq AND b.mold_type = 'A') e
               WHERE a.semi_no='W' AND a.TYPE ='I'
               AND a.ord_no = b.ord_no  AND b.brand_no= c.brand_no
               AND b.model_no = d.model_no AND a.bar_date between #{startTime} and #{endTime}
               AND b.model_no = e.model_no 
               GROUP BY b.brand_no,b.model_no,d.last_no,c.brand_fn,a.emp_no,a.emp_name,a.made_dept,e.mold_no
               ) x ,vck_smodel y  ,(SELECT ord_no ,MIN(shp_date) shp_date  FROM gc_sorders GROUP BY ord_no) z
          WHERE x.model_no = y.model_no  AND x.ord_no = z.ord_no  
          order by x.brand_no
    </select>

    <select id="queryWarnTop" resultType="com.zqn.factorymanager.entity.DevTracking">
        <![CDATA[
        SELECT x.brand_no,x.model_no,y.model_pic,x.last_no,x.brand_fn,x.emp_name,z.shp_date
          FROM (
          SELECT b.brand_no,b.model_no,d.last_no,c.brand_fn,a.emp_no,a.emp_name,a.made_dept,MAX(a.ord_no) ord_no
          FROM mk_sorderbar a ,gc_sorder  b ,be_brand c ,vck_smodel d 
          WHERE a.semi_no='V' AND a.TYPE ='I'
          AND a.ord_no = b.ord_no  AND b.brand_no= c.brand_no
          AND b.model_no = d.model_no
          AND a.bar_date between #{startTime} and #{endTime}
          AND NOT EXISTS (SELECT NULL FROM  mk_sorderbar WHERE ord_no= b.ord_no AND semi_no='V' AND TYPE='O' AND TRUNC(bar_date)<TRUNC(a.bar_date)+2)
          GROUP BY b.brand_no,b.model_no,d.last_no,c.brand_fn,a.emp_no,a.emp_name,a.made_dept
          ) x ,vck_smodel y  ,(SELECT ord_no ,MIN(shp_date) shp_date  FROM gc_sorders GROUP BY ord_no) z
          WHERE x.model_no = y.model_no   AND x.ord_no = z.ord_no   
        ORDER BY x.brand_no
        ]]>
    </select>

    <select id="queryWarnBottom" resultType="com.zqn.factorymanager.entity.DevTracking">
        <![CDATA[
        SELECT x.brand_no,x.model_no,y.model_pic,x.last_no,x.mold_no,x.brand_fn,x.emp_name,z.shp_date
          FROM (
          SELECT b.brand_no,b.model_no,d.last_no,c.brand_fn,a.emp_no,a.emp_name,a.made_dept,e.mold_no,MAX(a.ord_no) ord_no
          FROM mk_sorderbar a ,gc_sorder  b ,be_brand c ,vck_smodel d ,
          (SELECT a.model_no, b.mold_no
               FROM bg_moldd a, bg_mold b
               WHERE a.mold_seq = b.mold_seq AND b.mold_type = 'A') e
          WHERE a.semi_no='W' AND a.TYPE ='I'
          AND a.ord_no = b.ord_no  AND b.brand_no= c.brand_no
          AND b.model_no = d.model_no AND a.bar_date between #{startTime} and #{endTime}
          AND b.model_no = e.model_no 
          AND NOT EXISTS (SELECT NULL FROM  mk_sorderbar WHERE ord_no= b.ord_no AND semi_no='W' AND TYPE='O' AND TRUNC(bar_date)<TRUNC(a.bar_date)+2)
          GROUP BY b.brand_no,b.model_no,d.last_no,c.brand_fn,a.emp_no,a.emp_name,a.made_dept,e.mold_no
          ) x ,vck_smodel y  ,(SELECT ord_no ,MIN(shp_date) shp_date  FROM gc_sorders GROUP BY ord_no) z
          WHERE x.model_no = y.model_no  AND x.ord_no = z.ord_no 
        ORDER BY x.brand_no
        ]]>
    </select>

    <update id="updateBrandFn" parameterType="com.zqn.factorymanager.entity.DevTracking">
        UPDATE be_brand 
        SET brand_fn = #{brandFn}
        WHERE brand_no = #{brand_no}
    </update>
</mapper>