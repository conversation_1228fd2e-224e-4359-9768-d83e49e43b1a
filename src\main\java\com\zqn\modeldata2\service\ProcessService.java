package com.zqn.modeldata2.service;

import com.zqn.modeldata2.entity.ProcessInfo;
import com.zqn.modeldata2.entity.ProcessStep;
import com.zqn.modeldata2.entity.ProcessTemplate;

import java.util.List;
import java.util.Map;

public interface ProcessService {
    List<Integer> getOperation();

    List<Object> getBrand();

    List<String> getModel(String brand_no);

    Map<String, Object> getLast(String model_no);

    String getCode(String model_no, String operation);

    Map<String, Object> getPicture(String model_no);

    List<String> getType(String operation);

    List<String> getMaterial();

    List<ProcessInfo> getProcessInfo(ProcessInfo processInfo);

    Integer addProcessInfo(ProcessInfo processInfo);

    Integer updateProcessInfo(ProcessInfo processInfo);

    Integer deleteProcessInfo(ProcessInfo processInfo);

    Integer batchDeleteProcessInfo(List<ProcessInfo> processInfoList);

    String getNo(ProcessStep processStep);

    String getKey(ProcessStep processStep);

    List<ProcessTemplate> getTemplate(String operation);

    Integer templateImport(ProcessTemplate processTemplate);

    List<ProcessStep> getProcessStep(ProcessStep processStep);

    Integer addProcessStep(ProcessStep processStep);

    Integer updateProcessStep(ProcessStep processStep);

    Integer deleteProcessStep(ProcessStep processStep);

    Integer batchDeleteProcessStep(List<ProcessStep> processStepList);

    Integer editProcessStep(List<ProcessStep> processStepList);

    List<String> getModelList();

    Integer copyProcess(String originalModel, String targetModel, List<String> operationList, String user);
}