<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zqn.modeldata2.mapper.MenuMapper">


    <resultMap id="ResultMap" type="com.zqn.modeldata2.entity.Menu">
        <!--        <result property="" column="" jdbcType=""/>-->
        <result property="menuId" column="MENU_ID" jdbcType="NUMERIC"/>
        <result property="menuNo" column="MENU_NO" jdbcType="NUMERIC"/>
        <result property="menuDesc" column="MENU_DESC" jdbcType="VARCHAR"/>
        <result property="menuParent" column="MENU_PARENT" jdbcType="VARCHAR"/>
        <result property="userNo" column="USER_NO" jdbcType="VARCHAR"/>
        <result property="menuParentNo" column="MENU_PARENT_NO" jdbcType="NUMERIC"/>
        <result property="menuUrl" column="MENU_URL" jdbcType="VARCHAR"/>
        <result property="type" column="TYPE" jdbcType="NUMERIC"/>
        <result property="menuLevel" column="menu_level" jdbcType="NUMERIC"/>

    </resultMap>

    <!-- 通过菜单分类 不包含报表-->
    <select id="findByPart" resultMap="ResultMap">
        select menu_no, menu_desc, menu_parent, menu_parent_no, menu_url, user_no, menu_level
        from pcc_app_c_menu
        where user_no is null
          and menu_no != 17
          and menu_level = 0
          and menu_parent_no = #{partNo}
        order by MENU_NO
    </select>

    <!-- 查询所有菜单 不包含报表-->
    <select id="findAllMenu" resultMap="ResultMap">
        select menu_id,
               menu_no,
               menu_desc,
               menu_parent,
               menu_parent_no,
               menu_url,
               user_no,
               menu_level
        from pcc_app_c_menu
        where user_no is null
          and type = 1
        order by menu_no
    </select>

    <!-- 查询用户常用菜单 -->
    <select id="findCommonyUsedMenuByUser" resultMap="ResultMap">
        select menu_no, menu_desc, menu_parent, menu_parent_no, menu_url, user_no, menu_level
        from pcc_app_c_menu
        where type = 3
          and menu_parent_no != 0
          and user_no = #{userNo}
          and menu_level = 0
        order by menu_no
    </select>

    <select id="findMenuByUser" resultMap="ResultMap">
        select menu_no, menu_desc, menu_parent, menu_parent_no, menu_url, user_no, menu_level
        from pcc_app_c_menu
        where user_no = #{userNo}
          and type = 2
          and menu_level = 0
        order by menu_no
    </select>

    <!--  查询用户报表  -->
    <select id="findReportByUser" resultMap="ResultMap">
        select menu_no, menu_desc, menu_parent, menu_parent_no, menu_url, user_no, menu_level
        from pcc_app_c_menu
        where type = 2
          and menu_parent_no != 0
          and menu_parent_no = 17
          and user_no = #{userNo}
          and menu_level = 0
        order by menu_no
    </select>

    <select id="findSecondMenuByUser" resultType="com.zqn.modeldata2.entity.Menu">
        select menu_no, menu_desc, menu_parent, menu_parent_no, menu_url, user_no, menu_level
        from pcc_app_c_menu
        where user_no = #{userNo}
          and menu_parent_no = #{menu.menuNo}
          and type = 1
          and menu_level = 1
        order by menu_no
    </select>

    <!-- 新增用户常用表   -->
    <insert id="addCommonyUsed">
        insert into PCC_APP_C_MENU (MENU_NO, MENU_DESC, MENU_PARENT, USER_NO, MENU_PARENT_NO, MENU_URL, TYPE,
                                    menu_level)
        values (#{menu.menuNo},
                #{menu.menuDesc},
                #{menu.menuParent},
                #{menu.userNo},
                #{menu.menuParentNo},
                #{menu.menuUrl},
                #{menu.type},
                0)
    </insert>

    <delete id="deleteByUser">
        delete from PCC_APP_C_MENU where type = 3 and user_no = #{userNo} and menu_no in
        <foreach collection="delNos" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteCommyUsedByUser">
        delete from PCC_APP_C_MENU where type = 2 and user_no = #{userNo} and menu_no in
        <foreach collection="delNos" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>


    <select id="buttonQuery" resultType="com.zqn.modeldata2.entity.Menu">
        select menu_no, menu_desc, menu_parent, menu_parent_no, menu_url, user_no, menu_level
        from pcc_app_c_menu
        where menu_url = #{url}
          and user_no = #{loginUser}
    </select>
</mapper>