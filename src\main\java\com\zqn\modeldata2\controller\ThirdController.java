package com.zqn.modeldata2.controller;

import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.entity.CkSmodelpa;
import com.zqn.modeldata2.service.ThirdService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/third")
public class ThirdController {
    @Autowired
    private ThirdService thirdService;

    @PostMapping("/getOption")
    public R<Map<String, List<Object>>> getOption() {
        Map<String, List<Object>> result = thirdService.getOption();
        return R.success(result);
    }

    @PostMapping("/getPart")
    public R<Map<String, List<CkSmodelpa>>> getPart(@RequestBody CkSmodelpa ckSmodelpa) {
        Map<String, List<CkSmodelpa>> result = thirdService.getPart(ckSmodelpa);
        return R.success(result);
    }

    @PostMapping("/addTemporaryPart")
    public R<String> addTemporaryPart(@RequestBody CkSmodelpa ckSmodelpa) {
        int result = thirdService.addTemporaryPart(ckSmodelpa);
        if (result < 0) {
            return R.error("临时部位添加失败！");
        }
        return R.success("临时部位添加成功！");
    }

    @PostMapping("/addPart")
    public R<String> addPart(@RequestBody List<CkSmodelpa> ckSmodelpaList) {
        int result = thirdService.addPart(ckSmodelpaList);
        if (result < 0) {
            return R.error("部位数据添加失败！");
        }
        return R.success("部位数据添加成功！");
    }

    @PostMapping("/updatePart")
    public R<String> updatePart(@RequestBody CkSmodelpa ckSmodelpa) {
        int result = thirdService.updatePart(ckSmodelpa);
        if (result < 0) {
            return R.error("部位数据修改失败！");
        }
        return R.success("部位数据修改成功！");
    }

    @PostMapping("/deletePart")
    public R<String> deletePart(@RequestBody CkSmodelpa ckSmodelpa) {
        int result = thirdService.deletePart(ckSmodelpa);
        if (result < 0) {
            return R.error("部位数据删除失败！");
        }
        return R.success("部位数据删除成功！");
    }

    @PostMapping("/batchDeletePart")
    public R<String> batchDeletePart(@RequestBody List<CkSmodelpa> ckSmodelpaList) {
        int result = thirdService.batchDeletePart(ckSmodelpaList);
        if (result < 0) {
            return R.error("部位数据删除失败！");
        }
        return R.success("部位数据删除成功！");
    }
}
