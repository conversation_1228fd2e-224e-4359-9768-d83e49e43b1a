package com.zqn.modeldata2.entity;

import lombok.Data;

@Data
public class Fit {

    // 品牌
    private String brand_no;

    // 序號
      private   String rownum;

    //    原樣交接日期
    private String tran_date;
    //    接單日
    private String ord_date;

    private String get_date;

    //    生產工廠
    private String factory;

    // 量產訂單數量
    private String ord_qty;

    //    訂單數量
    private String tot_qty;
    //    業務
    private String dutyer;
    //    上線日
    private String online_date;

    //    樣品單號
    private String ord_no;

    //    鞋圖
    private byte[] model_pic;

    private String pic_base64;

    //    型體
    private String model_no;

    //    客人型體名稱
    private String model_name;
    //    楦頭
    private String last_no;
    //    面料配套
    private String t1_flag;
    //    副料配套
    private String t2_flag;
    //    副料配套时间
    private String t2_date;
    //    底料配套
    private String t3_flag;
    //    加工
    private String y_flag;
    //    鞋面完成日
    private String u_date;
    //    半成品完成日
    private String b_date;
    //    樣品要求完成日
    private String shp_date;
    //    樣品單類型
    private String dev_type;
    //    備註 (面部說明）
    private String upp_desc;
    // （底部說明）
    private String sol_desc;
    // (fit 確認)
    private String cfm_flag;
    // （樣品單結案）
    private String cl_flag;

    private String u1_qty;
    private String t5_qty;
    private String b1_qty;
    private String b2_qty;
    private String b3_qty;
    private String b4_qty;
    private String p1_qty;


}
