package com.zqn.production.mapper;

import com.zqn.production.entity.WorkOrder;
import com.zqn.production.entity.WorkOrderGroup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 工单Mapper接口
 */
@Mapper
public interface WorkOrderMapper {
    
    /**
     * 根据部门编号查询组别列表
     * @param deptNo 部门编号
     * @return 组别列表
     */
    WorkOrderGroup getGroupList(@Param("deptNo") String deptNo);

    /**
     * 初始化樣品鞋面每日目標基礎數據
     */
    void callPdIniBdEmpgrpd(Map<String, Object> params);
    
    /**
     * 根据部门编号和日期查询组别列表
     * @param deptNo 部门编号
     * @param date 日期
     * @return 组别列表
     */
    List<WorkOrderGroup> getGroupListWithDate(@Param("deptNo") String deptNo, @Param("date") Date date);
    
    /**
     * 根据制作部门和日期查询工单列表
     * @param madeDept 制作部门
     * @param date 日期
     * @return 工单列表
     */
    List<WorkOrder> getOrderList(@Param("madeDept") String madeDept, @Param("date") Date date);
    
    /**
     * 更新工单选择状态
     * @param ordNo 工单号
     * @param seFlag 选择标志
     * @param ordQty 工单数量
     * @return 影响行数
     */
    int updateOrderSelection(@Param("ordNo") String ordNo, @Param("seFlag") String seFlag, @Param("ordQty") String ordQty);
    
    /**
     * 插入工单选择记录
     * @param ordNo 工单号
     * @param seFlag 选择标志
     * @param ordQty 工单数量
     * @return 影响行数
     */
    int insertOrderSelection(@Param("ordNo") String ordNo, @Param("seFlag") String seFlag, @Param("ordQty") String ordQty);
    
    /**
     * 更新组别目标PPH
     * @param deptNo 部门编号
     * @param madeDept 制作部门
     * @param runRate 目标PPH
     * @return 影响行数
     */
    int updateGroupTarget(@Param("deptNo") String deptNo, @Param("madeDept") String madeDept,
                          @Param("runRate") String runRate, @Param("date") String date);
    
    /**
     * 批量查询CK_SMODEL表中的型体图片
     * @param modelNos 型体编号列表
     * @return 型体图片列表
     */
    List<WorkOrder> getBatchPicByCkSmodel(@Param("modelNos") List<String> modelNos);
    
    /**
     * 批量查询CB_MODEL表中的型体图片
     * @param modelNos 型体编号列表
     * @return 型体图片列表
     */
    List<WorkOrder> getBatchPicByCbModel(@Param("modelNos") List<String> modelNos);
} 