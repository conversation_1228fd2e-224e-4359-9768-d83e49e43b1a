package com.zqn.modeldata2.entity.sop;

import lombok.Data;

import java.util.Date;

/**
 * 工序流程
 */
@Data
public class SopProcessFlow {
    // 型体编号
    private String model;
    // 制程
    private String operation;
    // 主要代码
    private String rtgCode;
    // 工序编号
    private String seqNo;
    // 序号
    private int skey;
    // 目标序号
    private int targetSkey;
    // 工作段
    private String wkGroup;
    // 目标加工段
    private String targetWkGroup;
    // 排序起始加工段
    private String startWkGroup;
    // 工序名称
    private String seqName;
    // 动作
    private String actions;
    // 版本
    private int version;
    // 模板编号
    private String docNo;
    // 添加者
    private String insUser;
    // 添加时间
    private Date insDate;
    // 修改者
    private String updUser;
    // 修改时间
    private Date updDate;
}
