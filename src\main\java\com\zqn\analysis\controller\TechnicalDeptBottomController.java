package com.zqn.analysis.controller;

import com.zqn.analysis.entity.TechnicalDept;
import com.zqn.analysis.service.TechnicalDeptBottomService;
import com.zqn.modeldata2.common.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/11/29 10:28
 */
@RestController
@RequestMapping("/technicaldeptbottom")
public class TechnicalDeptBottomController {

    @Resource
    private TechnicalDeptBottomService technicalDeptBottomService;

    @GetMapping("/queryByDEUSER")
    public R<TechnicalDept> queryByDEUSER() {
        TechnicalDept technicalDept = technicalDeptBottomService.queryByDEUSER();
        return R.success(technicalDept);
    }


    @GetMapping("/queryByWEB")
    public R<TechnicalDept> queryByWEB() {
        TechnicalDept technicalDept = technicalDeptBottomService.queryByWEB();
        return R.success(technicalDept);
    }

    @GetMapping("/queryModelList")
    public R<TechnicalDept> queryModelList() {
        TechnicalDept technicalDept = technicalDeptBottomService.queryModelList();
        return R.success(technicalDept);
    }

    @GetMapping("/queryPeopleDetail")
    public R<List<Map<String, Object>>> queryPeopleDetail(@RequestParam String factory) {
        List<Map<String, Object>> result = technicalDeptBottomService.queryPeopleDetail(factory);
        return R.success(result);
    }
}
