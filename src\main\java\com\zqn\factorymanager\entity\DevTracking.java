package com.zqn.factorymanager.entity;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/11/15 10:32
 */
@Data
public class DevTracking {

//    -- 客戶 型體 鞋圖 楦頭編號  模具編號（面部不需要） A級師傅   版師  訂單號 預計出貨日

    private String brand_no;

    private String model_no;

    private byte[] model_pic;

    //模具編號
    private String mold_no;

    private String last_no;

    private String brand_fn;

    private String emp_name;

    private String brandFn;

    private String ord_no;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date shp_date;
}
