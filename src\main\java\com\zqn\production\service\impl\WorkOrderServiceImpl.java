package com.zqn.production.service.impl;

import com.zqn.modeldata2.common.R;
import com.zqn.production.entity.WorkOrder;
import com.zqn.production.entity.WorkOrderGroup;
import com.zqn.production.mapper.WorkOrderMapper;
import com.zqn.production.service.WorkOrderService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 工单服务实现类
 */
@Service
public class WorkOrderServiceImpl implements WorkOrderService {
    
    @Resource
    private WorkOrderMapper workOrderMapper;
    
    @Override
    public R<List<WorkOrderGroup>> getGroupList(String deptNo) {
        // 调用带日期参数的方法，传入null表示不按日期筛选
        return getGroupListWithDate(deptNo, null);
    }
    
    @Override
    public R<List<WorkOrderGroup>> getGroupListWithDate(String deptNo, Date date) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("oReturn", null); // 可以初始化为 null，调用后会填充返回值
            workOrderMapper.callPdIniBdEmpgrpd(params);
            String result = (String) params.get("oReturn");
            System.out.println("Result: " + result);
            List<WorkOrderGroup> groupList = workOrderMapper.getGroupListWithDate(deptNo, date);
            
            // 对组别列表进行排序：按照grp_no最后一个字母后面的数字排序，编码短的在前
            Collections.sort(groupList, new Comparator<WorkOrderGroup>() {
                @Override
                public int compare(WorkOrderGroup o1, WorkOrderGroup o2) {
                    String grpNo1 = o1.getGrp_no();
                    String grpNo2 = o2.getGrp_no();
                    
                    if (grpNo1 == null && grpNo2 == null) return 0;
                    if (grpNo1 == null) return 1;
                    if (grpNo2 == null) return -1;
                    
                    // 首先按编码长度排序，短的在前
                    int lengthCompare = Integer.compare(grpNo1.length(), grpNo2.length());
                    if (lengthCompare != 0) {
                        return lengthCompare;
                    }
                    
                    // 长度相同时，提取最后一个字母后面的数字进行比较
                    Pattern pattern = Pattern.compile("([A-Za-z])(\\d+)$");
                    Matcher matcher1 = pattern.matcher(grpNo1);
                    Matcher matcher2 = pattern.matcher(grpNo2);
                    
                    if (matcher1.find() && matcher2.find()) {
                        int num1 = Integer.parseInt(matcher1.group(2));
                        int num2 = Integer.parseInt(matcher2.group(2));
                        return Integer.compare(num1, num2);
                    }
                    
                    // 如果无法提取数字，则按字符串自然排序
                    return grpNo1.compareTo(grpNo2);
                }
            });
            
            return R.success(groupList);
        } catch (Exception e) {
            return R.error("获取组别列表失败：" + e.getMessage());
        }
    }
    
    @Override
    public R<List<WorkOrder>> getOrderList(String madeDept, Date date) {
        try {
            List<WorkOrder> orderList = workOrderMapper.getOrderList(madeDept, date);
            //获取型体图片
            getModelPic(orderList);
            return R.success(orderList);
        } catch (Exception e) {
            e.printStackTrace();
            return R.error("获取工单列表失败：" + e.getMessage());
        }
    }

    private void getModelPic(List<WorkOrder> orderList) {
        if (orderList == null || orderList.isEmpty()) {
            return;
        }
        
        // 收集所有型体编号
        List<String> modelNos = new ArrayList<>();
        for (WorkOrder order : orderList) {
            if (order.getModel_no() != null && !order.getModel_no().trim().isEmpty()) {
                modelNos.add(order.getModel_no());
            }
        }
        
        if (modelNos.isEmpty()) {
            return;
        }
        
        // 批量查询CK_SMODEL表
        List<WorkOrder> ckSmodelPics = workOrderMapper.getBatchPicByCkSmodel(modelNos);
        Map<String, byte[]> ckSmodelPicMap = new HashMap<>();
        for (WorkOrder pic : ckSmodelPics) {
            if (pic.getModel_no() != null && pic.getModel_pic() != null) {
                ckSmodelPicMap.put(pic.getModel_no(), pic.getModel_pic());
            }
        }
        
        // 批量查询CB_MODEL表
        List<WorkOrder> cbModelPics = workOrderMapper.getBatchPicByCbModel(modelNos);
        Map<String, byte[]> cbModelPicMap = new HashMap<>();
        for (WorkOrder pic : cbModelPics) {
            if (pic.getModel_no() != null && pic.getModel_pic() != null) {
                cbModelPicMap.put(pic.getModel_no(), pic.getModel_pic());
            }
        }
        
        // 设置图片：优先使用CK_SMODEL，如果没有则使用CB_MODEL
        for (WorkOrder order : orderList) {
            String modelNo = order.getModel_no();
            if (modelNo != null) {
                byte[] modelPic = ckSmodelPicMap.get(modelNo);
                if (modelPic == null) {
                    modelPic = cbModelPicMap.get(modelNo);
                }
                order.setModel_pic(modelPic);
            }
        }
    }

    @Override
    @Transactional
    public R<Void> updateOrderSelection(String ordNo, String seFlag, String ordQty) {
        try {
            // 尝试更新
            int updateCount = workOrderMapper.updateOrderSelection(ordNo, seFlag, ordQty);
            
            // 如果没有更新任何行，则插入
            if (updateCount == 0) {
                workOrderMapper.insertOrderSelection(ordNo, seFlag, ordQty);
            }
            
            return R.success(null);
        } catch (Exception e) {
            return R.error("更新工单选择状态失败：" + e.getMessage());
        }
    }
    
    @Override
    public R<Void> updateGroupTarget(String deptNo, String madeDept, String runRate, String date) {
        try {
            workOrderMapper.updateGroupTarget(deptNo, madeDept, runRate, date);
            return R.success(null);
        } catch (Exception e) {
            return R.error("更新组别目标PPH失败：" + e.getMessage());
        }
    }
} 