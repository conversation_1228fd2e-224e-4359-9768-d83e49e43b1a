package com.zqn.sop.mapper;

import com.zqn.sop.entity.PccMeProjectPlanActions;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PccMeProjectPlanActionsMapper {

    Integer exist(@Param("vo") PccMeProjectPlanActions pccMeProjectPlanActions);

    int add(@Param("vo") PccMeProjectPlanActions vo);

    List<PccMeProjectPlanActions> queryTag(@Param("type") String type);
}