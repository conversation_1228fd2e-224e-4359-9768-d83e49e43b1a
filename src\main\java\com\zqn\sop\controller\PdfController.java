package com.zqn.sop.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Lists;
import com.zqn.modeldata2.service.ProcessService;
import com.zqn.sop.entity.PccMeProjectPlanImg;
import com.zqn.sop.service.PccMeProjectPlanDtService;
import com.zqn.sop.vo.PccMeProjectPlanDtVo;
import com.zqn.sop.vo.PdfRequest;
import org.apache.pdfbox.io.MemoryUsageSetting;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.stream.Collectors;


@Controller
public class PdfController {


    @Autowired
    private TemplateEngine templateEngine;

    @Autowired
    private ProcessService processService;

    @Autowired
    private PccMeProjectPlanDtService pccMeProjectPlanDtService;


    @GetMapping("/pdf")
    public ResponseEntity<Resource> pdf(PdfRequest pdfRequest, HttpServletResponse response) throws Exception {

        String s = pdfRequest.getDeptName().split("-")[1];
        pdfRequest.setSimpleName(s);

        List<PccMeProjectPlanDtVo> result = pccMeProjectPlanDtService.queryAllDt(pdfRequest.getId());
        if (CollectionUtil.isEmpty(result)) {
            return null;
        }

        try (OutputStream os = response.getOutputStream()) {
            Context context = new Context();

            Map<String, Object> params = new HashMap<>();
            params.put("model_no", pdfRequest.getModelNo());
            String model_no = params.get("model_no") != null ? params.get("model_no").toString() : "";
            Map<String, Object> imgResult = processService.getPicture(model_no);
            String base64String = Base64.getEncoder().encodeToString((byte[]) imgResult.get("model_pic"));

            // 创建临时PDF文件路径

            List<String> html = new ArrayList<>();

            for (PccMeProjectPlanDtVo item : result) {

                String format = DateUtil.format(item.getCreate_date(), "yyyy/MM/dd");
                item.setCreateDate(format);
                if(StrUtil.isNotBlank(item.getOp_std()))
                    item.setOp_std(item.getOp_std().replace("\n", "<br/>"));
                if(StrUtil.isNotBlank(item.getSelf_check_points()))
                     item.setSelf_check_points(item.getSelf_check_points().replace("\n", "<br/>"));

                List<PccMeProjectPlanImg> imgUrls = item.getImgUrls();
                if (CollectionUtil.isNotEmpty(imgUrls)) {
                    List<String> type1Imgs = imgUrls.stream().filter(imageUrl -> imageUrl.getType() == 1)
                            .map(PccMeProjectPlanImg::getImg_url)
                            .collect(Collectors.toList());

                    List<String> type2Imgs = imgUrls.stream().filter(imageUrl -> imageUrl.getType() == 2)
                            .map(PccMeProjectPlanImg::getImg_url)
                            .collect(Collectors.toList());

                    item.setType1Imgs(type1Imgs);
                    item.setType2Imgs(type2Imgs);
//
//                    List<String> imageList = Arrays.asList(
//                            "http://192.168.131.125:8691/pcc/api/files/get/?url=/img/2024-07-04-14/f342f141-2567-4c2b-bbff-ad4abdfaf6e9.jpg",
//                            "http://192.168.131.125:8691/pcc/api/files/get/?url=/img/2024-07-04-14/042e7fc1-1cdc-4e8c-9ba2-fbed843b2c51.jpg",
//                            "http://192.168.131.125:8691/pcc/api/files/get/?url=/img/2024-07-05-08/a02b7118-92f7-4307-a8c9-f825a92d0b4c.png",
//                            "http://192.168.131.125:8691/pcc/api/files/get/?url=/img/2024-07-04-14/f342f141-2567-4c2b-bbff-ad4abdfaf6e9.jpg",
//                            "http://192.168.131.125:8691/pcc/api/files/get/?url=/img/2024-07-04-14/042e7fc1-1cdc-4e8c-9ba2-fbed843b2c51.jpg",
//                            "http://192.168.131.125:8691/pcc/api/files/get/?url=/img/2024-07-05-08/a02b7118-92f7-4307-a8c9-f825a92d0b4c.png",
//                            "http://192.168.131.125:8691/pcc/api/files/get/?url=/img/2024-07-04-14/f342f141-2567-4c2b-bbff-ad4abdfaf6e9.jpg",
//                            "http://192.168.131.125:8691/pcc/api/files/get/?url=/img/2024-07-04-14/042e7fc1-1cdc-4e8c-9ba2-fbed843b2c51.jpg",
//                            "http://192.168.131.125:8691/pcc/api/files/get/?url=/img/2024-07-05-08/a02b7118-92f7-4307-a8c9-f825a92d0b4c.png"
//                    );
//
//                    List<String> imageList2 = Arrays.asList(
//                            "http://192.168.131.125:8691/pcc/api/files/get/?url=/img/2024-07-04-14/f342f141-2567-4c2b-bbff-ad4abdfaf6e9.jpg",
//                            "http://192.168.131.125:8691/pcc/api/files/get/?url=/img/2024-07-04-14/042e7fc1-1cdc-4e8c-9ba2-fbed843b2c51.jpg",
//                            "http://192.168.131.125:8691/pcc/api/files/get/?url=/img/2024-07-05-08/a02b7118-92f7-4307-a8c9-f825a92d0b4c.png",
//                            "https://img2.baidu.com/it/u=978276174,890356570&fm=253&fmt=auto&app=120&f=JPEG?w=712&h=500",
//                            "https://img1.baidu.com/it/u=3518673092,2032183538&fm=253&fmt=auto&app=138&f=JPEG?w=781&h=500",
//                            "https://img2.baidu.com/it/u=978276174,890356570&fm=253&fmt=auto&app=120&f=JPEG?w=712&h=500",
//                            "https://img2.baidu.com/it/u=978276174,890356570&fm=253&fmt=auto&app=120&f=JPEG?w=712&h=500",
//                            "https://img1.baidu.com/it/u=3518673092,2032183538&fm=253&fmt=auto&app=138&f=JPEG?w=781&h=500"
//                    );
//                    item.setType1Imgs(imageList);
//                    item.setType2Imgs(imageList2);
                }

                context.setVariable("item", item);
                context.setVariable("base64Image", base64String);
                context.setVariable("pdfRequest", pdfRequest);
                String htmlContent = templateEngine.process("index", context);
                // 将HTML内容写入临时文件
                Path tempHtmlPath = Files.createTempFile(UUID.randomUUID().toString(), ".html");
                Files.write(tempHtmlPath, htmlContent.getBytes());
                html.add(tempHtmlPath.toAbsolutePath().toString());
            }

            Path outputPdfFile = Files.createTempFile(UUID.randomUUID().toString(), ".pdf");
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "attachment; filename=" + outputPdfFile.getFileName());
            List<List<String>> itemsList = Lists.partition(html, 3);
            List<String> pdfPaths = new ArrayList<>();
            for (List<String> item : itemsList) {
                Path tempPdfPath = Files.createTempFile(UUID.randomUUID().toString(), ".pdf");
                convertHtmlsToPdf(tempPdfPath.toAbsolutePath().toString(), item);
                pdfPaths.add(tempPdfPath.toAbsolutePath().toString());
            }
            PDFMergerUtility pdfMerger = new PDFMergerUtility();
            System.out.println("outputPdfFile" + outputPdfFile.toAbsolutePath().toString());
            try {
                for (String pdfFile : pdfPaths) {
                    pdfMerger.addSource(pdfFile);
                }
                pdfMerger.setDestinationFileName(outputPdfFile.toAbsolutePath().toString());
                pdfMerger.mergeDocuments(MemoryUsageSetting.setupMainMemoryOnly());
                System.out.println("PDF文件合并成功！");

            byte[] pdfBytes =  Files.readAllBytes(Paths.get(outputPdfFile.toAbsolutePath().toString()));
            os.write(pdfBytes);
            os.flush();
            } catch (IOException e) {
                e.printStackTrace();
            }


        } catch (Exception e) {
            e.printStackTrace();
        }
        // 删除临时文件
//        Files.deleteIfExists(tempHtmlPath);
//        Files.deleteIfExists(tempPdfPath);
        return null;
    }


    public void convertHtmlsToPdf(String outputPdfPath, List<String> inputHtmlPaths) {
        System.out.println("outputPdfPath" + outputPdfPath);
        try {
            ProcessBuilder processBuilder = new ProcessBuilder();
//            processBuilder.command("wkhtmltopdf", "--orientation", "--load-error-handling ignore", "Landscape");
//            processBuilder.command("wkhtmltopdf", "--orientation", "Landscape", "--load-error-handling", "ignore");
            processBuilder.command(
                    "wkhtmltopdf",
                    "--orientation", "Landscape", // 设置为横向
                    "--load-error-handling", "ignore", // 忽略加载错误
                    "--margin-top", "2mm", // 设置顶部边距
                    "--margin-bottom", "5mm", // 设置底部边距
                    "--margin-left", "8mm", // 设置左侧边距
                    "--margin-right", "8mm", // 设置右侧边距
                    "--encoding", "utf-8" // 设置编码为 utf-8
            );

//            processBuilder.redirectErrorStream(true);

            // 添加HTML输入文件路径
            for (String htmlPath : inputHtmlPaths) {
                processBuilder.command().add(htmlPath);
            }
            // 设置PDF输出文件路径
            processBuilder.command().add(outputPdfPath);
            Process process = processBuilder.start();
            int exitCode = process.waitFor(); // 等待进程执行完成
//            if (exitCode == 0) {
//                return Files.readAllBytes(Paths.get(outputPdfPath));
//            } else {
//                throw new IOException("Failed to convert HTML to PDF");
//            }

        } catch (Exception e) {
            e.printStackTrace();
        }
//        return null;
    }

}
