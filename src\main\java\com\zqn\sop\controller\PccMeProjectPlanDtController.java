package com.zqn.sop.controller;

import com.alibaba.fastjson.JSONObject;
import com.zqn.modeldata2.common.R;
import com.zqn.sop.service.PccMeProjectPlanDtService;
import com.zqn.sop.vo.PccMeProjectPlanDtVo;
import com.zqn.sop.vo.PccMeProjectPlanHdVo;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/5/8 10:02
 */
@RestController
@RequestMapping("/pccmeprjplandt")
@Validated
public class PccMeProjectPlanDtController {

    @Resource
    private PccMeProjectPlanDtService pccMeProjectPlanDtService;

    @GetMapping("/queryByItem")
    public R<PccMeProjectPlanDtVo> queryByItem(
                                               @RequestParam(value = "item") String item,
                                               @RequestParam(value = "version", required = false) Integer version,
                                               @RequestParam(value = "parentId") Integer parentId
                                               ) throws IOException {
        PccMeProjectPlanDtVo result = pccMeProjectPlanDtService.queryByItem(item, version,parentId);
        return R.success(result);
    }


    /**
     * @description: 查询型体的所有项次，用于打印
     * <AUTHOR> Yang
     * @date 2024/7/8 14:00
     * @version 1.0
     */
    @GetMapping("/queryAllDt")
    public R<List<PccMeProjectPlanDtVo>> queryAllDt(  @RequestParam(value = "parentId") Integer parentId) {
        List<PccMeProjectPlanDtVo> result = pccMeProjectPlanDtService.queryAllDt(parentId);
        return R.success(result);
    }

    @GetMapping("/queryAllTools")
    public R<List<JSONObject>> queryAllTools(@RequestParam(value = "content") String content,
                                             @RequestParam(value = "searchType") Integer searchType,
                                             @RequestParam(value = "dept") Integer dept) {
        List<JSONObject> result = pccMeProjectPlanDtService.queryAllTools(content, searchType, dept);
        return R.success(result);
    }

    @GetMapping("/queryAllActions")
    public R<List<JSONObject>> queryAllActions(@RequestParam(value = "index") String index,
                                               @RequestParam(value = "type") String type,
                                               @RequestParam(value = "tag") String tag) {
        List<JSONObject> result = pccMeProjectPlanDtService.queryActionsByTags(index, type, tag);
        return R.success(result);
    }

    @GetMapping("/queryAllItem")
    public R<List<JSONObject>> queryAllItem(@RequestParam(value = "parentId", required = true) Integer parentId) {
        List<JSONObject> result = pccMeProjectPlanDtService.queryAllItem(parentId);
        return R.success(result);
    }

    @PostMapping("/deleteItem")
    public R<Integer> deleteItem(@RequestBody PccMeProjectPlanHdVo vo) throws Exception {
        Integer result = pccMeProjectPlanDtService.deleteItem(vo);
        return R.success(result);
    }
}
