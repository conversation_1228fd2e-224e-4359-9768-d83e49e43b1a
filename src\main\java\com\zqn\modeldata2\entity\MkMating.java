package com.zqn.modeldata2.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class MkMating {
    // 出货日期
    private String shp_date;

    // 型体编号
    private String model_no;

    // 样品单号
    private String ord_no;

    // 样品类型
    private String dev_type;

    // 楦头编号
    private String last_no;

    // 双数
    private Double tot_qty;

    // 鞋面
    private String ushelf_no;

    // 大底
    private String bshelf_no6;

    // 包粘
    private String bshelf_no3;

    // 中底
    private String bshelf_no2;

    // 中底皮
    private String bshelf_no1;

    // 配套时间
    private String mat_date;

    // 配送时间
    private String pat_date;

    // 待配送
    private Double pat_qty;

    // 配送标记
    private String pat_flag;

    // 配送单号,pat_qty,ypat_qty
    private String mating_no;

    // 已配送
    private String ypat_qty;

    private String b1_flag;

    private String b2_flag;

    private String b3_flag;

    private String b6_flag;

    private String u_flag;

    private byte[] model_pic;

    private String dp1_qty;

    private String p1_qty;

    private String status;
    // 楦头状态
    private String las_c_flag;

    @JsonFormat(
            pattern = "YYYY-MM-dd HH:mm:ss"
    )
    private Date b1_date;

    @JsonFormat(
            pattern = "YYYY-MM-dd HH:mm:ss"
    )
    private Date b2_date;

    @JsonFormat(
            pattern = "YYYY-MM-dd HH:mm:ss"
    )
    private Date b3_date;

    @JsonFormat(
            pattern = "YYYY-MM-dd HH:mm:ss"
    )
    private Date b6_date;

    @JsonFormat(
            pattern = "YYYY-MM-dd HH:mm:ss"
    )
    private Date u_date;
}
