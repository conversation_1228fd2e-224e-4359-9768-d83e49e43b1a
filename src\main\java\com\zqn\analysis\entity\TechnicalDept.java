package com.zqn.analysis.entity;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/11/29 8:42
 */
@Data
public class TechnicalDept {

    //当日开版型体数量
    private Integer todayModelNum;

    //当日开版客户数量
    private Integer todayCustomerNum;

    //技术部总人数
    private Integer technicalTotalNum;

    //SOP统计
    private Integer sopNum;

    //厂别
    private List<String> categories;

    //面部数据
    private List<Integer> faceData;

    //底部数据
    private List<Integer> bottomData;

    //对应厂别技术人员明细
    private List<TechnicalDeptDetail> technicalDeptDetails;

    private List<TechnicalDeptModelList> modelList;
}
