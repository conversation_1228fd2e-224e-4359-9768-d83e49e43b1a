<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zqn.modeldata2.mapper.FirstMapper">
    <resultMap id="ResultMap" type="java.util.HashMap">
        <result property="factory" column="FACTORY" jdbcType="VARCHAR"/>
        <result property="brand_no" column="BRAND_NO" jdbcType="VARCHAR"/>
        <result property="season_no" column="SEASON_NO" jdbcType="VARCHAR"/>
        <result property="model_no" column="MODEL_NO" jdbcType="VARCHAR"/>
        <result property="model_desc" column="MODEL_DESC" jdbcType="VARCHAR"/>
        <result property="siz_type" column="SIZ_TYPE" jdbcType="VARCHAR"/>
        <result property="bas_size" column="BAS_SIZE" jdbcType="VARCHAR"/>
        <result property="series" column="SERIES" jdbcType="VARCHAR"/>
        <result property="last_nos" column="LAST_NOS" jdbcType="VARCHAR"/>
        <result property="constr" column="CONSTR" jdbcType="VARCHAR"/>
        <result property="upper_der" column="UPPER_DER" jdbcType="VARCHAR"/>
        <result property="sole_der" column="SOLE_DER" jdbcType="VARCHAR"/>
        <result property="model_pic" column="MODEL_PIC" jdbcType="BLOB"
                typeHandler="org.apache.ibatis.type.BlobTypeHandler"/>
    </resultMap>

    <select id="getBrands" resultMap="ResultMap">
        select brand_no
        from be_brand
        where inv_flag = 'N'
          and brand_no not in ('X', 'NO', 'NS')
        order by brand_no
    </select>

    <select id="getModel" resultMap="ResultMap">
        select a.factory,    -- 厂别
               a.brand_no,   -- 品牌
               a.season_no,  -- 季节
               a.model_no,   -- 型体
               a.model_desc, -- 型体描述
               a.siz_type,   -- 码别
               a.bas_size,   -- 基本码
               a.series,     -- 系列
               c.last_nos,   -- 楦头
               a.upper_der,  -- 面版师
               a.sole_der    -- 底版师
        from ck_smodel a,
             be_moudel b,
             bf_last c,
             cb_model d
        where a.module_no = b.module_no(+)
          and b.last_seq = c.last_seq(+)
          and a.model_no = d.model_no(+)
          and a.model_no = #{model_no}
    </select>

    <select id="getModels" resultMap="ResultMap">
        select a.model_no
        from ck_smodel a,
             be_moudel b,
             bf_last c,
             cb_model d
        where a.module_no = b.module_no(+)
          and b.last_seq = c.last_seq(+)
          and a.model_no = d.model_no(+)
          and a.brand_no = #{brand_no}
        order by a.model_no
    </select>



    <select id="getModelsByShoeLast" resultMap="ResultMap">
        select a.model_no
        from ck_smodel a, be_moudel b, bf_last c, cb_model d
        where a.module_no = b.module_no(+) and b.last_seq = c.last_seq(+)
        and a.model_no = d.model_no(+)
        and a.brand_no = #{brand_no}
        <if test="shoe_last != null and shoe_last.length() > 0">
            and c.last_nos like '%'||#{shoe_last}||'%'
        </if>
        <if test="shoe_last.length() == 0">
            and c.last_nos = #{shoe_last}
        </if>
        order by a.model_no
    </select>

    <select id="getModelPicture" resultMap="ResultMap">
        select model_pic
        from ck_smodel
        where model_no = #{model_no}
    </select>

    <select id="getSizeTypeList" resultType="string">
        select a.siz_type
        from ba_sizetype a,
             be_brand b
        where a.siz_type in (select column_value as siz_type
                             from table (fn_strlist(b.siz_types, ',')))
          and b.brand_no = #{brand_no}
          and a.inv_flag = 'N'
        order by a.siz_type
    </select>

    <select id="getSizeOption" resultType="string">
        select bas_size
        from vba_sizetype
        where siz_type = #{siz_type}
        <if test="siz_type != 'UG-S'">
            order by to_number(bas_size)
        </if>
    </select>

    <update id="updateSizeType">
        update ck_smodel
        set siz_type = #{siz_type},
            upd_user = #{upd_user},
            upd_date = sysdate
        where model_no = #{model_no}
    </update>

    <update id="updateBaseSize">
        update ck_smodel
        set bas_size = #{bas_size},
            upd_user = #{upd_user},
            upd_date = sysdate
        where model_no = #{model_no}
    </update>
</mapper>