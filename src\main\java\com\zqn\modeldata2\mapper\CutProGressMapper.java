package com.zqn.modeldata2.mapper;

import com.zqn.modeldata2.entity.CutProGress;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface CutProGressMapper {

    List<CutProGress> query(@Param("startTime") Date startTime,
                            @Param("endTime") Date endTime,
                            @Param("cinStartTime") Date cinStartTime,
                            @Param("cinEndTime") Date cinEndTime,
                            @Param("brand") String brand,
                            @Param("devType") String devType,
                            @Param("externalStatus") Integer externalStatus
    );


    CutProGress queryTotalQty(@Param("cinStartTime") Date cinStartTime,
                              @Param("cinEndTime") Date cinEndTime,
                              @Param("brand") String brand,
                              @Param("devType") String devType);

    List<String> queryAllDevType();

    Integer update(@Param("ordNo") String ordNo, @Param("itemNo") String itemNo);

    List<CutProGress> selectByCondition(CutProGress condition);
}