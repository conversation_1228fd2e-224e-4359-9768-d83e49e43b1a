package com.zqn.sop.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class PccProgressRoute {

    private Integer id;
    //主题
    private String theme;
    // 厂别
    private String factory;
    // 部门
    private String dept;
    //    问题明细
    private String problem_statement;
    //  改善的目的
    private String purpose;
    // 现状把握
    private String current_states;
    //     目标设定
    private String target_setting;

    //      要素分析
    private String cause_analysis;
    //    对策立案
    private String contemeasures;

    // 实施计划
    private String action_plan;
    //    结果确认
    private String results_evaluation;

    //       标准化
    private String standardization;

    //     创建人
    private String create_by;
    //            创建日期
    @JsonFormat(pattern = "YYYY-MM-dd HH:mm:ss")
    private Date create_date;
    //  修改人
    private String update_by;
    //修改日期
    @JsonFormat(pattern = "YYYY-MM-dd HH:mm:ss")
    private Date update_date;

    // 提案人
    private String proposer;


}
