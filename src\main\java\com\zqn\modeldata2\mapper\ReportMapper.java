package com.zqn.modeldata2.mapper;

import com.zqn.modeldata2.entity.CkSmodelp;
import com.zqn.modeldata2.entity.Sign;
import com.zqn.modeldata2.entity.SignFile;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface ReportMapper {
    Map<String, Object> getPicture(@Param("model_no") String model_no);

    Map<String, Object> getInfo(@Param("model_no") String model_no);

    List<Object> getTitle(@Param("model_no") String model_no, @Param("siz_type") String siz_type, @Param("bas_size") double bas_size);

    Integer getFullSize(@Param("model_no") String model_no);

    Integer updateFullSize(@Param("model_no") String model_no, @Param("full_size") Integer full_size);

    List<Object> getCount1(@Param("model_no") String model_no, @Param("siz_type") String siz_type, @Param("bas_size") double bas_size, @Param("procs_type") int procs_type);

    List<Object> getCount2(@Param("model_no") String model_no, @Param("siz_type") String siz_type, @Param("bas_size") double bas_size, @Param("procs_type") int procs_type);

    CkSmodelp getInsertInfo(@Param("model_no") String model_no);

    Sign getSignatureById(@Param("sign") Sign sign);

    SignFile getSignatureFile(@Param("signFile") SignFile signFile);

    Sign getChkSignature(@Param("sign") Sign sign);

    Sign getUppSignature(@Param("sign") Sign sign);

    Sign getSolSignature(@Param("sign") Sign sign);

    Integer addChkSignature(@Param("signFile") SignFile signFile);

    Integer addUppSignature(@Param("signFile") SignFile signFile);

    Integer addSolSignature(@Param("signFile") SignFile signFile);

    Integer updateChkSignature(@Param("signFile") SignFile signFile);

    Integer updateUppSignature(@Param("signFile") SignFile signFile);

    Integer updateSolSignature(@Param("signFile") SignFile signFile);

    Integer resetChkSignature(@Param("signFile") SignFile signFile);

    Integer resetUppSignature(@Param("signFile") SignFile signFile);

    Integer resetSolSignature(@Param("signFile") SignFile signFile);
}
