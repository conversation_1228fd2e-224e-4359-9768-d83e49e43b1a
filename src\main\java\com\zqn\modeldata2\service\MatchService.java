package com.zqn.modeldata2.service;

import com.zqn.modeldata2.entity.MkMating;
import com.zqn.modeldata2.entity.MkNpatQcDet;
import com.zqn.modeldata2.entity.MkNpatQcTot;

import java.util.List;

public interface MatchService {
    /**
     * 已配套待配送
     */
    List<MkMating> getWaitSend(int page_no, int page_size);

    /**
     * 待配送数量
     */
    Integer getSendCount();

    /**
     * 修改配送状态
     */
    Integer updateState(String pdLine, String ord_no, String mating_no);

    /**
     * 已配套待投入
     */
    List<MkMating> getWaitInput(int page_no, int page_size, int status);

    /**
     * 待投入数量
     */
    Integer getInputCount(int status);

    /**
     * 未配套库存查询
     */
    List<MkNpatQcTot> getNoMatch(Integer status);

    /**
     * 已配套楦头确认
     */
    List<MkNpatQcDet> getNoMatchDetail(String brand_no, Integer status);

    /**
     * 已配套楦头确认  ——明细
     */
    List<Object> getMatchConfirmDetail(String ord_No, String last_no);

    /**
     * 已配套楦头确认  ——操作
     */
    List<Object> getMatchConfirmMXXX(String ord_No, String last_no);


    /**
     * 已配套楦头确认
     */
    List<MkNpatQcDet> getMatchConfirm(int page_no, int page_size, int status);

    /**
     * 修改配送状态
     */
    Integer updateXuanTouState(String mating_no);

    /**
     * 未配套库存明细
     */
    List<MkNpatQcDet> formingQuery(int page_no, int page_size, Integer status, Integer status2);

    /**
     * 未配套库存明细数量
     */
    List<Integer> getformingCount(int status, int status2, int status3);

    /**
     * 保存
     */
    Integer insertConfirmDetail(List<String> sizeList, List<String> sizeListCot,
                                String ord_no, String last_no, String width, String lr_mark);


}
