package com.zqn.modeldata2.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.entity.sop.*;
import com.zqn.modeldata2.service.SopService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * SOP 控制层
 */
@RestController
@RequestMapping("/sop")
public class SopController {
    @Autowired
    private SopService sopService;

    /**
     * 获取制程列表
     */
    @GetMapping("/getOperationList")
    public R<List<SopOperation>> getOperationList() {
        List<SopOperation> result = sopService.getOperationList();
        if (result.isEmpty()) {
            return R.error("暂无制程列表数据！");
        }
        return R.success(result);
    }

    /**
     * 获取品牌列表
     */
    @GetMapping("/getBrandList")
    public R<List<SopBrand>> getBrandList() {
        List<SopBrand> result = sopService.getBrandList();
        if (result.isEmpty()) {
            return R.error("暂无品牌列表数据！");
        }
        return R.success(result);
    }

    /**
     * 获取型体列表
     */
    @PostMapping("/getModelList")
    public R<List<SopModel>> getModelList(@RequestBody Map<String, String> params) {
        String brand = params.get("brand");
        List<SopModel> result = sopService.getModelList(brand);
        if (result.isEmpty()) {
            return R.error("暂无型体列表数据！");
        }
        return R.success(result);
    }

    /**
     * 获取所有型体列表
     */
    @GetMapping("/getAllModelList")
    public R<List<String>> getAllModelList() {
        List<String> result = sopService.getAllModelList();
        if (result.isEmpty()) {
            return R.error("暂无型体列表数据！");
        }
        return R.success(result);
    }

    /**
     * 获取型体鞋图
     */
    @PostMapping("/getModelPicture")
    public R<SopPicture> getModelPicture(@RequestBody Map<String, String> params) {
        String model = params.get("model");
        SopPicture result = sopService.getModelPicture(model);
        if (result == null) {
            return R.error("暂无型体列表数据！");
        }
        return R.success(result);
    }

    /**
     * 获取工序信息类型列表
     */
    @PostMapping("/getInfoTypeList")
    public R<List<String>> getInfoTypeList(@RequestBody Map<String, String> params) {
        String operation = params.get("operation");
        List<String> result = sopService.getInfoTypeList(operation);
        if (result.isEmpty()) {
            return R.error("暂无工序信息类型列表数据！");
        }
        return R.success(result);
    }

    /**
     * 获取工序信息材质列表
     */
    @PostMapping("/getInfoMaterialList")
    public R<List<SopMaterial>> getInfoMaterialList(@RequestBody Map<String, String> params) {
        String model = params.get("model");
        String operation = params.get("operation");
        List<SopMaterial> result = sopService.getInfoMaterialList(model, operation);
        if (result.isEmpty()) {
            return R.error("暂无工序信息材质列表数据！");
        }
        return R.success(result);
    }

    /**
     * 获取工序信息部位列表
     */
    @PostMapping("/getInfoPartList")
    public R<List<String>> getInfoPartList() {
        List<String> result = sopService.getInfoPartList();
        if (result.isEmpty()) {
            return R.error("暂无工序信息部位列表数据！");
        }
        return R.success(result);
    }

    /**
     * 获取楦头编号列表
     */
    @PostMapping("/getLastNosList")
    public R<List<String>> getLastNosList(@RequestBody Map<String, String> params) {
        String brand = params.get("brand");
        List<String> result = sopService.getLastNosList(brand);
        if (result.isEmpty()) {
            return R.error("暂无楦头编号列表数据！");
        }
        return R.success(result);
    }

    /**
     * 获取 Outsole 列表
     */
    @PostMapping("/getOsNoList")
    public R<List<String>> getOsNoList(@RequestBody Map<String, String> params) {
        String brand = params.get("brand");
        List<String> result = sopService.getOsNoList(brand);
        if (result.isEmpty()) {
            return R.error("暂无 Outsole 列表数据！");
        }
        return R.success(result);
    }

    /**
     * 获取工序信息列表
     */
    @PostMapping("/getProcessInfoList")
    public R<List<SopProcessInfo>> getProcessInfoList(@RequestBody SopProcessInfo sopProcessInfo) {
        List<SopProcessInfo> result = sopService.getProcessInfoList(sopProcessInfo);
        if (result.isEmpty()) {
            return R.error("暂无工序信息列表数据！");
        }
        return R.success(result);
    }

    /**
     * 修改工序信息生产类型
     */
    @PostMapping("/updateInfoType")
    public R<String> updateInfoType(@RequestBody SopProcessInfo sopProcessInfo) {
        int result = sopService.updateInfoType(sopProcessInfo);
        if (result <= 0) {
            return R.error("工序信息生产类型修改失败！");
        }
        return R.success("工序信息生产类型修改成功！");
    }

    /**
     * 修改工序信息材质
     */
    @PostMapping("/updateInfoMaterial")
    public R<String> updateInfoMaterial(@RequestBody SopProcessInfo sopProcessInfo) {
        int result = sopService.updateInfoMaterial(sopProcessInfo);
        if (result <= 0) {
            return R.error("工序信息材质修改失败！");
        }
        return R.success("工序信息材质修改成功！");
    }

    /**
     * 修改工序信息部位
     */
    @PostMapping("/updateInfoPart")
    public R<String> updateInfoPart(@RequestBody SopProcessInfo sopProcessInfo) {
        int result = sopService.updateInfoPart(sopProcessInfo);
        if (result <= 0) {
            return R.error("工序信息部位修改失败！");
        }
        return R.success("工序信息部位修改成功！");
    }

    /**
     * 添加工序信息
     */
    @PostMapping("/addProcessInfo")
    public R<String> addProcessInfo(@RequestBody SopProcessInfo sopProcessInfo) {
        int result = sopService.addProcessInfo(sopProcessInfo);
        if (result <= 0) {
            return R.error("工序信息添加失败！");
        }
        return R.success("工序信息添加成功！");
    }

    /**
     * 删除工序信息
     */
    @PostMapping("/deleteProcessInfo")
    public R<String> deleteProcessInfo(@RequestBody Map<String, List<SopProcessInfo>> params) {
        List<SopProcessInfo> processInfoList = params.get("processInfoList") != null ? params.get("processInfoList") : new ArrayList<>();
        int result = sopService.deleteProcessInfo(processInfoList);
        if (result <= 0) {
            return R.error("工序信息删除失败！");
        }
        return R.success("工序信息删除成功！");
    }

    /**
     * 获取工序流程列表
     */
    @PostMapping("/getProcessFlowList")
    public R<List<SopProcessFlow>> getProcessFlowList(@RequestBody SopProcessFlow sopProcessFlow) {
        List<SopProcessFlow> result = sopService.getProcessFlowList(sopProcessFlow);
        if (result.isEmpty()) {
            return R.error("暂无工序流程列表数据！");
        }
        return R.success(result);
    }

    /**
     * 添加工序流程
     */
    @PostMapping("/addProcessFlow")
    public R<String> addProcessFlow(@RequestBody SopProcessFlow sopProcessFlow) {
        int result = sopService.addProcessFlow(sopProcessFlow);
        if (result <= 0) {
            return R.error("工序流程添加失败！");
        }
        return R.success("工序流程添加成功！");
    }

    /**
     * 插入工序流程
     */
    @PostMapping("/insertProcessFlow")
    public R<String> insertProcessFlow(@RequestBody SopProcessFlow sopProcessFlow) {
        int result = sopService.insertProcessFlow(sopProcessFlow);
        if (result <= 0) {
            return R.error("工序流程插入失败！");
        }
        return R.success("工序流程插入成功！");
    }

    /**
     * 获取模板列表
     */
    @PostMapping("/getTemplateList")
    public R<List<SopTemplate>> getTemplateList(@RequestBody Map<String, String> params) {
        String operation = params.get("operation");
        List<SopTemplate> result = sopService.getTemplateList(operation);
        if (result.isEmpty()) {
            return R.error("暂无模板列表数据！");
        }
        return R.success(result);
    }

    /**
     * 模板导入工序流程
     */
    @PostMapping("/templateImportFlow")
    public R<String> templateImportFlow(@RequestBody SopProcessFlow sopProcessFlow) {
        int result = sopService.templateImportFlow(sopProcessFlow);
        if (result <= 0) {
            return R.error("模板导入失败！");
        }
        return R.success("模板导入成功！");
    }

    /**
     * 修改工序流程加工段
     */
    @PostMapping("/updateFlowSection")
    public R<String> updateFlowSection(@RequestBody SopProcessFlow sopProcessFlow) {
        int result = sopService.updateFlowSection(sopProcessFlow);
        if (result <= 0) {
            return R.error("工序流程加工段修改失败！");
        }
        return R.success("工序流程加工段修改成功！");
    }

    /**
     * 排序工序流程加工段
     */
    @PostMapping("/sortSection")
    public R<String> sortSection(@RequestBody SopProcessFlow sopProcessFlow) {
        int result = sopService.sortSection(sopProcessFlow);
        if (result <= 0) {
            return R.error("工序流程加工段排序失败！");
        }
        return R.success("工序流程加工段排序成功！");
    }

    /**
     * 修改工序流程序号
     */
    @PostMapping("/updateFlowSequence")
    public R<String> updateFlowSequence(@RequestBody SopProcessFlow sopProcessFlow) {
        int result = sopService.updateFlowSequence(sopProcessFlow);
        if (result <= 0) {
            return R.error("工序流程序号修改失败！");
        }
        return R.success("工序流程序号修改成功！");
    }

    /**
     * 获取工序流程详情
     */
    @PostMapping("/getFlowDetail")
    public R<SopFlowDetail> getFlowDetail(@RequestBody SopProcessFlow sopProcessFlow) {
        SopFlowDetail result = sopService.getFlowDetail(sopProcessFlow);
        if (result == null) {
            return R.error("暂无工序流程详情数据！");
        }
        return R.success(result);
    }

    /**
     * 保存工序流程详情
     */
    @PostMapping("/saveFlowDetail")
    public R<String> saveFlowDetail(@RequestBody SopFlowDetail sopFlowDetail) {
        int result = sopService.saveFlowDetail(sopFlowDetail);
        if (result <= 0) {
            return R.error("工序流程详情保存失败！");
        }
        return R.success("工序流程详情保存成功！");
    }

    /**
     * 删除工序流程
     */
    @PostMapping("/deleteProcessFlow")
    public R<String> deleteProcessFlow(@RequestBody Map<String, List<SopProcessFlow>> param) {
        List<SopProcessFlow> processFlowList = param.get("processFlowList") != null ? param.get("processFlowList") : new ArrayList<>();
        int result = sopService.deleteProcessFlow(processFlowList);
        if (result <= 0) {
            return R.error("工序流程删除失败！");
        }
        return R.success("工序流程删除成功！");
    }

    /**
     * 重置工序流程
     */
    @PostMapping("/resetProcessFlow")
    public R<String> resetProcessFlow(@RequestBody SopProcessInfo sopProcessInfo) {
        int result = sopService.resetProcessFlow(sopProcessInfo);
        if (result <= 0) {
            return R.error("工序流程重置失败！");
        }
        return R.success("工序流程重置成功！");
    }

    /**
     * 获取选项列表
     */
    @PostMapping("/getFlowOptionList")
    public R<List<String>> getFlowOptionList(@RequestBody Map<String, Integer> params) {
        Integer type = params.get("type");
        Integer dept = params.get("dept");
        List<String> result = sopService.getFlowOptionList(type, dept);
        if (result.isEmpty()) {
            return R.error("暂无选项列表数据！");
        }
        return R.success(result);
    }

    /**
     * 获取动作列表
     */
    @PostMapping("/getFlowActionList")
    public R<List<SopFlowAction>> getFlowActionList(@RequestBody Map<String, String> params) {
        String proSeq = params.get("proSeq");
        List<SopFlowAction> result = sopService.getFlowActionList(proSeq);
        if (result.isEmpty()) {
            return R.error("暂无动作列表数据！");
        }
        return R.success(result);
    }

    /**
     * 选择动作
     */
    @PostMapping("/selectFlowAction")
    public R<Map<String, String>> selectFlowAction(@RequestBody Map<String, Integer> params) {
        int id = params.get("id");
        Map<String, String> result = sopService.selectFlowAction(id);
        if (result.isEmpty()) {
            return R.error("暂无动作详情数据！");
        }
        return R.success(result);
    }

    /**
     * 获取流程预览列表
     */
    @PostMapping("/getPreviewList")
    public R<List<SopPreview>> getPreviewList(@RequestBody Map<String, String> params) {
        String model = params.get("model");
        String operation = params.get("operation");
        String rtgCode = params.get("rtgCode");
        List<SopPreview> result = sopService.getPreviewList(model, operation, rtgCode);
        if (result.isEmpty()) {
            return R.error("暂无流程预览列表数据！");
        }
        return R.success(result);
    }

    /**
     * 复制工序
     */
    @PostMapping("/copyProcess")
    public R<String> copyProcess(@RequestBody Map<String, Object> params) throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        SopProcessInfo sopProcessInfo = params.get("processInfo") != null ? objectMapper.readValue(params.get("processInfo").toString(), SopProcessInfo.class) : null;
        boolean isCopyPicture = params.get("isCopyPicture") != null && (boolean) params.get("isCopyPicture");
        List<String> targetModelList = (List<String>) params.get("targetModelList");
        int result = sopService.copyProcess(sopProcessInfo, isCopyPicture, targetModelList);
        if (result < 0) {
            return R.success("工序复制失败！");
        }
        return R.success("工序复制成功！");
    }

    /**
     * 获取整体流程
     */
    @PostMapping("/getOverallFlow")
    public R<List<SopFlowDetail>> getOverallFlow(@RequestBody Map<String, String> params) {
        String model = params.get("model") != null ? params.get("model") : "";
        String operation = params.get("operation") != null ? params.get("operation") : "";
        String rtgCode = params.get("rtgCode") != null ? params.get("rtgCode") : "";
        List<SopFlowDetail> result = sopService.getOverallFlow(model, operation, rtgCode);
        if (result.isEmpty()) {
            return R.error("暂无整体流程数据！");
        }
        return R.success(result);
    }

    /**
     * 选择整体流程动作
     */
    @PostMapping("/selectOverallAction")
    public R<String> selectOverallAction(@RequestBody Map<String, Object> params) throws JsonProcessingException {
        int id = params.get("id") != null ? Integer.parseInt(params.get("id").toString()) : 0;
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        SopFlowDetail sopFlowDetail = params.get("sopFlowDetail") != null ? objectMapper.readValue(params.get("sopFlowDetail").toString(), SopFlowDetail.class) : null;
        int result = sopService.selectOverallAction(id, sopFlowDetail);
        if (result <= 0) {
            return R.error("整体流程动作选择失败！");
        }
        return R.success("整体流程动作选择成功！");
    }

    /**
     * 修改整体流程
     */
    @PostMapping("/updateOverallFlow")
    public R<String> updateOverallFlow(@RequestBody SopFlowDetail sopFlowDetail) {
        int result = sopService.updateOverallFlow(sopFlowDetail);
        if (result <= 0) {
            return R.error("整体流程修改失败！");
        }
        return R.success("整体流程修改成功！");
    }

    /**
     * 修改整体流程图片
     */
    @PostMapping("/updateOverallPicture")
    public R<String> updateOverallPicture(@RequestBody SopFlowDetail sopFlowDetail) {
        int result = sopService.updateOverallPicture(sopFlowDetail);
        if (result <= 0) {
            return R.error("整体流程图片修改失败！");
        }
        return R.success("整体流程图片修改成功！");
    }

    /**
     * 添加整体流程
     */
    @PostMapping("/addOverallFlow")
    public R<String> addOverallFlow(@RequestBody Map<String, Object> params) throws JsonProcessingException {
        String model = params.get("model").toString();
        String operation = params.get("operation").toString();
        String rtgCode = params.get("rtgCode").toString();
        String user = params.get("user").toString();
        ObjectMapper objectMapper = new ObjectMapper();
        List<SopFlowAction> actionList = params.get("actionList") != null ? objectMapper.readValue(params.get("actionList").toString(), new TypeReference<List<SopFlowAction>>() {}) : null;
        SopProcessFlow sopProcessFlow = new SopProcessFlow();
        sopProcessFlow.setModel(model);
        sopProcessFlow.setOperation(operation);
        sopProcessFlow.setRtgCode(rtgCode);
        sopProcessFlow.setInsUser(user);
        sopProcessFlow.setUpdUser(user);
        int result = sopService.addOverallFlow(sopProcessFlow, actionList);
        if (result <= 0) {
            return R.error("整体流程添加失败！");
        }
        return R.success("整体流程添加成功！");
    }

    /**
     * 修改工序完成状态
     */
    @PostMapping("/updateCompleteState")
    public R<String> updateCompleteState(@RequestBody Map<String, String> params) {
        String model = params.get("model");
        String operation = params.get("operation");
        String rtgCode = params.get("rtgCode");
        String cplFlag = params.get("cplFlag");
        int result = sopService.updateCompleteState(model, operation, rtgCode, cplFlag);
        if (result <= 0) {
            return R.error("工序完成状态修改失败！");
        }
        return R.success("工序完成状态修改成功！");
    }

    /**
     * 获取创建人列表
     */
    @GetMapping("/getCreatorList")
    public R<List<SopCreator>> getCreatorList() {
        List<SopCreator> result = sopService.getCreatorList();
        if (result.isEmpty()) {
            return R.error("暂无创建人列表数据！");
        }
        return R.success(result);
    }

    /**
     * 获取制程选项
     */
    @PostMapping("getOperationOption")
    public R<List<String>> getOperationOption() {
        List<String> result = sopService.getOperationOption();
        if (result.isEmpty()) {
            return R.error("暂无制程选项数据！");
        }
        return R.success(result);
    }

    /**
     * 获取标签选项
     */
    @PostMapping("/getTagOption")
    public R<List<SopTagOption>> getTagOption() {
        List<SopTagOption> result = sopService.getTagOption();
        if (result.isEmpty()) {
            return R.error("暂无标签选项数据！");
        }
        return R.success(result);
    }

    /**
     * 获取翻译型体列表
     */
    @PostMapping("/getTranslationModelList")
    public R<List<String>> getTranslationModelList(@RequestBody Map<String, Boolean> params) {
        boolean isCompleted = params.get("isCompleted") && (boolean) params.get("isCompleted");
        List<String> result = sopService.getTranslationModelList(isCompleted);
        if (result.isEmpty()) {
            return R.error("暂无翻译型体列表数据！");
        }
        return R.success(result);
    }

    /**
     * 获取翻译制程列表
     */
    @PostMapping("/getTranslationOperationList")
    public R<List<String>> getTranslationOperationList(@RequestBody Map<String, Boolean> params) {
        boolean isCompleted = params.get("isCompleted") && (boolean) params.get("isCompleted");
        List<String> result = sopService.getTranslationOperationList(isCompleted);
        if (result.isEmpty()) {
            return R.error("暂无翻译制程列表数据！");
        }
        return R.success(result);
    }

    /**
     * 获取翻译详细制程列表
     */
    @PostMapping("/getTranslationProSeqList")
    public R<List<String>> getTranslationProSeqList(@RequestBody Map<String, Boolean> params) {
        boolean isCompleted = params.get("isCompleted") && (boolean) params.get("isCompleted");
        List<String> result = sopService.getTranslationProSeqList(isCompleted);
        if (result.isEmpty()) {
            return R.error("暂无翻译详细制程列表数据！");
        }
        return R.success(result);
    }

    /**
     *  获取工序翻译列表
     */
    @PostMapping("/getTranslationList")
    public R<List<SopTranslation>> getTranslationList(@RequestBody Map<String, Object> params) {
        String model = params.get("model") != null ? params.get("model").toString() : "";
        String operation = params.get("operation") != null ? params.get("operation").toString() : "";
        String proSeq = params.get("proSeq") != null ? params.get("proSeq").toString() : "";
        int pageNo = params.get("pageNo") != null ? (int) params.get("pageNo") : 1;
        int pageSize = params.get("pageSize") != null ? (int) params.get("pageSize") : 100;
        boolean isCompleted = params.get("isCompleted") != null && (boolean) params.get("isCompleted");
        boolean needTranslation = params.get("needTranslation") != null && (boolean) params.get("needTranslation");
        List<SopTranslation> result = sopService.getTranslationList(model, operation, proSeq, pageNo, pageSize, isCompleted, needTranslation);
        if (result.isEmpty()) {
            return R.error("暂无工序翻译列表数据！");
        }
        return R.success(result);
    }

    /**
     * 获取已完成待翻译数量
     */
    @GetMapping("/getTranslationCount")
    public R<Integer> getTranslationCount() {
        int result = sopService.getTranslationCount();
        if (result < 0) {
            return R.error("暂无已完成待翻译数据！");
        }
        return R.success(result);
    }

    /**
     * 修改工序翻译
     */
    @PostMapping("/updateTranslation")
    public R<String> updateTranslation(@RequestBody SopTranslation sopTranslation) {
        int result = sopService.updateTranslation(sopTranslation);
        if (result < 0) {
            return R.error("工序翻译修改失败！");
        }
        return R.success("工序翻译修改成功！");
    }

    /**
     * 获取热冷压规格列表
     */
    @PostMapping("getPressList")
    public R<List<SopPress>> getPressList(@RequestBody Map<String, String> params) {
        String model_no = params.get("model_no") != null ? params.get("model_no") : "";
        String operation = params.get("operation") != null ? params.get("operation") : "";
        String rtg_code = params.get("rtg_code") != null ? params.get("rtg_code") : "";
        List<SopPress> result = sopService.getPressList(model_no, operation, rtg_code);
        if (result.isEmpty()) {
            return R.error("暂无热冷压规格列表数据！");
        }
        return R.success(result);
    }

    /**
     * 保存热冷压规格
     */
    @PostMapping("savePress")
    public R<String> savePress(@RequestBody List<SopPress> pressList) {
        int result = sopService.savePress(pressList);
        if (result <= 0) {
            return R.error("保存热冷压规格失败！");
        }
        return R.success("保存热冷压规格成功！");
    }
}
