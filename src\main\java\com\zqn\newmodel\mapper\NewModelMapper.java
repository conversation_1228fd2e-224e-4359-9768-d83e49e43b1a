package com.zqn.newmodel.mapper;

import com.zqn.newmodel.entity.NewModel;
import com.zqn.newmodel.entity.NewModelTable2;
import com.zqn.newmodel.entity.NewModelTable3;
import com.zqn.newmodel.entity.NewModelTitle;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Mapper
public interface NewModelMapper {

    NewModelTitle queryHd(@Param(value = "ordNo") String ordNo);

    void pdGcSordereq(Map<String, Object> params);

    List<NewModelTable2> querySize(@Param(value = "ordNo") String ordNo);

    List<NewModelTable3> queryDt(@Param(value = "ordNo") String ordNo,
                                 @Param(value = "ord_qty") BigDecimal ordQty,
                                 @Param("brand_no") String brandNo);

    List<NewModelTable2> querySizeCaption(@Param(value = "ordNo") String ordNo,
                                          @Param(value = "sizType") String sizType);
}