package com.zqn.modeldata2.mapper;

import com.zqn.modeldata2.entity.ColumnMap;
import com.zqn.modeldata2.entity.MkSlastbar;
import com.zqn.modeldata2.entity.MkSlastbarPlus;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface ScanMapper {
    Map<String, Object> getCount(@Param("user_id") String user_id, @Param("ins_user") String ins_user, @Param("cl_flag") String cl_flag, @Param("today") boolean today);

    List<MkSlastbarPlus> getScanException(@Param("user_id") String user_id, @Param("ins_user") String ins_user, @Param("cl_flag") String cl_flag, @Param("today") boolean today, @Param("page_no") int page_no, @Param("page_size") int page_size);

    List<ColumnMap> getDept();

    List<ColumnMap> getDesc();

    List<ColumnMap> getAddr();

    Map<String, Object> getBarCodeA(@Param("ord_no") String ord_no);

    Map<String, Object> getBarCodeB(@Param("column_seq") String column_seq);

    Map<String, Object> getBarCodeC(@Param("column_seq") String column_seq);

    Map<String, Object> getBarCodeD(@Param("column_seq") String column_seq);

    List<ColumnMap> getOption(@Param("mkSlastbar") MkSlastbar mkSlastbar);

    int addException(@Param("mkSlastbar") MkSlastbar mkSlastbar);

    int updateException(@Param("mkSlastbar") MkSlastbar mkSlastbar);

    int updateMethod(@Param("mkSlastbar") MkSlastbar mkSlastbar);

    int updateState(@Param("mkSlastbar") MkSlastbar mkSlastbar);

    int deleteException(@Param("mkSlastbar") MkSlastbar mkSlastbar);
}
