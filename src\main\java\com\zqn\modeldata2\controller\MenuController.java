package com.zqn.modeldata2.controller;

import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.entity.Menu;
import com.zqn.modeldata2.service.MenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.util.List;

@RestController
@RequestMapping("/menu")
public class MenuController {
    @Autowired
    private MenuService menuService;

    @GetMapping("/findByPart")
    public R<List<Menu>> findByPart(HttpServletRequest request, @RequestParam("menuParentNo") String menuParentNo, @RequestParam("userNo") String userNo) {
        HttpSession session = request.getSession();
        session.setAttribute("customValue", "your_custom_value_here");
        return R.success(menuService.findByPart(menuParentNo, userNo));
    }

    @GetMapping("/findCommonyUsed")
    public R<List<Menu>> findCommonyUsed(@RequestParam("userNo") String userNo) {
        return R.success(menuService.findCommonyUsed(userNo));
    }

    @PostMapping("/editCommonyUsed")
    public R<Integer> editCommonyUsed(@RequestBody List<Menu> menus) {
        return R.success(menuService.editCommonyUsed(menus));
    }

    /**
     * 查询用户的菜单
     *
     * @return
     */
    @GetMapping("/findByUser")
    public R<List<Menu>> findByUser(@RequestParam("userNo") String userNo) {
        return R.success(menuService.findByUser(userNo));
    }

    /**
     * 编辑用户菜单权限
     *
     * @param menus
     * @return
     */
    @PostMapping("/editMenuByUser")
    public R<Integer> editMenuByUser(@RequestBody List<Menu> menus) {
        return R.success(menuService.editMenuByUser(menus));
    }


    /**
     * 查询用户的菜单
     *
     * @return
     */
    @GetMapping("/findReportByUser")
    public R<List<Menu>> findReportByUser(@RequestParam("userNo") String userNo) {
        return R.success(menuService.findReportByUser(userNo));
    }

    @GetMapping("/buttonQuery")
    public R<Boolean> buttonQuery(@RequestParam("url") String url,
                                  @RequestParam("loginUser") String loginUser) {
        return R.success(menuService.buttonQuery(url, loginUser));
    }

}