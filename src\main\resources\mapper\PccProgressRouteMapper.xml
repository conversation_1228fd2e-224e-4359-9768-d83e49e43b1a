<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zqn.sop.mapper.PccProgressRouteMapper">

    <delete id="delete">
        delete from PCC_PROGRESS_ROUTE where id = #{id}
    </delete>

    <select id="query" resultType="com.zqn.sop.entity.PccProgressRoute">
        select *
        from PCC_PROGRESS_ROUTE
        <where>
            <if test="factory != null and factory != ''">
                AND factory = #{factory}
            </if>
            <if test="dept != null and dept != ''">
                AND dept = #{dept}
            </if>
        </where>
        order by create_date desc
    </select>

    <insert id="save"  useGeneratedKeys="true" keyProperty="id" keyColumn="ID">
        INSERT INTO PCC_PROGRESS_ROUTE (PROPOSER,
            THEME, FACTORY, DEPT, PROBLEM_STATEMENT, PURPOSE, CURRENT_STATES, TARGET_SETTING,
            CAUSE_ANALYSIS, CONTEMEASURES, ACTION_PLAN, RESULTS_EVALUATION, STANDARDIZATION,
            CREATE_BY,CREATE_DATE, UPDATE_BY, UPDATE_DATE
        ) VALUES ( #{route.proposer},
                     #{route.theme}, #{route.factory}, #{route.dept}, #{route.problem_statement}, #{route.purpose},
                     #{route.current_states}, #{route.target_setting}, #{route.cause_analysis}, #{route.contemeasures},
                     #{route.action_plan}, #{route.results_evaluation}, #{route.standardization}, #{route.create_by},
                     #{route.create_date},#{route.update_by}, #{route.update_date}
                 )
    </insert>


    <update id="udpate">
        UPDATE PCC_PROGRESS_ROUTE
        <set>
            <if test="route.theme != null">
                THEME = #{route.theme},
            </if>
            <if test="route.proposer != null">
                PROPOSER = #{route.proposer},
            </if>
            <if test="route.factory != null">
                FACTORY = #{route.factory},
            </if>
            <if test="route.dept != null">
                DEPT = #{route.dept},
            </if>
            <if test="route.problem_statement != null">
                PROBLEM_STATEMENT = #{route.problem_statement},
            </if>
            <if test="route.purpose != null">
                PURPOSE = #{route.purpose},
            </if>
            <if test="route.current_states != null">
                CURRENT_STATES = #{route.current_states},
            </if>
            <if test="route.target_setting != null">
                TARGET_SETTING = #{route.target_setting},
            </if>
            <if test="route.cause_analysis != null">
                CAUSE_ANALYSIS = #{route.cause_analysis},
            </if>
            <if test="route.contemeasures != null">
                CONTEMEASURES = #{route.contemeasures},
            </if>
            <if test="route.action_plan != null">
                ACTION_PLAN = #{route.action_plan},
            </if>
            <if test="route.results_evaluation != null">
                RESULTS_EVALUATION = #{route.results_evaluation},
            </if>
            <if test="route.standardization != null">
                STANDARDIZATION = #{route.standardization},
            </if>
            <if test="route.update_by != null">
                UPDATE_BY = #{route.update_by},
            </if>
            <if test="route.update_date != null">
                UPDATE_DATE = #{route.update_date},
            </if>
        </set>
        WHERE ID = #{route.id}
    </update>


    <select id="getById" resultType="com.zqn.sop.entity.PccProgressRoute">
        select * from PCC_PROGRESS_ROUTE where id = #{id}
    </select>

    <select id="getByFactoryAndDept" resultType="com.zqn.sop.entity.PccProgressRoute">
        select * from PCC_PROGRESS_ROUTE where factory = #{factory} and dept = #{dept}
    </select>

</mapper>