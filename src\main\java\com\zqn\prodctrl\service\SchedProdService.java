package com.zqn.prodctrl.service;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.zqn.prodctrl.entity.SchedProd;

import java.util.Date;
import java.util.List;

public interface SchedProdService {

    PageInfo<SchedProd> query(int pageNo, int pageSize, Date startTime, Date endTime, String brand, String ordNo, String devType,String pFlag, String department, String phase, String pdLine);

    List<JSONObject> queryAllDevType();

    String edit(SchedProd schedProd) throws Exception;
}
