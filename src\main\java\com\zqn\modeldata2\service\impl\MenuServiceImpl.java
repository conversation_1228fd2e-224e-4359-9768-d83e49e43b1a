package com.zqn.modeldata2.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.github.houbb.heaven.util.lang.StringUtil;
import com.zqn.modeldata2.entity.Menu;
import com.zqn.modeldata2.mapper.MenuMapper;
import com.zqn.modeldata2.service.MenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@DS("app")
public class MenuServiceImpl implements MenuService {

    @Autowired
    private MenuMapper menuMapper;

    /**
     * 查询所有菜单，包括分类
     *
     * @param partNo
     * @param userNo
     * @return
     */
    @Override
    public List<Menu> findByPart(String partNo, String userNo) {
        //菜单分类
        List<Menu> menuList = menuMapper.findByPart(partNo);
        //菜单
        List<Menu> childMenus = menuMapper.findAllMenu();

        //根据权限过滤菜单
        childMenus = filtMenu(childMenus, userNo);
        List<Menu> menuByUser = menuMapper.findCommonyUsedMenuByUser(userNo);
        menuByUser = filtMenu(menuByUser, userNo);
        int i = 0;
        for (Menu menu : menuList) {
            List<Menu> menus = new ArrayList<>();
            for (Menu childMenu : childMenus) {
                if (childMenu.getMenuParentNo().equals(menu.getMenuNo())) {
                    menus.add(childMenu);
                }
            }
            if (menu.getMenuNo().equals(1)) {
                menu.setMenuList(menuByUser);
            } else {
                menu.setMenuList(menus);
            }
            menu.setMenuNo(i);
            i++;
        }

        return menuList;
    }

    /**
     * 查找用户常用菜单，根据记录设置选中
     *
     * @param userNo
     * @return
     */
    @Override
    public List<Menu> findCommonyUsed(String userNo) {
        List<Menu> allMenu = menuMapper.findAllMenu();
        List<Menu> menuByUser = menuMapper.findCommonyUsedMenuByUser(userNo);
        List<Integer> menuNoByUser = menuByUser.stream().map(Menu::getMenuNo).collect(Collectors.toList());
        for (Menu menu : allMenu) {
            if (menuNoByUser.contains(menu.getMenuNo())) {
                menu.setChecked(true);
            } else {
                menu.setChecked(false);
            }
        }
        allMenu = filtMenu(allMenu, userNo);
        return allMenu;
    }

    /**
     * 编辑用户常用菜单
     *
     * @param menus
     * @return
     */
    @Transactional
    @Override
    public Integer editCommonyUsed(List<Menu> menus) {
        if (!CollectionUtils.isEmpty(menus)) {
            String userNo = menus.get(0).getUserNo();

            List<Menu> allMenu = menuMapper.findAllMenu();
            List<Integer> menuNos = menus.stream().map(Menu::getMenuNo).collect(Collectors.toList());
            List<Menu> menuByUser = menuMapper.findCommonyUsedMenuByUser(userNo);
            List<Integer> hasMenuNo = menuByUser.stream().map(Menu::getMenuNo).collect(Collectors.toList());

            if (menus.get(0).getMenuNo().equals(-1)) {
                //删除全部常用菜单
                menuMapper.deleteByUser(userNo, hasMenuNo);
            } else {
                //找出需要删除的
                List<Menu> delMenus = allMenu.stream().filter(x -> !menuNos.contains(x.getMenuNo())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(delMenus)) {
                    List<Integer> delNos = delMenus.stream().map(Menu::getMenuNo).collect(Collectors.toList());
                    menuMapper.deleteByUser(userNo, delNos);
                }
            }

            //需要新增的菜单
            List<Menu> addMenus = allMenu.stream().filter(x -> menuNos.contains(x.getMenuNo()) && !hasMenuNo.contains(x.getMenuNo())).collect(Collectors.toList());
            for (Menu addMenu : addMenus) {
                addMenu.setUserNo(userNo);
            }
            //添加新选中的
            for (Menu addMenu : addMenus) {
                addMenu.setType(3);
                menuMapper.addCommonyUsed(addMenu);
            }
        }
        return 0;
    }

    @Override
    public List<Menu> findByUser(String userNo) {
        if (StringUtil.isEmpty(userNo)) {
            return null;
        }
        //查询所有系统菜单
        List<Menu> allMenu = menuMapper.findAllMenu();
        //查找用户已有权限的菜单
        List<Menu> menuByUser = menuMapper.findMenuByUser(userNo);

        List<Integer> menuNoByuser = menuByUser.stream().map(Menu::getMenuNo).collect(Collectors.toList());
        //修改选中信息返回给前端
        for (Menu menu : allMenu) {
            if (menuNoByuser.contains(menu.getMenuNo())) {
                menu.setChecked(true);
            } else {
                menu.setChecked(false);
            }
        }
        //把二级菜单单独分开
        List<Menu> firstMenus = allMenu.stream().filter(x -> x.getMenuLevel() == 0).collect(Collectors.toList());
        List<Menu> secondMenus = allMenu.stream().filter(x -> x.getMenuLevel() == 1).collect(Collectors.toList());
        //修改选中信息返回给前端
        for (Menu menu : firstMenus) {
            List<Menu> temp = new ArrayList<>();
            for (Menu secondMenu : secondMenus) {
                if (secondMenu.getMenuParentNo().equals(menu.getMenuNo())) {
                    temp.add(secondMenu);
                }
            }
            menu.setMenuList(temp);
        }
        return firstMenus;
    }


    @Override
    public List<Menu> findReportByUser(String userNo) {
        if (StringUtil.isEmpty(userNo)) {
            return null;
        }
        //查找用户已有权限的菜单
        List<Menu> menuByUser = menuMapper.findReportByUser(userNo);

        return menuByUser;
    }

    @Transactional
    @Override
    public Integer editMenuByUser(List<Menu> menus) {
        if (!CollectionUtils.isEmpty(menus)) {
            String userNo = menus.get(0).getUserNo();
            if (StringUtil.isEmpty(userNo)) {
                return null;
            }

            List<Menu> allMenu = menuMapper.findAllMenu();
            List<Integer> menuNos = menus.stream().map(Menu::getMenuNo).collect(Collectors.toList());
            List<Menu> menuByUser = menuMapper.findMenuByUser(userNo);
            List<Integer> hasMenuNo = menuByUser.stream().map(Menu::getMenuNo).collect(Collectors.toList());

            if (menus.get(0).getMenuNo().equals(-1)) {
                //删除全部常用菜单
                menuMapper.deleteCommyUsedByUser(userNo, hasMenuNo);
            } else {
                //找出需要删除的
                List<Menu> delMenus = allMenu.stream().filter(x -> !menuNos.contains(x.getMenuNo())).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(delMenus)) {
                    List<Integer> delNos = delMenus.stream().map(Menu::getMenuNo).collect(Collectors.toList());
                    menuMapper.deleteCommyUsedByUser(userNo, delNos);
                }
            }

            //需要新增的菜单
            List<Menu> addMenus = allMenu.stream().filter(x -> menuNos.contains(x.getMenuNo()) && !hasMenuNo.contains(x.getMenuNo())).collect(Collectors.toList());
            for (Menu addMenu : addMenus) {
                addMenu.setUserNo(userNo);
            }
            //添加新选中的
            for (Menu addMenu : addMenus) {
                //用户菜单
                addMenu.setType(2);
                menuMapper.addCommonyUsed(addMenu);
            }
        }
        return 0;
    }

    /**
     * 根据菜单设置过滤
     * 过滤掉报表
     *
     * @param menuList
     * @param userNo
     * @return
     */
    private List<Menu> filtMenu(List<Menu> menuList, String userNo) {
        List<Menu> result = new ArrayList<>();
        //权限过滤
        List<Menu> menuByUser = menuMapper.findMenuByUser(userNo);
        List<Integer> menuNoByUser = menuByUser.stream().map(Menu::getMenuNo).collect(Collectors.toList());
        List<Menu> menus = menuList.stream().filter(x -> menuNoByUser.contains(x.getMenuNo())).collect(Collectors.toList());
        //过滤掉报表
        result = menus.stream().filter(x -> !x.getMenuParentNo().equals(17)).collect(Collectors.toList());
        return result;
    }

    @Override
    public Boolean buttonQuery(String url, String loginUser) {
        List<Menu> result = menuMapper.buttonQuery(url, loginUser);
        if (!CollectionUtils.isEmpty(result) && result.size() > 0) {
            return true;
        }
        return false;
    }
}
