<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zqn.prodctrl.mapper.PendingProdMapper">

    <select id="queryCount" resultType="com.zqn.prodctrl.entity.PendingProd">
        SELECT sum(tot_qty) as tot_qty_sum
        FROM vw_gc_sorders_tbs
        WHERE TRUNC(shp_date) BETWEEN TO_DATE(#{startTime}, 'yyyy/MM/dd') AND TO_DATE(#{endTime}, 'yyyy/MM/dd')
        <if test="brand != null and brand != ''">
            AND UPPER(brand_no) = UPPER(#{brand})
        </if>
        <if test="ordNo != null and ordNo != ''">
            AND UPPER(ord_no) = UPPER(#{ordNo})
        </if>
        <if test="devType != null and devType != ''">
            AND dev_type = #{devType}
        </if>

        <if test="phase != null and phase != ''">
            AND phase = #{phase}
        </if>

        <if test="pdLine != null and pdLine != ''">
            AND pd_line = #{pdLine}
        </if>

    </select>

    <!--
    -項次 item_no, 上線日期 online_date,  樣品單號  ord_no, 樣品類型 dev_type,  鞋圖 model_pic ,
    -面師/底師 upper_ser/sole_ser, 業務  dutyer, 型體 model_no,  sku sku_no,  楦頭編號 last_no,
    -楦頭數量  app_qty, 派工日 wo_date ,出貨日 shp_date,  樣品雙數 tot_qty, 型體確認 model_cfm_flag, 形體確認註記 dev_types,
    -模具新舊 mod_old, 模具確認 model_cfm_flag, 庫存註記 kc_flag,  面料交期 pur_u_flag,  底料交期 pur_s_flag,品牌 brand_no ,
    品牌描述 brand_desc
    -->
    <select id="query" resultType="com.zqn.prodctrl.entity.PendingProd">
        SELECT item_no, online_date, ord_no, dev_type, model_pic, upper_ser, sole_ser, dutyer,
        model_no, sku_no, last_no, app_qty, wo_date, shp_date, tot_qty, dev_types,
        mod_old, model_cfm_flag, mold_cfm_flag, kc_flag, pur_u_flag, pur_s_flag,brand_no,brand_desc,
        pd_line,phase
        FROM vw_gc_sorders_tbs
        WHERE TRUNC(shp_date) BETWEEN TO_DATE(#{startTime}, 'yyyy/MM/dd') AND TO_DATE(#{endTime}, 'yyyy/MM/dd')
        <if test="brand != null and brand != ''">
            AND UPPER(brand_no) = UPPER(#{brand})
        </if>
        <if test="ordNo != null and ordNo != ''">
            AND UPPER(ord_no) = UPPER(#{ordNo})
        </if>
        <if test="devType != null and devType != ''">
            AND dev_type = #{devType}
        </if>

        <if test="phase != null and phase != ''">
            AND phase = #{phase}
        </if>

        <if test="pdLine != null and pdLine != ''">
            AND pd_line = #{pdLine}
        </if>

        order by shp_date
    </select>

    <select id="queryAllDevType" resultType="java.lang.String">
        select dev_type
        FROM vw_gc_sorders_tbs
        group by dev_type
    </select>


    <update id="edit">
        UPDATE gc_sorders
        SET pic_flag = 'Y',
            PIC_DATE = SYSDATE
        WHERE ord_no = #{ordNo}
          AND item_no = #{itemNo}
    </update>
</mapper>