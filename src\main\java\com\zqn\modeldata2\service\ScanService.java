package com.zqn.modeldata2.service;

import com.zqn.modeldata2.entity.MkSlastbar;
import com.zqn.modeldata2.entity.MkSlastbarPlus;

import java.util.List;
import java.util.Map;

public interface ScanService {
    Map<String, Object> getCount(String user_id, String ins_user, String cl_flag, boolean today);

    List<MkSlastbarPlus> getScanException(String user_id, String ins_user, String cl_flag, boolean today, int page_no, int page_size);

    Map<String, Object> getBarCodeA(String ord_no);

    Map<String, Object> getBarCodeB(String column_seq);

    Map<String, Object> getBarCodeC(String column_seq);

    Map<String, Object> getBarCodeD(String column_seq);

    List<Object> getOption(MkSlastbar mkSlastbar);

    int addException(MkSlastbar mkSlastbar);

    int updateException(MkSlastbar mkSlastbar);

    int updateMethod(MkSlastbar mkSlastbar);

    int updateState(MkSlastbar mkSlastbar);

    int deleteException(MkSlastbar mkSlastbar);
}
