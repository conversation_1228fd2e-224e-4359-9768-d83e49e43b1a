package com.zqn.modeldata2.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.entity.MaterialMatch;
import com.zqn.modeldata2.entity.MaterialMatchDt;
import com.zqn.modeldata2.mapper.MaterialMatchMapper;
import com.zqn.modeldata2.service.MaterialMatchService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class MaterialMatchServiceImpl implements MaterialMatchService {

    private static final Logger LOG = LoggerFactory.getLogger(MaterialMatchServiceImpl.class);
    @Resource
    private MaterialMatchMapper materialMatchMapper;

    @Override
    public R<PageInfo<MaterialMatch>> query(int pageNo, int pageSize, Date startTime, Date endTime, String brand, String devType, Integer fileType, Integer cutComplType) {
        PageHelper.startPage(pageNo, pageSize);
        List<MaterialMatch> list = materialMatchMapper.query(startTime, endTime, brand, devType, fileType, cutComplType);
        PageInfo<MaterialMatch> pageInfo = new PageInfo<>(list);

        if (pageInfo.getList().size() > 0) {
            for (MaterialMatch materialMatch : pageInfo.getList()) {
                Date t1Flag = materialMatchMapper.queryMaxShDate(materialMatch.getOrd_no());
                materialMatch.setT1_flag(t1Flag);
            }
        }
        return R.success(pageInfo);
    }

    @Override
    public List<MaterialMatchDt> queryDetailTableData(String ordNo) {
        return materialMatchMapper.queryDetailTableData(ordNo);
    }

    @Override
    public List<JSONObject> queryAllDevType() {
        List<JSONObject> result = new ArrayList<JSONObject>();
        List<String> devTypes = materialMatchMapper.queryAllDevType();
        if (devTypes.size() > 0) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("value", "");
            jsonObject.put("text", "請選擇");
            result.add(jsonObject);
        }
        for (String devType : devTypes) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("value", devType);
            jsonObject.put("text", devType);
            result.add(jsonObject);
        }
        return result;
    }
}
