package com.zqn.modeldata2.service;

import com.zqn.modeldata2.entity.CkSmodelp;
import com.zqn.modeldata2.entity.Sign;
import com.zqn.modeldata2.entity.SignFile;

import java.util.List;
import java.util.Map;

public interface ReportService {
    Map<String, Object> getPicture(String model_no);

    Map<String, Object> getInfo(String model_no);

    List<Object> getTitle(String model_no, String siz_type, double bas_size);

    Boolean getFullSize(String model_no);

    Integer updateFullSize(String model_no, Boolean full_size);

    List<Object> getCount1(String model_no, String siz_type, double bas_size, int procs_type);

    List<Object> getCount2(String model_no, String siz_type, double bas_size, int procs_type);

    CkSmodelp getInsertInfo(String model_no);

    Sign getSignature(Sign sign);

    Integer saveChkSignature(Sign sign);

    Integer saveUppSignature(Sign sign);

    Integer saveSolSignature(Sign sign);

    Integer resetChkSignature(SignFile signFile);

    Integer resetUppSignature(SignFile signFile);

    Integer resetSolSignature(SignFile signFile);
}
