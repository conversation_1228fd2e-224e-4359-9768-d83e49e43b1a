package com.zqn.modeldata2.mapper;

import com.zqn.modeldata2.entity.MbptDtDto;
import com.zqn.modeldata2.entity.MbptDto;
import com.zqn.modeldata2.entity.MkSorderbarDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
public interface PendingUpperProdMapper {

    List<MbptDto> query(@Param("startTime") Date startTime,
                        @Param("endTime") Date endTime,
                        @Param("brand") String brand,
                        @Param("devType") String devType,
                        @Param("fileType") Integer fileType, Integer cutComplType,
                        @Param("ordNo") String searchOrdNo, @Param("madeDept") String madeDept,
                        @Param("pdLine") String pdLine,
                        @Param("t2Flag") String t2Flag,
                        @Param("bDate") List<Date> bDate,
                        @Param("grpNo")  String grpNo
                        );

    List<String> queryAllDevType();

    List<MbptDtDto> queryDetailTableData(@Param("userNo") String userNo,
                                         @Param("grpNo") String grpNo);

    void addMadeDept(Map<String, Object> params);

    Integer editMadeDept(@Param("dto") MkSorderbarDto mkSorderbarDto, @Param("barDate") Date date);

    List<Date> queryBarDate(@Param("ordNo") String ordNo);

    List<MkSorderbarDto> queryMkSorderbarDtoByOrdNo(@Param("ordNo") String ordNo);

    void fwtrcc(Map<String, Object> params);

    MbptDto querycount(@Param("startTime") Date startTime,
                       @Param("endTime") Date endTime,
                       @Param("brand") String brand,
                       @Param("devType") String devType,
                       @Param("fileType") Integer fileType, Integer cutComplType,
                       @Param("ordNo") String searchOrdNo, @Param("madeDept") String madeDept,
                       @Param("pdLine") String pdLine,
                       @Param("t2Flag") String t2Flag,
                       @Param("bDate") List<Date> bDate,
                       @Param("grpNo")  String grpNo);

    void cancel(@Param("dto") MkSorderbarDto mkSorderbarDto);
}