<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zqn.modeldata2.mapper.UserMapper">
    <resultMap id="ResultMap" type="com.zqn.modeldata2.entity.UserDto">
        <result property="userId" column="user_id" jdbcType="VARCHAR"/>
        <result property="userDesc" column="user_desc" jdbcType="VARCHAR"/>

    </resultMap>

    <select id="getUser" resultMap="ResultMap">
        select user_id, user_desc
        from sy_user
        where user_id like '%' || #{userNo} || '%'
    </select>
</mapper>