package com.zqn.sop.service;

import com.alibaba.fastjson.JSONObject;
import com.zqn.sop.entity.PccMeProjectPlanDt;
import com.zqn.sop.entity.PccMeProjectPlanImg;
import com.zqn.sop.vo.PccMeProjectPlanDtVo;
import com.zqn.sop.vo.PccMeProjectPlanHdVo;

import java.io.IOException;
import java.util.List;

public interface PccMeProjectPlanDtService {

    int add(PccMeProjectPlanDt pccMeProjectPlanDts, String type) throws Exception;

    List<PccMeProjectPlanDtVo> query(Integer parentId) throws IOException;

    PccMeProjectPlanDtVo queryByItem(String item, Integer version,Integer parentId);

    List<JSONObject> queryAllTools(String content, Integer searchType, Integer dept);

    Integer selectMax(String model, String dept,String factory);

    List<JSONObject> queryAllItem(Integer parentId);

    List<JSONObject> queryAllActions(String index, String type);

    Integer deleteItem(PccMeProjectPlanHdVo vo) throws Exception;

    List<JSONObject> queryActionsByTags(String index, String type, String tag);

    List<PccMeProjectPlanDtVo> queryAllDt(Integer parentId);

    Integer selectMin(String modelNo, String dept, String factory);
}