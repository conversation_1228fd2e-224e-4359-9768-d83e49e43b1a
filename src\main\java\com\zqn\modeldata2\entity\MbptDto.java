package com.zqn.modeldata2.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 面部料板配套
 * @date 2024/7/25 15:24
 */
@Data
public class MbptDto {

    //樣品單號
    private String ord_no;

    //項次
    private String item_no;

    //樣品類型
    private String dev_type;

    //鞋圖
    private byte[] model_pic;

    //製鞋組別
    private String made_dept;

    //姓名
    private String emp_name;

    //生产线
    private String pd_line;

    //業務
    private String dutyer;

    //版师
    private String upper_der;

    //型體
    private String model_no;

    //楦頭編號
    private String last_no;

    //楦头数量
    private BigDecimal sum_app_qty;

    //派工日
    @JsonFormat(pattern = "YY/MM/dd")
    private Date wo_date;

    //出貨日
    @JsonFormat(pattern = "YY/MM/dd")
    private Date shp_date;

    //品牌
    private String brand_no;

    //訂單量
    private String tot_qty;

    //面料狀況
    private String t1_flag;

    //底料狀況
    private String t2_flag;

    //副料配套
    private String t3_flag;

    //發外投入
    private String y1_qty;

    //發外產出
    private String y2_qty;

    //中底皮
    private String b1_qty;

    //中底
    private String b2_qty;

    //包粘
    private String b3_qty;

    //大底
    private String b4_qty;

    //鞋面投入
    private String uin_flag;

    //订单总量
    private BigDecimal tot_qty_sum;

    //投入数量
    private Integer in_qty;

    //产出数量
    private Integer out_qty;

    private String grp_no;

    //副料配套日期
    @JsonFormat(pattern = "YY/MM/dd")
    private Date t3_date;

    // 半成品结案日期
    @JsonFormat(pattern = "YY/MM/dd")
    private Date b_date;

}
