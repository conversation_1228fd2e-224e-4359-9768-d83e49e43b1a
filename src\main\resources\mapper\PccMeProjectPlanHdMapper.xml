<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zqn.sop.mapper.PccMeProjectPlanHdMapper">

    <select id="query" resultType="com.zqn.sop.entity.PccMeProjectPlanHd">
        select id, model as model_no, shoe_last, brand, link_date, remark, create_by, create_date, update_by,
        update_date, dept, shoe_make_head, printmaker, senior_technician,factory,audit_flag
        from PCC_ME_PROJECT_PLAN_HD
        <where>
            <if test="hasAudit != true">
                AND ((brand = 'BA' and create_by = #{loginUser}) or brand != 'BA')
            </if>
            <if test="model != null and model != ''">
                AND model = #{model}
            </if>
            <if test="dept != null and dept != ''">
                AND dept = #{dept}
            </if>
            <if test="brand != null and brand != ''">
                AND brand = #{brand}
            </if>
            <choose>
              <when test="selectFactory == 'ALL' and userFactory !='FS'">
                  AND factory != 'FS'
              </when>
                <when test="selectFactory == 'FS'  and userFactory !='FS' ">
                    AND factory = '无权访问'
                </when>
                <when test="selectFactory != null and selectFactory != '' ">
                    AND factory = #{selectFactory}
                </when>
            </choose>
        </where>
        order by create_date desc
    </select>


    <select id="queryModel" resultType="com.zqn.sop.entity.PccMeProjectPlanHd">
        SELECT model as model_no
        FROM PCC_ME_PROJECT_PLAN_HD
        WHERE UPPER(model) LIKE '%' || UPPER(#{model}) || '%'
    </select>

    <select id="queryShoeLastByModelNo" resultType="java.lang.String">
        select c.last_nos
        from ck_smodel a,
             be_moudel b,
             bf_last c,
             cb_model d
        where a.module_no = b.module_no(+)
          and b.last_seq = c.last_seq(+)
          and a.model_no = d.model_no(+)
          and a.brand_no = #{brand}
          and a.model_no = #{modelNo}
        order by a.model_no
    </select>

    <insert id="save" useGeneratedKeys="true" keyProperty="id" keyColumn="ID">
        insert into PCC_ME_PROJECT_PLAN_HD (MODEL, SHOE_LAST, BRAND, LINK_DATE, REMARK, CREATE_BY, CREATE_DATE,
                                            UPDATE_BY,
                                            UPDATE_DATE, dept, shoe_make_head, printmaker, senior_technician, factory,
                                            audit_flag)
        values (#{planHd.model_no},
                #{planHd.shoe_last},
                #{planHd.brand},
                #{planHd.link_date},
                #{planHd.remark},
                #{planHd.create_by},
                #{planHd.create_date},
                #{planHd.update_by},
                #{planHd.update_date},
                #{planHd.dept},
                #{planHd.shoe_make_head},
                #{planHd.printmaker},
                #{planHd.senior_technician},
                #{planHd.factory},
                0
                )
    </insert>

    <!--删除操作流程-->
    <delete id="deleteContent">
        delete
        from PCC_SOP_CONT
        where PARENT_ID in (select id from PCC_ME_PROJECT_PLAN_DT where parent_id = #{hdId})
    </delete>

    <delete id="deleteDt">
        delete
        from PCC_ME_PROJECT_PLAN_DT
        where parent_id = #{hdId}
    </delete>

    <delete id="deleteImgs">
        delete
        from PCC_ME_PROJECT_PLAN_IMG
        where PARENT_ID in (select id from PCC_ME_PROJECT_PLAN_DT where parent_id = #{hdId})
    </delete>

    <delete id="deleteHd">
        delete
        from PCC_ME_PROJECT_PLAN_HD
        where id = #{hdId}
    </delete>

    <!--    插入明细表-->
    <insert id="copyDt">
        INSERT INTO PCC_ME_PROJECT_PLAN_DT (parent_id, item_no, actions, tools, margin, remark, create_by, create_date,
                                            update_by, update_date, machine, temp, pressure, glue, car_line,
                                            chemical_substance, needle_spacing, spacing, needle, img_tit1, img_tit2,
                                            time, version, protective_gear)
        select #{parentId},
               item_no,
               actions,
               tools,
               margin,
               remark,
               create_by,
               create_date,
               update_by,
               update_date,
               machine,
               temp,
               pressure,
               glue,
               car_line,
               chemical_substance,
               needle_spacing,
               spacing,
               needle,
               img_tit1,
               img_tit2,
               time,
               version,
               protective_gear
        from PCC_ME_PROJECT_PLAN_DT a
        where a.parent_id = #{oldId}
    </insert>

    <!--查询插入后的数据-->
    <select id="queryDt" resultType="com.zqn.sop.vo.PccMeProjectPlanDtVo">
        select *
        from PCC_ME_PROJECT_PLAN_DT
        where parent_id = #{parentId}
    </select>

    <insert id="insertCont">
        insert into PCC_SOP_CONT (parent_id, content, lang, type, action_id)
        select #{id}, content, lang, type, action_id
        from PCC_SOP_CONT
        where PARENT_ID = (select b.id
                           from PCC_ME_PROJECT_PLAN_HD a
                                    left join PCC_ME_PROJECT_PLAN_DT b on a.id = b.PARENT_ID
                                    left join PCC_SOP_CONT c on b.id = c.PARENT_ID
                           where a.MODEL = #{model}
                           and a.dept = #{dept}
                           and a.factory = #{factory}
                           and b.ITEM_NO = #{itemNo}
                           and b.version = #{version}
                           group by b.id)
    </insert>

    <insert id="insertImg">
        insert into PCC_ME_PROJECT_PLAN_IMG (parent_id, img_url, remark, type)
        select #{id}, img_url, remark, type
        from PCC_ME_PROJECT_PLAN_IMG
        where PARENT_ID = (select b.id
                           from PCC_ME_PROJECT_PLAN_HD a
                                    left join PCC_ME_PROJECT_PLAN_DT b on a.id = b.PARENT_ID
                                    left join PCC_ME_PROJECT_PLAN_IMG c on b.id = c.PARENT_ID
                           where a.MODEL = #{model}
                             and a.dept = #{dept}
                             and a.factory = #{factory}
                             and b.ITEM_NO = #{itemNo}
                             and b.version = #{version}
                           group by b.id)
    </insert>



    <select id="queryAllPlanFactory"  resultType="java.lang.String">
        select distinct factory from PCC_ME_PROJECT_PLAN_HD
    </select>

    <select id="queryById" resultType="com.zqn.sop.vo.PccMeProjectPlanHdVo">
        select *
        from PCC_ME_PROJECT_PLAN_HD
        where id = #{id}
    </select>


    <update id="audit">
        update PCC_ME_PROJECT_PLAN_HD
        set audit_flag = 1
        where id = #{id}
          and audit_flag = 0
    </update>

    <update id="revAudit">
        update PCC_ME_PROJECT_PLAN_HD
        set audit_flag = 0
        where id = #{id}
          and audit_flag = 1
    </update>

</mapper>