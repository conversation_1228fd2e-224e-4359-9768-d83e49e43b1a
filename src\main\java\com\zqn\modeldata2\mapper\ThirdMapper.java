package com.zqn.modeldata2.mapper;

import com.zqn.modeldata2.entity.CkSmodelpa;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ThirdMapper {
    List<Object> getOption1();

    List<Object> getOption2();

    List<CkSmodelpa> getPart1(@Param("ckSmodelpa") CkSmodelpa ckSmodelpa);

    List<CkSmodelpa> getPart2(@Param("ckSmodelpa") CkSmodelpa ckSmodelpa);

    int addTemporaryPart(@Param("ckSmodelpa") CkSmodelpa ckSmodelpa);

    int addAllPart(@Param("ckSmodelpaList") List<CkSmodelpa> ckSmodelpaList);

    int updatePart(@Param("ckSmodelpa") CkSmodelpa ckSmodelpa);

    int deletePart(@Param("ckSmodelpa") CkSmodelpa ckSmodelpa);

    int batchDeletePart(@Param("ckSmodelpaList") List<CkSmodelpa> ckSmodelpaList);
}
