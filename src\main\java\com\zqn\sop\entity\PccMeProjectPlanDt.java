package com.zqn.sop.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:ME工程编制明细表
 * @date 2024/5/8 8:36
 */
@Data
public class PccMeProjectPlanDt {

    private Integer id;

    private Integer parent_id;

    //项次
    private Integer item_no;

    //动作
    private String actions;

    //工具
    private String tools;

    //边距
    private String margin;

    private String remark;

    private String create_by;

    @JsonFormat(pattern = "YYYY/MM/dd")
    private Date create_date;

    private String update_by;

    @JsonFormat(pattern = "YYYY-MM-dd HH:mm:ss")
    private Date update_date;

    private List<PccMeProjectPlanImg> imgUrls;

    private List<PccMeProjectPlanVideo> videoUrls;

    //机器
    private String machine;

    //温度
    private String temp;

    //压力
    private String pressure;

    //胶水
    private String glue;

    //车线
    private String car_line;

    //化学品
    private String chemical_substance;

    //时间
    private String time;

    //针距
    private String needle_spacing;

    //间距
    private String spacing;

    //车针
    private String needle;

    //防护用品
    private String protective_gear;

    //图片备注1
    private String img_tit1;

    //图片备注2
    private String img_tit2;

    //版本
    private Integer version;

    //电车转速
    private String speed;

}
