package com.zqn.modeldata2.entity.sop;

import lombok.Data;

import java.sql.Timestamp;

/**
 * 热冷压规格
 */
@Data
public class SopPress {
    // 型体
    private String model_no;
    // 制程
    private String operation;
    // 主要代码
    private String rtg_code;
    // 部件名称
    private String component_name;
    // 厂商
    private String vendor;
    // 流程条件
    private String process_condition;
    // 压机类型
    private int press_type;
    // 上垫片-厚度
    private String top_thickness;
    // 上垫片-硬度
    private String top_hardness;
    // 上垫片-颜色
    private String top_color;
    // 上垫片-更换频率
    private String top_change_frequency;
    // 上垫片-机器设定温度
    private String top_mc_set_temp;
    // 上垫片-实际温度
    private String top_actual_temp;
    // 下垫片-厚度
    private String bottom_thickness;
    // 下垫片-硬度
    private String bottom_hardness;
    // 下垫片-颜色
    private String bottom_color;
    // 下垫片-更换频率
    private String bottom_change_frequency;
    // 下垫片-机器设定温度
    private String bottom_mc_set_temp;
    // 下垫片-实际温度
    private String bottom_actual_temp;
    // 时间
    private String time;
    // 大小1
    private String size1;
    // 大小2
    private String size2;
    // 大小3
    private String size3;
    // 大小4
    private String size4;
    // 大小5
    private String size5;
    // 大小6
    private String size6;
    // 大小7
    private String size7;
    // 机器设定1
    private String mc_setting1;
    // 机器设定2
    private String mc_setting2;
    // 机器设定3
    private String mc_setting3;
    // 机器设定4
    private String mc_setting4;
    // 机器设定5
    private String mc_setting5;
    // 机器设定6
    private String mc_setting6;
    // 机器设定7
    private String mc_setting7;
    // 测压元件1
    private String load_cell1;
    // 测压元件2
    private String load_cell2;
    // 测压元件3
    private String load_cell3;
    // 测压元件4
    private String load_cell4;
    // 测压元件5
    private String load_cell5;
    // 测压元件6
    private String load_cell6;
    // 测压元件7
    private String load_cell7;
    // 探针位置-图片
    private String probe_location_image;
    // 探针位置-备注
    private String probe_location_remark;
    // 建立人
    private String ins_user;
    // 建立日期
    private Timestamp ins_date;
    // 修改人
    private String upd_user;
    // 修改日期
    private Timestamp upd_date;
}
