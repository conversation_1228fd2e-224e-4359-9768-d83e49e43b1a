package com.zqn.modeldata2.controller;

import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.entity.ProcessInfo;
import com.zqn.modeldata2.entity.ProcessStep;
import com.zqn.modeldata2.entity.ProcessTemplate;
import com.zqn.modeldata2.service.ProcessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/process")
public class ProcessController {
    @Autowired
    private ProcessService processService;

    @PostMapping("/getOperation")
    public R<List<Integer>> getOperation() {
        List<Integer> result = processService.getOperation();
        return R.success(result);
    }

    @PostMapping("/getBrand")
    public R<List<Object>> getBrand() {
        List<Object> result = processService.getBrand();
        return R.success(result);
    }

    @PostMapping("/getModel")
    public R<List<String>> getModel(@RequestBody Map<String, Object> params) {
        String brand_no = params.get("brand_no") != null ? params.get("brand_no").toString() : "";
        List<String> result = processService.getModel(brand_no);
        return R.success(result);
    }

    @PostMapping("/getLast")
    public R<Map<String, Object>> getLast(@RequestBody Map<String, Object> params) {
        String model_no = params.get("model_no") != null ? params.get("model_no").toString() : "";
        Map<String, Object> result = processService.getLast(model_no);
        return R.success(result);
    }

    @PostMapping("/getCode")
    public R<String> getCode(@RequestBody Map<String, Object> params) {
        String model_no = params.get("model_no") != null ? params.get("model_no").toString() : "";
        String operation = params.get("operation") != null ? params.get("operation").toString() : "";
        String result = processService.getCode(model_no, operation);
        return R.success(result);
    }

    @PostMapping("/getPicture")
    public R<Map<String, Object>> getPicture(@RequestBody Map<String, Object> params) {
        String model_no = params.get("model_no") != null ? params.get("model_no").toString() : "";
        Map<String, Object> result = processService.getPicture(model_no);
        return R.success(result);
    }

    @PostMapping("/getType")
    public R<List<String>> getType(@RequestBody Map<String, String> params) {
        String operation = params.get("operation");
        List<String> result = processService.getType(operation);
        return R.success(result);
    }

    @PostMapping("/getMaterial")
    public R<List<String>> getMaterial() {
        List<String> result = processService.getMaterial();
        return R.success(result);
    }

    @PostMapping("/getProcessInfo")
    public R<List<ProcessInfo>> getProcessInfo(@RequestBody ProcessInfo processInfo) {
        List<ProcessInfo> result = processService.getProcessInfo(processInfo);
        return R.success(result);
    }

    @PostMapping("/addProcessInfo")
    public R<String> addProcessInfo(@RequestBody ProcessInfo processInfo) {
        Integer result = processService.addProcessInfo(processInfo);
        if (result < 0) {
            return R.error("工序信息添加失败！");
        }
        return R.success("工序信息添加成功！");
    }

    @PostMapping("/updateProcessInfo")
    public R<String> updateProcessInfo(@RequestBody ProcessInfo processInfo) {
        Integer result = processService.updateProcessInfo(processInfo);
        if (result < 0) {
            return R.error("工序信息修改失败！");
        }
        return R.success("工序信息修改成功！");
    }

    @PostMapping("/deleteProcessInfo")
    public R<String> deleteProcessInfo(@RequestBody ProcessInfo processInfo) {
        Integer result = processService.deleteProcessInfo(processInfo);
        if (result < 0) {
            return R.error("工序信息删除失败！");
        }
        return R.success("工序信息删除成功！");
    }

    @PostMapping("/batchDeleteProcessInfo")
    public R<String> batchDeleteProcessInfo(@RequestBody Map<String, List<ProcessInfo>> params) {
        List<ProcessInfo> processInfoList = params.get("processInfoList") != null ? params.get("processInfoList") : new ArrayList<>();
        Integer result = processService.batchDeleteProcessInfo(processInfoList);
        if (result < 0) {
            return R.error("工序信息批量删除失败！");
        }
        return R.success("工序信息批量删除成功！");
    }

    @PostMapping("/getNo")
    public R<String> getNo(@RequestBody ProcessStep processStep) {
        String result = processService.getNo(processStep);
        return R.success(result);
    }

    @PostMapping("/getKey")
    public R<String> getKey(@RequestBody ProcessStep processStep) {
        String result = processService.getKey(processStep);
        return R.success(result);
    }

    @PostMapping("/getTemplate")
    public R<List<ProcessTemplate>> getTemplate(@RequestBody Map<String, Object> params) {
        String operation = params.get("operation") != null ? params.get("operation").toString() : "";
        List<ProcessTemplate> result = processService.getTemplate(operation);
        return R.success(result);
    }

    @PostMapping("/templateImport")
    public R<String> templateImport(@RequestBody ProcessTemplate processTemplate) {
        Integer result = processService.templateImport(processTemplate);
        if (result < 0) {
            return R.error("模板导入失败!");
        }
        return R.success("模板导入成功!");
    }

    @PostMapping("/getProcessStep")
    public R<List<ProcessStep>> getProcessStep(@RequestBody ProcessStep processStep) {
        List<ProcessStep> result = processService.getProcessStep(processStep);
        return R.success(result);
    }

    @PostMapping("/addProcessStep")
    public R<String> addProcessStep(@RequestBody ProcessStep processStep) {
        Integer result = processService.addProcessStep(processStep);
        if (result < 0) {
            return R.error("工序步骤添加失败！");
        }
        return R.success("工序步骤添加成功！");
    }

    @PostMapping("/updateProcessStep")
    public R<String> updateProcessStep(@RequestBody ProcessStep processStep) {
        Integer result = processService.updateProcessStep(processStep);
        if (result < 0) {
            return R.error("工序步骤修改失败！");
        }
        return R.success("工序步骤修改成功！");
    }

    @PostMapping("/deleteProcessStep")
    public R<String> deleteProcessStep(@RequestBody ProcessStep processStep) {
        Integer result = processService.deleteProcessStep(processStep);
        if (result < 0) {
            return R.error("工序步骤删除失败！");
        }
        return R.success("工序步骤删除成功！");
    }

    @PostMapping("/batchDeleteProcessStep")
    public R<String> batchDeleteProcessStep(@RequestBody Map<String, List<ProcessStep>> params) {
        List<ProcessStep> processStepList = params.get("processStepList") != null ? params.get("processStepList") : new ArrayList<>();
        Integer result = processService.batchDeleteProcessStep(processStepList);
        if (result < 0) {
            return R.error("工序步骤批量删除失败！");
        }
        return R.success("工序步骤批量删除成功！");
    }

    @PostMapping("/editProcessStep")
    public R<String> editProcessStep(@RequestBody Map<String, List<ProcessStep>> params) {
        List<ProcessStep> processStepList = params.get("processStepList") != null ? params.get("processStepList") : new ArrayList<>();
        Integer result = processService.editProcessStep(processStepList);
        if (result < 0) {
            return R.error("工序步骤编辑失败！");
        }
        return R.success("工序步骤编辑成功！");
    }

    @PostMapping("/getModelList")
    public R<List<String>> getModelList() {
        List<String> result = processService.getModelList();
        return R.success(result);
    }

    @PostMapping("/copyProcess")
    public R<String> copyProcess(@RequestBody Map<String, Object> param) {
        String originalModel = param.get("originalModel") != null ? param.get("originalModel").toString() : "";
        String targetModel = param.get("targetModel") != null ? param.get("targetModel").toString() : "";
        List<String> operationList = param.get("operationList") != null ? (List<String>) param.get("operationList") : new ArrayList<>();
        String user = param.get("user") != null ? param.get("user").toString() : "";
        Integer result = processService.copyProcess(originalModel, targetModel, operationList, user);
        if (result < 0) {
            return R.error("工序数据复制失败！");
        }
        return R.success("工序数据复制成功！");
    }
}