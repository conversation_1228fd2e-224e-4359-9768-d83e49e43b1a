package com.zqn.modeldata2.controller.export;

import cn.afterturn.easypoi.cache.ExcelCache;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import com.zqn.modeldata2.entity.sop.SopFlowPicture;
import com.zqn.modeldata2.entity.sop.SopPreview;
import com.zqn.modeldata2.service.SopService;
import com.zqn.sop.util.ExcelExportUtil;
import org.apache.poi.ss.usermodel.PrintSetup;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFDrawing;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

@Controller
@RequestMapping("/sop/export")
public class SopExportController {
    @Autowired
    private SopService sopService;

    @Value("${uploadUrl}")
    private String TARGET_FOLDER;

    public static byte[] compressImage(String inputImagePath) {
        try {
            // 读取图像
            BufferedImage bufferImg = ImageIO.read(new File(inputImagePath));

            // 获取图片格式
            String formatName = inputImagePath.substring(inputImagePath.lastIndexOf(".") + 1);
            if (formatName.equals("jfif")) {
                formatName = "jpeg";
            }

            // 获取图片写入器
            Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName(formatName);
            if (!writers.hasNext()) throw new IllegalStateException("No writers found");
            ImageWriter writer = writers.next();

            // 设置输出流
            ByteArrayOutputStream byteArrayOut = new ByteArrayOutputStream();
            ImageOutputStream ios = ImageIO.createImageOutputStream(byteArrayOut);
            writer.setOutput(ios);

            // 设置压缩参数
            ImageWriteParam param = writer.getDefaultWriteParam();
            if (param.canWriteCompressed()) {
                param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
                param.setCompressionQuality(0.2f); // 这里设置压缩质量
            }

            // 写入图像
            writer.write(null, new IIOImage(bufferImg, null, null), param);

            // 关闭流
            ios.close();
            writer.dispose();

            return byteArrayOut.toByteArray();

        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    @GetMapping("/excel")
    public void exportOperationProcess(
            @RequestParam(value = "brand") String brand,
            @RequestParam(value = "model") String model,
            @RequestParam(value = "operation") String operation,
            @RequestParam(value = "rtgCode") String rtgCode,
            HttpServletResponse response
    ) throws IOException {
        Map<String, String> operationMap = new HashMap<>();
        operationMap.put("1", "加工");
        operationMap.put("4", "鞋面");
        operationMap.put("5", "半成品");
        operationMap.put("6", "成型");

        // 模板路径
        String filePath = "static/excel/operation_process_ygb0.xlsx";
        if ("4".equals(operation)) {
            filePath = "static/excel/operation_process_mb_ygb0.xlsx";
        }

        Workbook tempWorkbook = ExcelCache.getWorkbook(filePath, new Integer[]{0, 1}, false);
        List<SopPreview> previewList = sopService.getPreviewList(model, operation, rtgCode);
        byte[] modelImg = sopService.getModelPicture(model).getModelPicture();

        Map<Integer, List<Map<String, Object>>> resultMap = new TreeMap<>();
        List<String> sheetList = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");

        List<Map<String, Object>> list0 = new ArrayList<>();
        for (SopPreview sopPreview : previewList) {
            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("skey", sopPreview.getSkey());
            String seqName = sopPreview.getSeqName() != null ? sopPreview.getSeqName() : "";
            hashMap.put("seq_name", seqName.replaceAll("\n", " "));
            hashMap.put("empty", "");
            list0.add(hashMap);
        }
        Map<String, Object> map0 = new TreeMap<>();
        map0.put("model", model);
        map0.put("list", list0);
        List<Map<String, Object>> mapList0 = new ArrayList<>();
        mapList0.add(map0);
        resultMap.put(0, mapList0);
        sheetList.add("IE測試記錄表");

        List<Map<String, Object>> mapList = new ArrayList<>();
        for (int sheetIndex = previewList.size() - 1; sheetIndex >= 0; sheetIndex--) {
            SopPreview sopPreview = previewList.get(sheetIndex);
            Map<String, Object> map = new TreeMap<>();
            map.put("brand", "客户 Brand：" + brand);
            map.put("style", "Style：" + model + "/" + sopPreview.getLast());
            map.put("title", operationMap.get(operation) + "作业流程 Operation process");
            map.put("item", sopPreview.getSkey());
            map.put("actions", sopPreview.getActions());
            map.put("op_std", sopPreview.getStandard());
            map.put("tools", sopPreview.getTools());
            map.put("chemical_substance", sopPreview.getChemical());
            map.put("machine", sopPreview.getMachine());
            map.put("protective_gear", sopPreview.getDefence());
            map.put("temp", sopPreview.getTemp());
            map.put("pressure", sopPreview.getPressure());
            map.put("time", sopPreview.getTime());
            map.put("needle", sopPreview.getNeedle());
            map.put("self_check_points", sopPreview.getCheckPoint());
            map.put("createBy", "制表 Tab：" + sopPreview.getTab());
            map.put("glue", sopPreview.getGlue());
            map.put("thread", sopPreview.getCarLine());
            map.put("margin", sopPreview.getMargin());
            map.put("stitch", sopPreview.getNeedleSpacing());
            map.put("space", sopPreview.getSpacing());
            map.put("empty", "");
            map.put("process_option1", sopPreview.getProcessOption1());
            map.put("process_option2", sopPreview.getProcessOption2());
            map.put("process_option3", sopPreview.getProcessOption3());
            map.put("process_option4", sopPreview.getProcessOption4());
            map.put("process_option5", sopPreview.getProcessOption5());
            map.put("process_option6", sopPreview.getProcessOption6());
            map.put("process_option7", sopPreview.getProcessOption7());
            map.put("process_option8", sopPreview.getProcessOption8());
            map.put("process_option9", sopPreview.getProcessOption9());
            map.put("process_option10", sopPreview.getProcessOption10());

            // Format the date
            Date updateDate = sopPreview.getUpdDate();
            String formattedDate = dateFormat.format(updateDate);
            map.put("createDate", "日期 Date:" + formattedDate);
            mapList.add(map);
            sheetList.add("Sheet" + (previewList.size() - sheetIndex));
        }
        resultMap.put(1, mapList);

        TemplateExportParams params = new TemplateExportParams(filePath, true);
        params.setTemplateWb(tempWorkbook);
        XSSFWorkbook workbook = (XSSFWorkbook) ExcelExportUtil.exportExcelClone(resultMap, params);

        // Set sheet names after all sheets are created
        for (int i = 0; i < sheetList.size(); i++) {
            workbook.setSheetName(i, sheetList.get(i));
        }

        // Ensure the page settings are copied
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            copyPrintSetup(tempWorkbook.getSheetAt(0), workbook.getSheetAt(i));
        }

        if (workbook != null) {
            // 判断是否根据内容自适应行高
            //暂时取消，多sheet导出高度不会失效
//            setRowHeight(workbook);
        }

        // 插入图片
        for (int imageIndex = 0; imageIndex < previewList.size(); imageIndex++) {
            SopPreview sopPreview = previewList.get(imageIndex);
            List<SopFlowPicture> assetsPicture = sopPreview.getImgList();
            // 获取标签页
            XSSFSheet sheet = (XSSFSheet) workbook.getSheet("Sheet" + (imageIndex + 1));
            XSSFDrawing patriarch = sheet.createDrawingPatriarch();
            if (!CollectionUtils.isEmpty(assetsPicture)) {
                List<byte[]> type1Images = new ArrayList<>();

                //读取本地文件流并压缩
                for (SopFlowPicture sopFlowPicture : assetsPicture) {
                    String picturePath = TARGET_FOLDER + sopFlowPicture.getImgUrl();
                    byte[] compressedImage = compressImage(picturePath);
                    type1Images.add(compressedImage);
                }
                insertImagesInGrid(patriarch, workbook, type1Images, 6, 1, 8, 6);
            }

            //插入型体图片
            if (modelImg != null) {
                // 创建锚点
                XSSFClientAnchor anchor = new XSSFClientAnchor(0, 0, 0, 0, (short) 1, 0, (short) 2, 1);
                patriarch.createPicture(anchor, workbook.addPicture(modelImg, XSSFWorkbook.PICTURE_TYPE_PNG));
            }
        }

        String fileName = model + operationMap.get(operation) + rtgCode + ".xlsx";

        response.reset();
        response.setContentType("application/octet-stream;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
        response.setHeader("Access-Control-Allow-Origin", "*"); // 或者指定具体的域名
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With");
        try {
            workbook.write(response.getOutputStream());
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            workbook.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void insertImagesInGrid(XSSFDrawing patriarch, XSSFWorkbook workbook, List<byte[]> imageBytesList, int startRow, int startCol, int endRow, int endCol) {
        int numRows = 2; // 网格行数
        int numCols = 5; // 网格列数
        int rowHeight = (endRow - startRow) / numRows;
        int colWidth = (endCol - startCol) / numCols;

        for (int i = 0; i < numRows; i++) {
            for (int j = 0; j < numCols; j++) {
                int index = i * numCols + j;
                if (index >= imageBytesList.size()) {
                    return; // 如果图片数量少于网格单元格数量，提前返回
                }
                byte[] imageBytes = imageBytesList.get(index);
                int row1 = startRow + i * rowHeight;
                int col1 = startCol + j * colWidth;
                int row2 = row1 + rowHeight;
                int col2 = col1 + colWidth;

                // 创建锚点
                XSSFClientAnchor anchor = new XSSFClientAnchor(0, 0, 0, 0, (short) col1, row1, (short) col2, row2);
                patriarch.createPicture(anchor, workbook.addPicture(imageBytes, XSSFWorkbook.PICTURE_TYPE_PNG));
            }
        }
    }

    // Copy print setup settings from source sheet to target sheet
    private void copyPrintSetup(Sheet sourceSheet, Sheet targetSheet) {
        PrintSetup sourcePrintSetup = sourceSheet.getPrintSetup();
        PrintSetup targetPrintSetup = targetSheet.getPrintSetup();

        targetPrintSetup.setPaperSize(sourcePrintSetup.getPaperSize());
        targetPrintSetup.setScale(sourcePrintSetup.getScale());
        targetPrintSetup.setPageStart(sourcePrintSetup.getPageStart());
        targetPrintSetup.setFitWidth(sourcePrintSetup.getFitWidth());
        targetPrintSetup.setFitHeight(sourcePrintSetup.getFitHeight());
        targetPrintSetup.setHeaderMargin(sourcePrintSetup.getHeaderMargin());
        targetPrintSetup.setFooterMargin(sourcePrintSetup.getFooterMargin());
        targetPrintSetup.setLandscape(sourcePrintSetup.getLandscape());

        targetSheet.setMargin(Sheet.TopMargin, sourceSheet.getMargin(Sheet.TopMargin));
        targetSheet.setMargin(Sheet.BottomMargin, sourceSheet.getMargin(Sheet.BottomMargin));
        targetSheet.setMargin(Sheet.LeftMargin, sourceSheet.getMargin(Sheet.LeftMargin));
        targetSheet.setMargin(Sheet.RightMargin, sourceSheet.getMargin(Sheet.RightMargin));
        targetSheet.setMargin(Sheet.HeaderMargin, sourceSheet.getMargin(Sheet.HeaderMargin));
        targetSheet.setMargin(Sheet.FooterMargin, sourceSheet.getMargin(Sheet.FooterMargin));

        // Copy any other necessary settings
    }

    /**
     * 一对多，设置行高
     */
    private static void setRowHeight(Workbook workbook) {
        Sheet sheet = workbook.getSheetAt(0);
        //设置第4列的列宽为60（下标从0开始），TestExportSub2Vo 不知道为什么设置了列宽但是不起作用，只能在这里单独设置
//        sheet.setColumnWidth(3,60*256);
        for (int i = 0; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            int j = i / 9;
            if (i == 3 + (j * 9) || i == 5 + (j * 9)) {
                //设置其他行的行高根据内容自适应
                row.setHeightInPoints(70);
            } else if (i == 5 + (j * 9) || i == 7 + (j * 9)) {
                //设置其他行的行高根据内容自适应
                row.setHeightInPoints(70);
            } else {
                //设置第二行的行高（表格表头）
                row.setHeightInPoints(50);
            }
        }
    }

    private static void setRowHeight(Row row) {
        //根据内容长度设置行高
        int enterCnt = 0;
        for (int j = 0; j < row.getPhysicalNumberOfCells(); j++) {
            if (j < 7) {
                continue;
            }
            int rwsTemp = row.getCell(j).toString().length();
            //这里取每一行中的每一列字符长度最大的那一列的字符
            if (rwsTemp > enterCnt) {
                enterCnt = rwsTemp;
            }
        }
        //设置默认行高为50
        row.setHeightInPoints(50);
        //如果字符长度大于20，判断大了多少倍，根据倍数来设置相应的行高
        if (enterCnt > 15) {
            float d = (float) enterCnt / 15;
            if (d > 1.0f) {
                d = d * 1.0f;
            }
            float f = 50 * d;
            /*if (d>2 && d<4){
                f = 35*2;
            }else if(d>=4 && d<6){
                f = 35*3;
            }else if (d>=6 && d<8){
                f = 35*4;
            }*/
            row.setHeightInPoints(f);
        }
    }
}
