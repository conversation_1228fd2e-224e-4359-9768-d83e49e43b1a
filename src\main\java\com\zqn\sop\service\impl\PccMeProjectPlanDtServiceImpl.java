package com.zqn.sop.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.zqn.sop.entity.*;
import com.zqn.sop.mapper.PccMeProjectPlanDtMapper;
import com.zqn.sop.mapper.PccMeProjectPlanHdMapper;
import com.zqn.sop.service.PccMeProjectPlanDtService;
import com.zqn.sop.service.PccSopContService;
import com.zqn.sop.vo.PccMeProjectPlanDtVo;
import com.zqn.sop.vo.PccMeProjectPlanHdVo;
import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
@DS("app")
public class PccMeProjectPlanDtServiceImpl implements PccMeProjectPlanDtService {

    private static final Logger logger = LoggerFactory.getLogger(PccMeProjectPlanDtServiceImpl.class);

    @Resource
    private PccMeProjectPlanDtMapper dtMapper;

    @Resource
    private PccSopContService pccSopContService;

    @Resource
    private PccMeProjectPlanHdMapper hdMapper;

    @Override
    public List<PccMeProjectPlanDtVo> query(Integer parentId) throws IOException {
        List<PccMeProjectPlanDtVo> result = dtMapper.query(parentId);
        for (PccMeProjectPlanDtVo vo : result) {
            dealDtData(vo);
        }
        return result;
    }

    private static void dealCont(List<PccSopCont> pccSopConts, PccMeProjectPlanDtVo vo) {
        for (PccSopCont pccSopCont : pccSopConts) {
            String content = pccSopCont.getContent();
            String lang = pccSopCont.getLang();

            if (StrUtil.equals(lang, "zh-Hans")) {
                if (pccSopCont.getType() == 1) {
                    vo.setOp_std(content);
                } else {
                    vo.setSelf_check_points(content);
                }
            }
            if (StrUtil.equals(lang, "en")) {
                if (pccSopCont.getType() == 1) {
                    vo.setOp_std_en(content);
                } else {
                    vo.setSelf_check_points_en(content);
                }
            }
            if (StrUtil.equals(lang, "vi")) {
                if (pccSopCont.getType() == 1) {
                    vo.setOp_std_vn(content);
                } else {
                    vo.setSelf_check_points_vn(content);
                }
            }
            if (StrUtil.equals(lang, "id")) {
                if (pccSopCont.getType() == 1) {
                    vo.setOp_std_id(content);
                } else {
                    vo.setSelf_check_points_id(content);
                }
            }
            if (StrUtil.equals(lang, "bn")) {
                if (pccSopCont.getType() == 1) {
                    vo.setOp_std_bd(content);
                } else {
                    vo.setSelf_check_points_bd(content);
                }
            }
            if (StrUtil.equals(lang, "fil")) {
                if (pccSopCont.getType() == 1) {
                    vo.setOp_std_ph(content);
                } else {
                    vo.setSelf_check_points_ph(content);
                }
            }
        }
    }

    /**
     * 根据模型编号、项目、版本和部门查询项目计划详情。
     *
     * @param modelNo 模型编号，用于查询特定模型的项目计划。
     * @param item    项目代码，用于查询特定项目的计划。
     * @param version 版本号，用于查询特定版本的计划。
     * @param dept    部门代码，用于查询特定部门的计划。
     * @return 返回查询到的项目计划详情对象，包含相关的操作和数据。
     */
    @Override
    public PccMeProjectPlanDtVo queryByItem( String item, Integer version,Integer parentId) {
        // 根据参数查询项目计划详情
        PccMeProjectPlanDtVo vo = dtMapper.queryByItem(item, version,parentId);
        // 根据项目计划ID查询相关的操作步骤内容
        List<PccSopCont> pccSopConts = pccSopContService.query(vo.getId());
        // 处理操作步骤内容，并将其添加到项目计划详情中
        dealCont(pccSopConts, vo);
        // 处理项目计划的详细数据
        dealDtData(vo);
        // 查询所有项目项，用于填充项目计划详情中的项目项列表
        List<JSONObject> items = queryAllItem(parentId);
        vo.setItems(items);
        // 查询所有版本，用于填充项目计划详情中的版本列表
        List<JSONObject> versions = queryAllVersions(item, parentId);
        vo.setVersions(versions);
        // 返回处理后的项目计划详情
        return vo;
    }

    @Override
    public List<PccMeProjectPlanDtVo> queryAllDt(Integer parentId) {
        List<PccMeProjectPlanDtVo> dtList = dtMapper.queryAllDt(parentId);
        for (PccMeProjectPlanDtVo vo : dtList) {
            // 根据项目计划ID查询相关的操作步骤内容
            List<PccSopCont> pccSopConts = pccSopContService.query(vo.getId());
            // 处理操作步骤内容，并将其添加到项目计划详情中
            dealCont(pccSopConts, vo);
            // 处理项目计划的详细数据
            dealDtData(vo);
        }
        return dtList;
    }

    @Override
    public Integer selectMax(String model, String dept,String factory) {
        return dtMapper.selectMax(model,dept,factory);
    }

    @Override
    public Integer selectMin(String modelNo, String dept, String factory) {
        return dtMapper.selectMin(modelNo, dept, factory);
    }

    /**
     * 查询所有工具的方法
     * 通过调用dtMapper的queryAllTools方法，获取工具信息列表。将这些信息转换为JSONObject的格式，
     * 并返回包含转换后的JSONObject的列表。
     *
     * @param content    查询内容，用于过滤工具信息
     * @param searchType 查询类型，用于指定查询的条件
     * @param dept
     * @return 包含工具信息的JSONObject列表，每个JSONObject包含"value"和"text"两个字段，
     * 分别表示工具的序列号和工具名称。
     */
    @Override
    public List<JSONObject> queryAllTools(String content, Integer searchType, Integer dept) {
        // 初始化结果列表，用于存储转换后的工具信息
        List<JSONObject> result = new ArrayList<>();
        // 调用dtMapper的queryAllTools方法，获取原始工具信息列表
        List<String> list = dtMapper.queryAllTools(content, searchType, dept);
        // 初始化计数器，用于为每个工具分配序列号
        int i = 0;
        // 遍历原始工具信息列表
        for (String str : list) {
            // 计数器自增
            i++;
            // 创建一个新的JSONObject，用于存储当前工具的信息
            JSONObject jsonObject = new JSONObject();
            // 将序列号添加到JSONObject中
            jsonObject.put("value", i);
            // 将工具名称添加到JSONObject中
            jsonObject.put("text", str);
            // 将当前工具的JSONObject添加到结果列表中
            result.add(jsonObject);
        }
        // 返回转换后的工具信息列表
        return result;
    }


    @Override
    public List<JSONObject> queryAllActions(String index, String type) {
        List<JSONObject> result = new ArrayList<>();
        //
        List<PccMeProjectPlanActions> list = dtMapper.queryAllActions(index, type);
        int i = 0;
        for (PccMeProjectPlanActions pccMeProjectPlanActions : list) {
            i++;
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("value", pccMeProjectPlanActions.getId());
            jsonObject.put("text", pccMeProjectPlanActions.getAction_cn());
            result.add(jsonObject);
        }
        return result;
    }

    @Override
    public List<JSONObject> queryActionsByTags(String index, String type, String tag) {
        List<JSONObject> result = new ArrayList<>();
        //
        List<PccMeProjectPlanActions> list = dtMapper.queryActionsByTags(index, type, tag);
        int i = 0;
        for (PccMeProjectPlanActions pccMeProjectPlanActions : list) {
            i++;
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("value", pccMeProjectPlanActions.getId());
            jsonObject.put("text", pccMeProjectPlanActions.getAction_cn());
            result.add(jsonObject);
        }
        return result;
    }

    @Override
    public List<JSONObject> queryAllItem(Integer parentId) {
        List<JSONObject> result = new ArrayList<JSONObject>();
        List<Integer> integers = dtMapper.queryAllItem(parentId);
        for (Integer integer : integers) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("value", integer);
            jsonObject.put("text", integer);
            result.add(jsonObject);
        }
        return result;
    }

    public List<JSONObject> queryAllVersions( String itemNo,Integer parentId) {
        List<JSONObject> result = new ArrayList<JSONObject>();
        List<Integer> integers = dtMapper.queryAllVersion(itemNo,parentId);
        for (Integer integer : integers) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("value", integer);
            jsonObject.put("text", integer);
            result.add(jsonObject);
        }
        return result;
    }

    private void dealDtData(PccMeProjectPlanDtVo vo) {
        List<PccMeProjectPlanImg> imgs = dtMapper.queryImg(vo.getId());
        for (PccMeProjectPlanImg planImg : imgs) {
            String imgUrl = planImg.getImg_url();
            String fileDownloadUri = ServletUriComponentsBuilder.fromCurrentContextPath()
                    .path("/api/files/get/")
                    .queryParam("url", imgUrl)
                    .toUriString();
            planImg.setRemark(imgUrl);

            planImg.setImg_url(fileDownloadUri);
        }
        vo.setImgUrls(imgs);
        List<PccMeProjectPlanVideo> videos = dtMapper.queryVideo(vo.getId());
        vo.setVideoUrls(videos);
    }

    /**
     * 添加项目计划详细信息。
     *
     * @param pccMeProjectPlanDt 项目计划详细信息对象
     * @param modelNo 型体编号
     * @param dept 部门
     * @param type 类型，用于区分新增还是更新
     * @return 添加的记录数
     * @throws Exception 如果操作失败抛出异常
     */
    @Transactional
    @Override
    public int add(PccMeProjectPlanDt pccMeProjectPlanDt, String type) throws Exception {
        // 根据模型编号、项次和部门查询已存在的项目计划详细信息
        PccMeProjectPlanDtVo vo = dtMapper.queryByItem( pccMeProjectPlanDt.getItem_no().toString(), null,pccMeProjectPlanDt.getParent_id());

        // 如果已存在该项目计划详细信息
        //获取版本号
        if (vo != null) {
            // 根据类型确定是新增还是更新
            //已经存在该项次，区分插入还是更新，新增则是插入项次，所有项次+1。更新则版本+1
            if (StrUtil.equals("add", type)) {
                // 新增情况下，设置版本号为1，并处理项次自增逻辑
                pccMeProjectPlanDt.setVersion(1);
                //获取所有需要更新的数据
                //当前项次已存在，还继续新增该项次，所有原项次及之后的项次+1
                // 临时增加偏移量
                dtMapper.incrementItemWithOffset(pccMeProjectPlanDt.getItem_no(),pccMeProjectPlanDt.getParent_id());
                // 最终更新
                dtMapper.finalizeItemUpdate(pccMeProjectPlanDt.getItem_no(),pccMeProjectPlanDt.getParent_id());
            } else {
                // 更新情况下，版本号加1
                pccMeProjectPlanDt.setVersion(vo.getVersion() + 1);
            }
        } else {
            // 不存在该项目计划详细信息，直接设置版本号为1
            //没有这个项次直接保存
            pccMeProjectPlanDt.setVersion(1);
        }

        // 设置项次编号、备注、创建日期和更新日期
        //插入明细表
        Date date = new Date();
        pccMeProjectPlanDt.setItem_no(pccMeProjectPlanDt.getItem_no());
        pccMeProjectPlanDt.setRemark("");
        pccMeProjectPlanDt.setCreate_date(date);
        pccMeProjectPlanDt.setUpdate_date(date);
        // 添加项目计划详细信息
        int addCount = dtMapper.add(pccMeProjectPlanDt);

        // 如果存在图片信息
        //图片
        if (!CollectionUtils.isEmpty(pccMeProjectPlanDt.getImgUrls())) {
            // 遍历图片信息列表，添加图片信息
            for (PccMeProjectPlanImg planImg : pccMeProjectPlanDt.getImgUrls()) {
                int img = dtMapper.insertImg(pccMeProjectPlanDt.getId(), planImg.getImg_url(), "", planImg.getType());
                addCount += img;
            }
        }
        //如果有视频
        //视频
        if (!CollectionUtils.isEmpty(pccMeProjectPlanDt.getVideoUrls())) {
            // 遍历视频信息列表，添加视频信息
            for (PccMeProjectPlanVideo planVideo : pccMeProjectPlanDt.getVideoUrls()) {
                int video = dtMapper.insertVideo(pccMeProjectPlanDt.getId(), planVideo.getUrl(), planVideo.getName());
                addCount += video;
            }
        }
        // 返回添加的记录数
        return addCount;
    }

    /**
     * 根据提供的项目计划头部信息和详细信息删除特定项目计划条目。
     * 此方法首先验证输入参数的有效性，然后调用数据访问对象（DAO）删除指定的项目计划条目。
     * 如果输入参数无效，将抛出异常。
     *
     * @param vo 删除项目计划条目所需的信息，包括项目计划头部和详细信息。
     * @return 被删除的条目数量。
     * @throws Exception 如果输入参数无效或删除操作失败，将抛出异常。
     */
    @Override
    public Integer deleteItem(PccMeProjectPlanHdVo vo) throws Exception {
        // 验证输入参数是否为空
        if (vo == null) {
            throw new Exception("删除失败！");
        }
        // 验证项目编号是否为空
        if (StrUtil.isEmpty(vo.getModel_no())) {
            throw new Exception("删除失败！");
        }
        // 验证项目计划详细信息中的物品编号是否存在
        if (vo.getPccMeProjectPlanDts().getItem_no() == null) {
            throw new Exception("删除失败！");
        }
        PccMeProjectPlanHdVo pccMeProjectPlanHdVo = hdMapper.queryById(vo.getId());
        if (pccMeProjectPlanHdVo != null && pccMeProjectPlanHdVo.getAudit_flag() == 1) {
            throw new Exception("删除失败，数据已审核！");
        }
        // 调用DAO删除指定的项目计划条目，并返回删除的条目数量
        Integer result = dtMapper.deleteItem(vo.getId(), vo.getPccMeProjectPlanDts().getItem_no());
        Integer deleteContent = dtMapper.deleteContent(vo.getId(), vo.getPccMeProjectPlanDts().getItem_no());
        Integer deleteImg = dtMapper.deleteImg(vo.getId(), vo.getPccMeProjectPlanDts().getItem_no());
        // 临时增加偏移量
        dtMapper.deleteItemWithOffset( vo.getPccMeProjectPlanDts().getItem_no(), vo.getId());
        // 最终更新
        dtMapper.deleteItemUpdate(vo.getPccMeProjectPlanDts().getItem_no(), vo.getId());
        return result;
    }
}
