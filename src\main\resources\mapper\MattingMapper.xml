<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zqn.modeldata2.mapper.MattingMapper">

    <update id="callPdMkLastcomp">
        {
            call pd_mk_lastcomp(
                TO_DATE(#{startDate, jdbcType=VARCHAR}, 'YYYY/MM/DD'),
                TO_DATE(#{endDate, jdbcType=VARCHAR}, 'YYYY/MM/DD')
            )
            }
    </update>


    <select id="selectMkTpMattingGroupBy" resultType="com.zqn.modeldata2.entity.Matting">

        SELECT pd_line,phase,sort_no,to_char(shp_date,'yymm') mm,
               MAX(DECODE(to_char(shp_date,'dd'),'01',tvalue,0)) tday01,
               MAX(DECODE(to_char(shp_date,'dd'),'02',tvalue,0)) tday02,
               MAX(DECODE(to_char(shp_date,'dd'),'03',tvalue,0)) tday03,
               MAX(DECODE(to_char(shp_date,'dd'),'04',tvalue,0)) tday04,
               MAX(DECODE(to_char(shp_date,'dd'),'05',tvalue,0)) tday05,
               MAX(DECODE(to_char(shp_date,'dd'),'06',tvalue,0)) tday06,
               MAX(DECODE(to_char(shp_date,'dd'),'07',tvalue,0)) tday07,
               MAX(DECODE(to_char(shp_date,'dd'),'08',tvalue,0)) tday08,
               MAX(DECODE(to_char(shp_date,'dd'),'09',tvalue,0)) tday09,
               MAX(DECODE(to_char(shp_date,'dd'),'10',tvalue,0)) tday10,
               MAX(DECODE(to_char(shp_date,'dd'),'11',tvalue,0)) tday11,
               MAX(DECODE(to_char(shp_date,'dd'),'12',tvalue,0)) tday12,
               MAX(DECODE(to_char(shp_date,'dd'),'13',tvalue,0)) tday13,
               MAX(DECODE(to_char(shp_date,'dd'),'14',tvalue,0)) tday14,
               MAX(DECODE(to_char(shp_date,'dd'),'15',tvalue,0)) tday15,
               MAX(DECODE(to_char(shp_date,'dd'),'16',tvalue,0)) tday16,
               MAX(DECODE(to_char(shp_date,'dd'),'17',tvalue,0)) tday17,
               MAX(DECODE(to_char(shp_date,'dd'),'18',tvalue,0)) tday18,
               MAX(DECODE(to_char(shp_date,'dd'),'19',tvalue,0)) tday19,
               MAX(DECODE(to_char(shp_date,'dd'),'20',tvalue,0)) tday20,
               MAX(DECODE(to_char(shp_date,'dd'),'21',tvalue,0)) tday21,
               MAX(DECODE(to_char(shp_date,'dd'),'22',tvalue,0)) tday22,
               MAX(DECODE(to_char(shp_date,'dd'),'23',tvalue,0)) tday23,
               MAX(DECODE(to_char(shp_date,'dd'),'24',tvalue,0)) tday24,
               MAX(DECODE(to_char(shp_date,'dd'),'25',tvalue,0)) tday25,
               MAX(DECODE(to_char(shp_date,'dd'),'26',tvalue,0)) tday26,
               MAX(DECODE(to_char(shp_date,'dd'),'27',tvalue,0)) tday27,
               MAX(DECODE(to_char(shp_date,'dd'),'28',tvalue,0)) tday28,
               MAX(DECODE(to_char(shp_date,'dd'),'29',tvalue,0)) tday29,
               MAX(DECODE(to_char(shp_date,'dd'),'30',tvalue,0)) tday30,
               MAX(DECODE(to_char(shp_date,'dd'),'31',tvalue,0)) tday31
        FROM MK_TP_MATTING
        WHERE PD_LINE != 'C線CASUAL'
        GROUP BY pd_line,phase,sort_no,to_char(shp_date,'yymm')
        ORDER BY pd_line,sort_no,phase,to_char(shp_date,'yymm')

    </select>
    <select id="query" resultType="com.zqn.modeldata2.entity.Fit">


        SELECT rownum,a.brand_no,
        TO_CHAR(c.TRAN_DATE, 'MM/DD') AS TRAN_DATE,
        TO_CHAR(d.get_date, 'MM/DD') AS get_date,
        TO_CHAR(a.ord_date, 'MM/DD') AS ord_date,
        a.factory,d.ord_qty,b.tot_qty,a.dutyer, TO_CHAR(d.online_date, 'MM/DD') AS online_date,
        a.ord_no,a.model_no,c.MODEL_NAME,e.last_no,b.t1_flag,B.T2_FLAG,B.T3_FLAG,B.Y_FLAG,
        TO_CHAR(b.t2_date, 'MM/DD') as t2_date,
        TO_CHAR(B.U_DATE, 'MM/DD') as U_DATE,
        TO_CHAR(B.B_DATE, 'MM/DD') as 	B_DATE,
        TO_CHAR(B.SHP_DATE, 'MM/DD') as SHP_DATE,
        A.DEV_TYPE,A.UPP_DESC,A.SOL_DESC,C.CFM_FLAG,B.CL_FLAG,
        b.u1_qty,
        b.t5_qty,
        b.b1_qty,
        b.b2_qty,
        b.b3_qty,
        b.b4_qty,
        b.p1_qty
        FROM gc_sorder a ,gc_sorders b ,cb_model c ,
        (SELECT  model_no, MIN(GET_DATE) GET_DATE,  MIN(online_date) online_date,SUM(ord_qty) ord_qty
        FROM gb_custorderx GROUP BY model_no) d, vck_smodel e
        WHERE a.ord_no= b.ord_No AND a.dev_type IN ('FITTING樣品','FITTING兩雙','FITTING大中小')
        AND a.model_no = c.model_no  AND a.model_no = d.model_no(+)
        AND  a.model_no = e.model_no

        <if test="factory != null and factory != ''">
            and a.factory = #{factory}
        </if>
        <if test="brandNo != null and brandNo != ''">
            and a.brand_no = #{brandNo}
        </if>

        <if test="startTime != null and startTime != ''">
            and a.ord_date &gt;= TO_DATE(#{startTime}, 'YYYY-MM-DD')
        </if>

        <if test="endTime != null and endTime != ''">
            and a.ord_date &lt;= TO_DATE(#{endTime}, 'YYYY-MM-DD')
        </if>
        <if test="cfmFlag != null and cfmFlag != ''">
            and  C.CFM_FLAG = #{cfmFlag}
        </if>
        <if test="clFlag != null and clFlag != ''">
            and B.CL_FLAG = #{clFlag}
        </if>
</select>


    <select id="queryList" resultType="com.zqn.modeldata2.entity.Fit">

        SELECT rownum,a.brand_no,
        TO_CHAR(c.TRAN_DATE, 'MM/DD') AS TRAN_DATE,
        TO_CHAR(d.get_date, 'MM/DD') AS get_date,
        TO_CHAR(a.ord_date, 'MM/DD') AS ord_date,
        a.factory,d.ord_qty,b.tot_qty,a.dutyer, TO_CHAR(d.online_date, 'MM/DD') AS online_date,
        c.model_pic,a.ord_no,a.model_no,c.MODEL_NAME,e.last_no,b.t1_flag,B.T2_FLAG,B.T3_FLAG,B.Y_FLAG,
        TO_CHAR(b.t2_date, 'MM/DD') as t2_date,
        TO_CHAR(B.U_DATE, 'MM/DD') as U_DATE,
        TO_CHAR(B.B_DATE, 'MM/DD') as 	B_DATE,
        TO_CHAR(B.SHP_DATE, 'MM/DD') as SHP_DATE,
        A.DEV_TYPE,A.UPP_DESC,A.SOL_DESC,C.CFM_FLAG,B.CL_FLAG,
        b.u1_qty,
        b.t5_qty,
        b.b1_qty,
        b.b2_qty,
        b.b3_qty,
        b.b4_qty,
        b.p1_qty
        FROM gc_sorder a ,gc_sorders b ,cb_model c ,
        (SELECT  model_no, MIN(GET_DATE) GET_DATE,  MIN(online_date) online_date,SUM(ord_qty) ord_qty
        FROM gb_custorderx GROUP BY model_no) d, vck_smodel e
        WHERE a.ord_no= b.ord_No AND a.dev_type IN ('FITTING樣品','FITTING兩雙','FITTING大中小')
        AND a.model_no = c.model_no  AND a.model_no = d.model_no(+)
        AND  a.model_no = e.model_no

        <if test="factory != null and factory != ''">
            and a.factory = #{factory}
        </if>
        <if test="brandNo != null and brandNo != ''">
            and a.brand_no = #{brandNo}
        </if>

        <if test="startTime != null and startTime != ''">
            and a.ord_date &gt;= TO_DATE(#{startTime}, 'YYYY-MM-DD')
        </if>

        <if test="endTime != null and endTime != ''">
            and a.ord_date &lt;= TO_DATE(#{endTime}, 'YYYY-MM-DD')
        </if>
        <if test="cfmFlag != null and cfmFlag != ''">
            and  C.CFM_FLAG = #{cfmFlag}
        </if>
        <if test="clFlag != null and clFlag != ''">
            and B.CL_FLAG = #{clFlag}
        </if>
    </select>
    <select id="queryImages" resultType="com.zqn.modeldata2.entity.Image">
        SELECT model_no , model_pic
        FROM cb_model
        WHERE model_no IN
        <foreach item="modelNo" collection="modelNoList" open="(" separator="," close=")">
            #{modelNo}
        </foreach>
    </select>
    <select id="bottomList" resultType="com.zqn.modeldata2.entity.BottomOrder">
        select * from DSUSER.VPROD_FOLLOWUP
        <where>
            <if test="startTime != null and startTime != ''">
                AND  GET_DATE &gt;= TO_DATE(#{startTime}, 'YYYY-MM-DD')
            </if>

            <if test="endTime != null and endTime != ''">
                AND  GET_DATE &lt;= TO_DATE(#{endTime}, 'YYYY-MM-DD')
            </if>
        </where>
        order by BRAND_NO, SOLE,OUT_CDATE
    </select>
    <select id="queryDayuanList" resultType="com.zqn.modeldata2.entity.DaYuan">

        SELECT A.ORD_SEQ,A.BRAND_NO,C.model_pic,A.ORD_NO,A.DUTYER,A.MODEL_NO,C.LAST_NO,
               B.SHP_DATE,B.U1_QTY, b.t5_qty , b.b1_qty,b.b2_qty,b.b3_qty,b.b4_qty,b.p1_qty
        FROM gc_sorders b, gc_sorder a, vck_smodel c
        WHERE a.ord_no = b.ord_no(+)
          AND a.model_no = c.model_no
          AND b.cl_flag='N' AND b.wo_flag='Y'
          AND INSTR(a.dev_type,'大圖')>0
          AND a.prob_sets>0

        <if test="brandNo != null and brandNo != ''">
            and a.brand_no = #{brandNo}
        </if>

        <if test="startTime != null and startTime != ''">
            AND  B.SHP_DATE &gt;= TO_DATE(#{startTime}, 'YYYY-MM-DD')
        </if>

        <if test="endTime != null and endTime != ''">
            AND B.SHP_DATE &lt;= TO_DATE(#{endTime}, 'YYYY-MM-DD')
        </if>

        order by     B.SHP_DATE

    </select>
    <select id="queryDayuanBrands" resultType="com.zqn.modeldata2.entity.Brand">


        SELECT DISTINCT A.BRAND_NO
        FROM gc_sorders b, gc_sorder a, vck_smodel c
        WHERE a.ord_no = b.ord_no(+)
          AND a.model_no = c.model_no
          AND b.cl_flag='N' AND b.wo_flag='Y'
          AND INSTR(a.dev_type,'大圖')>0
          AND a.prob_sets>0

    </select>
    <select id="getBottomItem" resultType="com.zqn.modeldata2.entity.BottomOrderItem">

        select * from DSUSER.VPROD_DETAIL  where order_no = #{modelNo} and mline = #{mline}

    </select>

</mapper>