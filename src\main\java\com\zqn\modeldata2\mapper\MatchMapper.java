package com.zqn.modeldata2.mapper;

import com.zqn.modeldata2.entity.MkMating;
import com.zqn.modeldata2.entity.MkNpatQcDet;
import com.zqn.modeldata2.entity.MkNpatQcTot;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MatchMapper {
    /**
     * 已配套待配送
     */
    List<MkMating> getWaitSend(@Param("page_no") int page_no, @Param("page_size") int page_size);

    /**
     * 待配送数量
     */
    Integer getSendCount();

    /**
     * 修改配送状态
     */
    Integer updateState(@Param("pdLine") String pdLine, @Param("mating_no") String mating_no);

    /**
     * 修改配送状态2
     */
    Integer updateState2(@Param("pdLine") String pdLine, @Param("ord_no") String ord_no);

    /**
     * 已配套待投入
     */
    List<MkMating> getWaitInput(@Param("page_no") int page_no, @Param("page_size") int page_size, @Param("status") int status);

    /**
     * 待投入数量
     */
    Integer getInputCount(@Param("status") int status);

    /**
     * 未配套库存查询
     */
    List<MkNpatQcTot> getNoMatch(@Param("status") Integer status);

    /**
     * 未配套库存明细
     */
    List<MkNpatQcDet> getNoMatchDetail(@Param("brand_no") String brand_no, @Param("status") Integer status);

    /**
     * 已配套楦头确认
     */
    List<MkNpatQcDet> getMatchConfirm(@Param("page_no") int page_no, @Param("page_size") int page_size, @Param("status") int status);

    List<Integer> getMatchCount(@Param("status3") int status3);

    /**
     * 已配套楦头确认  ——明细
     */
    List<Object> getMatchConfirmDetail(@Param("ord_No") String ord_No, @Param("last_no") String last_no);

    /**
     * 已配套楦头确认  ——操作
     */
    List<Object> getMatchConfirmMXXX(@Param("ord_No") String ord_No, @Param("last_no") String last_no);

    /**
     * 修改楦头状态
     */
    Integer updateXuanTouState(@Param("mating_no") String mating_no);

    /**
     * 成型已投入未產出查詢
     */
    List<MkNpatQcDet> formingQuery(@Param("page_no") int page_no, @Param("page_size") int page_size, @Param("status") Integer status, @Param("status2") Integer status2);

    /**
     * 成型已投入未產出数量
     */
    List<Integer> getformingCount(@Param("status") int status, @Param("status2") int status2, @Param("status3") int status3);

    /**
     * 保存
     */
    int insertConfirmDetail(@Param("sizeList") List<String> sizeList, @Param("sizeListCot") List<String> sizeListCot,
                            @Param("ord_no") String ord_no, @Param("last_no") String last_no, @Param("width") String width, @Param("lr_mark") String lr_mark);
}
