package com.zqn.newmodel.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 新型体开发指令第三个表格
 * @date 2024/8/5 15:32
 */
@Data
public class NewModelTable3 {

    //部位编码
    private String part_no;

    //部位描述
    private String part_name;

    //物料编号
    private String mat_seq;

    //物料名称规格及颜色
    private String mat_desc;

    //单位
    private String suom;

    //单用量
    private String dev_qtya;

    //需求量
    private String req_qty;

    //厂商简称
    private String vnd_sim;

    //备注事项
    private String remark;
}
