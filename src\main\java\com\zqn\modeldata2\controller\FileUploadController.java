package com.zqn.modeldata2.controller;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSONObject;
import com.zqn.modeldata2.common.R;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Iterator;

@RestController
@RequestMapping("/api/files")
public class FileUploadController {

    @Value("${uploadUrl}")
    private String TARGET_FOLDER;

    @Value("${file.prefix}")
    private String filePrefix;

    @PostMapping("/upload2")
    public JSONObject uploadFile2(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return null;
        }

        try {
            Date date = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd-HH");
            String format = sdf.format(date);

            String uploadPath = TARGET_FOLDER + File.separator + "img" + File.separator + format;
            if (!FileUtil.exist(uploadPath)) {
                FileUtil.mkdir(uploadPath);
            }

            // 获取原始文件名
            String originalFileName = file.getOriginalFilename();
            // 获取文件扩展名
            String extension = null;
            try {
                extension = originalFileName.substring(originalFileName.lastIndexOf("."));
            } catch (Exception e) {
                extension = ".jpg";
            }
            // 随机生成文件名
            String fileName = IdUtil.randomUUID() + extension;

            // 获取文件字节
            byte[] bytes = file.getBytes();

            // 将字节数组转换为BufferedImage
            InputStream is = new ByteArrayInputStream(bytes);
            BufferedImage image = ImageIO.read(is);
            if (image == null) {
                return null;
            }

            // 压缩图像
            byte[] compressedImage = compressImage(image, getContentTypeByFileName(fileName), 0.35f);

            // 保存压缩后的图像到服务器
            Path path = Paths.get(uploadPath + File.separator + fileName);
            Files.write(path, compressedImage);

            String fileDownloadUri = "/img" + "/" + format + "/" + fileName;

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("link", filePrefix + "/api/files/getVideo/?url=" + fileDownloadUri);
            return jsonObject;
        } catch (IOException e) {
            throw new RuntimeException("上传失败！");
        }
    }



    @PostMapping("/uploadVideo")
    public JSONObject  uploadVideo(@RequestParam("file") MultipartFile file) {
        Date date = new Date();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd-HH");
        String format = sdf.format(date);

        String uploadPath = TARGET_FOLDER + File.separator + "video" + File.separator + format;
        if (!FileUtil.exist(uploadPath)) {
            FileUtil.mkdir(uploadPath);
        }

        // 获取原始文件名
        String originalFileName = file.getOriginalFilename();
        // 获取文件扩展名
        String extension = null;
        try {
            extension = originalFileName.substring(originalFileName.lastIndexOf("."));
        } catch (Exception e) {
            // 如果没有扩展名，可以设置为默认值或者直接返回错误
            return null;
        }
        // 随机生成文件名
        String fileName = IdUtil.randomUUID() + extension;

        // 保存视频文件到服务器
        try {
            byte[] bytes = file.getBytes();
            Path path = Paths.get(uploadPath + File.separator + fileName);
            Files.write(path, bytes);
        } catch (IOException e) {
            return null;
        }

        String fileDownloadUri = "/video" + "/" + format + "/" + fileName;

//        ObjectMapper mapper = new ObjectMapper(); // Jackson的ObjectMapper用于创建JSON
        JSONObject jsonObject = new JSONObject();

        jsonObject.put("link", filePrefix + "/api/files/getVideo/?url=" + fileDownloadUri);
//        jsonObject.put("url", fileDownloadUri);
        return jsonObject;
    }

    @PostMapping("/upload")
    public R<?> uploadFile(@RequestParam("file") MultipartFile file) {
        if (file.isEmpty()) {
            return R.error("上传失败！");
        }

        try {
            Date date = new Date();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd-HH");
            String format = sdf.format(date);

            String uploadPath = TARGET_FOLDER + File.separator + "img" + File.separator + format;
            if (!FileUtil.exist(uploadPath)) {
                FileUtil.mkdir(uploadPath);
            }

            // 获取原始文件名
            String originalFileName = file.getOriginalFilename();
            // 获取文件扩展名
            String extension = null;
            try {
                extension = originalFileName.substring(originalFileName.lastIndexOf("."));
            } catch (Exception e) {
                extension = ".jpg";
            }
            // 随机生成文件名
            String fileName = IdUtil.randomUUID() + extension;

            // 获取文件字节
            byte[] bytes = file.getBytes();

            // 将字节数组转换为BufferedImage
            InputStream is = new ByteArrayInputStream(bytes);
            BufferedImage image = ImageIO.read(is);
            if (image == null) {
                return R.error("无效的图像文件！");
            }

            // 压缩图像
            byte[] compressedImage = compressImage(image, getContentTypeByFileName(fileName), 0.35f);

            // 保存压缩后的图像到服务器
            Path path = Paths.get(uploadPath + File.separator + fileName);
            Files.write(path, compressedImage);

            String fileDownloadUri = "/img" + "/" + format + "/" + fileName;

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("url", fileDownloadUri);
            return R.success(jsonObject);
        } catch (IOException e) {
            return R.error("上传失败！");
        }
    }

    public byte[] compressImage(BufferedImage bufferImg, String contentType, float quality) {
        try {
            // 获取图片格式
            String formatName = getFormatNameFromContentType(contentType);

            // 获取图片写入器
            Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName(formatName);
            if (!writers.hasNext()) {
                throw new IllegalStateException("No writers found for format: " + formatName);
            }
            ImageWriter writer = writers.next();

            // 设置输出流
            ByteArrayOutputStream byteArrayOut = new ByteArrayOutputStream();
            try (ImageOutputStream ios = ImageIO.createImageOutputStream(byteArrayOut)) {
                writer.setOutput(ios);

                // 设置压缩参数
                ImageWriteParam param = writer.getDefaultWriteParam();
                if (param.canWriteCompressed()) {
                    param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
                    param.setCompressionQuality(quality); // 设置压缩质量
                }

                // 写入图像
                writer.write(null, new IIOImage(bufferImg, null, null), param);
            }

            // 释放资源
            writer.dispose();

            return byteArrayOut.toByteArray();

        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * @description: 获取图片
     * @param: url
     * response
     * @return: void
     * <AUTHOR> Yang
     * @date: 2024/5/9 8:39
     */
    @GetMapping(value = "/get")
    @ResponseBody
    public void getImage(@RequestParam(value = "url") String url, HttpServletResponse response) throws IOException {
        File imageFile = new File(TARGET_FOLDER + File.separator + url);
        if (imageFile.exists()) {
            String contentType = getContentTypeByFileName(url);
            response.setContentType(contentType);
            try (InputStream is = new FileInputStream(imageFile)) {
                BufferedImage image = ImageIO.read(is);
                if (image != null) {
                    String formatName = getFormatNameFromContentType(contentType);
                    ImageIO.write(image, formatName, response.getOutputStream());
                } else {
                    response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                }
            }
        } else {
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
        }
    }

    @GetMapping(value = "/getVideo")
    @ResponseBody
    public void getVideo(@RequestParam(value = "url") String url, HttpServletResponse response) throws IOException {
        File videoFile = new File(TARGET_FOLDER + File.separator + url);
        if (videoFile.exists()) {
            String contentType = getContentTypeByVideoFileName(url);
            response.setContentType(contentType);
            response.setContentLength((int) videoFile.length());

            try (
                    BufferedInputStream input = new BufferedInputStream(new FileInputStream(videoFile));
                    OutputStream output = response.getOutputStream()
            ) {
                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = input.read(buffer)) != -1) {
                    output.write(buffer, 0, bytesRead);
                }
                output.flush();
            }
        } else {
            response.setStatus(HttpServletResponse.SC_NOT_FOUND);
        }
    }


    private String getContentTypeByVideoFileName(String fileName) {
        // 根据文件扩展名获取对应的MIME类型
        if (fileName.endsWith(".mp4")) {
            return "video/mp4";
        } else if (fileName.endsWith(".webm")) {
            return "video/webm";
        } else if (fileName.endsWith(".ogg")) {
            return "video/ogg";
        } else if (fileName.endsWith(".ogv")) {
            return "video/ogg";
        } else if (fileName.endsWith(".avi")) {
            return "video/x-msvideo";
        } else if (fileName.endsWith(".wmv")) {
            return "video/x-ms-wmv";
        } else if (fileName.endsWith(".mov")) {
            return "video/quicktime";
        } else if (fileName.endsWith(".flv")) {
            return "video/x-flv";
        } else if (fileName.endsWith(".3gp")) {
            return "video/3gpp";
        } else if (fileName.endsWith(".3g2")) {
            return "video/3gpp2";
        } else {
            // 默认类型，或者如果文件扩展名不匹配，你可以返回一个通用的类型
            // 或者返回null，表示无法识别文件类型
            return "application/octet-stream";
        }
    }

    private String getContentTypeByFileName(String fileName) {
        String fileExtension = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
        switch (fileExtension) {
            case "png":
                return MediaType.IMAGE_PNG_VALUE;
            case "gif":
                return "image/gif";
            case "bmp":
                return "image/bmp";
            default:
                return MediaType.IMAGE_JPEG_VALUE; // Default to JPEG
        }
    }

    private String getFormatNameFromContentType(String contentType) {
        switch (contentType) {
            case MediaType.IMAGE_PNG_VALUE:
                return "png";
            case "image/gif":
                return "gif";
            case "image/bmp":
                return "bmp";
            default:
                return "jpeg"; // Default to JPEG
        }
    }
}