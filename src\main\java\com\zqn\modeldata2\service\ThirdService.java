package com.zqn.modeldata2.service;

import com.zqn.modeldata2.entity.CkSmodelpa;

import java.util.List;
import java.util.Map;

public interface ThirdService {
    Map<String, List<Object>> getOption();

    Map<String, List<CkSmodelpa>> getPart(CkSmodelpa ckSmodelpa);

    int addTemporaryPart(CkSmodelpa ckSmodelpa);

    int addPart(List<CkSmodelpa> ckSmodelpaList);

    int updatePart(CkSmodelpa ckSmodelpa);

    int deletePart(CkSmodelpa ckSmodelpa);

    int batchDeletePart(List<CkSmodelpa> ckSmodelpaList);
}
