package com.zqn.modeldata2.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.entity.MbptDtDto;
import com.zqn.modeldata2.entity.MbptDto;
import com.zqn.modeldata2.entity.MkSorderbarDto;
import com.zqn.modeldata2.mapper.MbptMapper;
import com.zqn.modeldata2.service.MbptService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/7/25 15:30
 */
@Service
public class MbptserviceImpl implements MbptService {

    @Resource
    private MbptMapper mbptMapper;

    @Override
    public R<PageInfo<MbptDto>> query(int pageNo, int pageSize, Date startTime, Date endTime, String brand, String devType, Integer fileType, Integer cutComplType, String searchOrdNo, String pdLine, String t2Flag, String bDateStr,String grpNO) throws ParseException {
        Date bDate = null;
        List<Date> dates = new ArrayList<>();
        if (bDateStr != null && !bDateStr.isEmpty()) {

            String[] split = bDateStr.split(",");
            for (String s : split) {
                // 将输入的日期格式从 YYYY-MM-DD 转换为 YYYY/MM/DD
                // 定义日期格式
                SimpleDateFormat formatter = new SimpleDateFormat("yyyy/MM/dd");
                // 将字符串解析为日期对象
                bDate = formatter.parse("20"+s);
                dates.add(bDate);
            }
        }

        //统计订单总量
        MbptDto count = mbptMapper.querycount(startTime, endTime, brand, devType, fileType, cutComplType, searchOrdNo,
                pdLine, t2Flag, dates,grpNO);
        PageHelper.startPage(pageNo, pageSize);
        List<MbptDto> mbptDtos = mbptMapper.query(startTime, endTime, brand, devType, fileType, cutComplType, searchOrdNo,
                pdLine, t2Flag, dates,grpNO);
        for (MbptDto mbptDto : mbptDtos) {
            String dutyer = mbptDto.getDutyer();
            //过滤掉业务/版师字段中的数字
            String[] split = dutyer.split("\\d+");
            if (split.length > 0) {
                dutyer = split[0];
            }
            mbptDto.setDutyer(dutyer);
            //处理订单总量
            if (count != null) {
                mbptDto.setTot_qty_sum(count.getTot_qty_sum());
            }
        }
        PageInfo<MbptDto> pageInfo = new PageInfo<>(mbptDtos);
        return R.success(pageInfo);
    }

    @Override
    public List<JSONObject> queryAllDevType() {
        List<JSONObject> result = new ArrayList<JSONObject>();
        List<String> devTypes = mbptMapper.queryAllDevType();
        if (devTypes.size() > 0) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("value", "");
            jsonObject.put("text", "請選擇");
            result.add(jsonObject);
        }
        for (String devType : devTypes) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("value", devType);
            jsonObject.put("text", devType);
            result.add(jsonObject);
        }
        return result;
    }

    @Override
    public List<MbptDtDto> queryDetailTableData(String userNo, String grpNo) {
        return mbptMapper.queryDetailTableData(userNo, grpNo);
    }

    @Override
    @Transactional
    public Integer addMadeDept(MkSorderbarDto mkSorderbarDto) {
        try {
            Map<String, Object> params = new HashMap<>();
            params.put("i_ord_no", mkSorderbarDto.getOrd_no());
            params.put("i_made_dept", mkSorderbarDto.getMade_dept());
            params.put("i_bar_qty", mkSorderbarDto.getBar_qty());
            params.put("i_semi_no", "U");
            params.put("i_semi_su", "1");
            params.put("o_return", ""); // 初始化输出参数

            mbptMapper.addMadeDept(params);
            String result = (String) params.get("o_return");
            //失败消息为：1.鞋面投入失敗！
            if (result.indexOf("1") > -1) {
                throw new Exception(result);
            }
            return 1;
        } catch (Exception e) {
            // 发生异常时抛出新异常
            throw new RuntimeException("An error occurred during editing made dept", e);
        }
    }

    @Override
    @Transactional
    public Integer editMadeDept(MkSorderbarDto mkSorderbarDto) {
        try {
            List<Date> bardates = mbptMapper.queryBarDate(mkSorderbarDto.getOrd_no());
            if (CollectionUtils.isEmpty(bardates)) {
                throw new Exception("未找到投入扫描明细！");
            }
            // 调用mbptMapper.editMadeDept方法
            return mbptMapper.editMadeDept(mkSorderbarDto, bardates.get(0));
        } catch (Exception e) {
            // 发生异常时抛出新异常，并传递原始异常作为原因
            throw new RuntimeException("An error occurred during editing made dept", e);
        }
    }

    @Override
    public List<MkSorderbarDto> queryMkSorderbarDtoByOrdNo(String ordNo) {
        return mbptMapper.queryMkSorderbarDtoByOrdNo(ordNo);
    }

    @Override
    @Transactional
    public String fwtr(MkSorderbarDto mkSorderbarDto) {
        String result = "";
        Map<String, Object> params = new HashMap<>();
        params.put("i_ord_no", mkSorderbarDto.getOrd_no());
        params.put("i_made_dept", mkSorderbarDto.getMade_dept());
        params.put("i_bar_qty", mkSorderbarDto.getBar_qty());
        params.put("i_semi_no", "Y");
        params.put("i_semi_su", "1");
        params.put("o_return", ""); // 初始化输出参数

        //发外投入产出
        mbptMapper.fwtrcc(params);
        result = (String) params.get("o_return");
        return result;
    }

    @Override
    @Transactional
    public String fwcc(MkSorderbarDto mkSorderbarDto) {
        Map<String, Object> params = new HashMap<>();
        params.put("i_ord_no", mkSorderbarDto.getOrd_no());
        params.put("i_made_dept", mkSorderbarDto.getMade_dept());
        params.put("i_bar_qty", mkSorderbarDto.getBar_qty());
        params.put("i_semi_no", "Y");
        params.put("i_semi_su", "2");
        params.put("o_return", ""); // 初始化输出参数

        //发外投入产出
        mbptMapper.fwtrcc(params);
        String result = (String) params.get("o_return");
        return result;
    }

    /**
     * @description: 取消投入、出产
     * @param: mkSorderbarDto
     * @return: java.lang.String
     * <AUTHOR> Yang
     * @date: 2024/8/26 13:31
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRED, isolation = Isolation.DEFAULT)
    public String cancel(MkSorderbarDto mkSorderbarDto) {
        // 参数校验
        if (mkSorderbarDto == null) {
            throw new IllegalArgumentException("Invalid parameter for cancel operation.");
        }

        try {
            // 发外投入产出
            mbptMapper.cancel(mkSorderbarDto);
            // 成功消息
            String result = "cancel.success";
            return result;
        } catch (Exception e) {
            throw new RuntimeException("Failed to cancel operation.", e);
        }
    }
}
