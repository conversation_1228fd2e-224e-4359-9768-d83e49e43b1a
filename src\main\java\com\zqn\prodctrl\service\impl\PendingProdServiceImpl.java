package com.zqn.prodctrl.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zqn.prodctrl.entity.PendingProd;
import com.zqn.prodctrl.mapper.PendingProdMapper;
import com.zqn.prodctrl.service.PendingProdService;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/8/27 11:03
 */
@Service
public class PendingProdServiceImpl implements PendingProdService {

    private static final Logger logger = LoggerFactory.getLogger(PendingProdServiceImpl.class);

    @Autowired
    private PendingProdMapper pendingProdMapper;

    @Override
    public PageInfo<PendingProd> query(int pageNo, int pageSize, Date startTime, Date endTime, String brand, String ordNo, String devType,  String phase, String pdLine) {
        // 创建一个SimpleDateFormat对象，指定日期格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
        // 将Date对象格式化为指定格式的字符串
        String startTimeStr = sdf.format(startTime);
        String endTimeStr = sdf.format(endTime);
        PendingProd count = pendingProdMapper.queryCount(startTimeStr, endTimeStr, brand, ordNo, devType,phase,pdLine);
        PageHelper.startPage(pageNo, pageSize);
        List<PendingProd> pendingProds = pendingProdMapper.query(startTimeStr, endTimeStr, brand, ordNo, devType,phase,pdLine);
        PageInfo<PendingProd> pageInfo = new PageInfo<>(pendingProds);
        long total = pageInfo.getTotal();
        System.out.println(total);
        for (PendingProd pendingProd : pageInfo.getList()) {
            //处理订单总量
            if (count != null) {
                pendingProd.setTot_qty_sum(count.getTot_qty_sum());
            }
        }
        return pageInfo;
    }

    @Override
    public List<JSONObject> queryAllDevType() {
        List<JSONObject> result = new ArrayList<JSONObject>();
        List<String> devTypes = pendingProdMapper.queryAllDevType();
        if (devTypes.size() > 0) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("value", "");
            jsonObject.put("text", "請選擇");
            result.add(jsonObject);
        }
        for (String devType : devTypes) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("value", devType);
            jsonObject.put("text", devType);
            result.add(jsonObject);
        }
        return result;
    }

    @Override
    @Transactional
    public String edit(PendingProd pendingProd) throws IllegalArgumentException {
        // 验证订单号和订单明细是否为空
        if (StringUtils.isBlank(pendingProd.getOrd_no()) || StringUtils.isBlank(pendingProd.getItem_no())) {
            throw new IllegalArgumentException("订单号或订单明细不能为空");
        }

        try {
            // 调用数据库操作方法
            pendingProdMapper.edit(pendingProd.getOrd_no(), pendingProd.getItem_no());
            logger.info("订单 {} 的明细 {} 已成功更新", pendingProd.getOrd_no(), pendingProd.getItem_no());
            return "投入成功！";
        } catch (Exception e) {
            logger.error("编辑订单时发生异常：{}", e.getMessage(), e);
            throw new RuntimeException("编辑订单时发生异常，请稍后重试", e);
        }
    }
}
