package com.zqn.modeldata2.service.impl;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.zqn.modeldata2.service.MicrosoftService;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

@Service
public class MicrosoftServiceImpl implements MicrosoftService {

    private RestTemplate restTemplate;

    // 构造函数注入RestTemplate
    public MicrosoftServiceImpl(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    // 调用Microsoft API进行翻译
    public JSONArray callMicrosoftAPITranslate(JSONObject json) throws JSONException {
        JSONArray result = new JSONArray();
        String textToTranslate = json.getString("textToTranslate");
        //源语言
        String sourceLang = json.getString("sourceLang");
        //目标语言
        JSONArray langs = json.getJSONArray("lang");
        String url = "https://api.cognitive.microsofttranslator.com/translate?api-version=3.0&from=" + sourceLang + "";
        for (Object lang : langs) {
            if (lang.equals(sourceLang)) {
                continue;
            }
            url += "&to=" + lang;
        }
        // 设置HTTP请求头
        HttpHeaders headers = new HttpHeaders();
        headers.set("Ocp-Apim-Subscription-Key", "2e2e4439ebf84766a0860ad3fba3120d");
        headers.set("Content-Type", "application/json");
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 构建请求体
        JSONArray jsonArray = new JSONArray();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("Text", textToTranslate);
        jsonArray.add(jsonObject);

        // 发起POST请求
        HttpEntity<String> request = new HttpEntity<>(jsonArray.toString(), headers);
        ResponseEntity<String> response = restTemplate.postForEntity(url, request, String.class);

        // 处理API响应
        JSONObject temp = new JSONObject();
        if (response.getStatusCode() == HttpStatus.OK) {
            JSONArray responseBody = JSONArray.parseArray(response.getBody());
            System.out.println(responseBody.toString());
            result = responseBody;
        } else {
            temp.put("data", "Error occurred while calling Trans API");
            result.add(temp);
        }
        return result;
    }
}
