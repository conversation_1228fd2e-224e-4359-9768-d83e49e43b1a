package com.zqn.modeldata2.mapper;

import com.zqn.modeldata2.entity.MaterialMatch;
import com.zqn.modeldata2.entity.MaterialMatchDt;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
public interface MaterialMatchMapper {

    List<MaterialMatch> query(@Param("startTime") Date startTime,
                              @Param("endTime") Date endTime,
                              @Param("brand") String brand,
                              @Param("devType") String devType,
                              @Param("fileType") Integer fileType, Integer cutComplType);


    Date queryMaxShDate(@Param("ord_no") String ord_no);

    Integer update(@Param("ordNo") String ord_no, @Param("itemNo") String itemNo);

    List<String> queryAllDevType();

    List<MaterialMatchDt> queryDetailTableData(@Param("ordNo") String ordNo);

    List<MaterialMatch> selectManualClose(@Param("ordNo") String ordNo);

    void manualClose(Map<String, Object> params);

    BigDecimal queryTotQtyCount(Date startTime, Date endTime, String brand, String devType, Integer fileType, Integer cutComplType);
}