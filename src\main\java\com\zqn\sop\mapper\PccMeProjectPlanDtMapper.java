package com.zqn.sop.mapper;

import com.zqn.sop.entity.PccMeProjectPlanActions;
import com.zqn.sop.entity.PccMeProjectPlanDt;
import com.zqn.sop.entity.PccMeProjectPlanImg;
import com.zqn.sop.entity.PccMeProjectPlanVideo;
import com.zqn.sop.vo.PccMeProjectPlanDtVo;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PccMeProjectPlanDtMapper {

    int add(@Param("dt") PccMeProjectPlanDt planDt);

    List<PccMeProjectPlanDtVo> query(@Param("parentId") Integer parentId);

    List<String> queryTools(@Param("ids") List<Integer> id);

    List<PccMeProjectPlanImg> queryImg(@Param("id") Integer id);

    PccMeProjectPlanDtVo queryByItem(@Param("item") String item, @Param("version") Integer version,@Param("parentId") Integer parentId);

    @MapKey("id")
    List<String> queryAllTools(@Param("content") String content, @Param("searchType") Integer searchType, @Param("dept") Integer dept);

    int insertImg(@Param("parentId") Integer parentId, @Param("imgUrl") String imgUrl, @Param("remark") String remark, @Param("type") Integer type);

    Integer selectMax(
            @Param("model") String model, @Param("dept") String dept,
            @Param("factory") String factory);

    int insertContent(@Param("parentId") Integer id,
                      @Param("lang") String lang,
                      @Param("opStd") String opStd,
                      @Param("selfCheckPoints") String selfCheckPoints);

    List<Integer> queryAllItem(@Param("parentId") Integer parentId);

    List<Integer> queryAllVersion( @Param("itemNo") String itemNo, @Param("parentId") Integer parentId);

    List<PccMeProjectPlanActions> queryAllActions(@Param("item") String item,
                                                  @Param("type") String type);

    void incrementItemWithOffset(@Param("itemNo")Integer itemNo,@Param("parentId")Integer parentId);

    void finalizeItemUpdate(@Param("itemNo")Integer itemNo, @Param("parentId")Integer parentId);

    void deleteItemWithOffset(@Param("itemNo")Integer itemNo, @Param("parentId")Integer parentId);

    void deleteItemUpdate( @Param("itemNo")Integer itemNo, @Param("parentId")Integer parentId);

    Integer deleteItem(@Param("parentId") Integer parentId,
                       @Param("itemNo") Integer itemNo);

    Integer deleteContent(@Param("parentId") Integer parentId,
                          @Param("itemNo") Integer itemNo);

    Integer deleteImg(@Param("parentId") Integer parentId,
                      @Param("itemNo") Integer itemNo);

    List<PccMeProjectPlanActions> queryActionsByTags(@Param("item") String item,
                                                     @Param("type") String type,
                                                     @Param("tag") String tag);

    List<PccMeProjectPlanDtVo> queryAllDt(@Param("parentId") Integer parentId);

    int insertVideo(@Param("parentId") Integer parentId, @Param("url") String url, @Param("name") String name);

    List<PccMeProjectPlanVideo> queryVideo(@Param("id") Integer id);

    Integer selectMin(
            @Param("model") String model, @Param("dept") String dept,
            @Param("factory") String factory);
}