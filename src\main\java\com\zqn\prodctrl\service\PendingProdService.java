package com.zqn.prodctrl.service;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.zqn.prodctrl.entity.PendingProd;

import java.util.Date;
import java.util.List;

public interface PendingProdService {

    PageInfo<PendingProd> query(int pageNo, int pageSize, Date startTime, Date endTime, String brand, String ordNo, String devType, String phase, String pdLine);

    List<JSONObject> queryAllDevType();

    String edit(PendingProd schedProd) throws Exception;
}
