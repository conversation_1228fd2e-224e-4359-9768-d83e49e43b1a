package com.zqn.modeldata2.mapper;

import com.zqn.modeldata2.entity.*;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface MattingMapper {


    // 调用存储过程的方法，参数类型为String
    void callPdMkLastcomp(@Param("startDate") String startDate, @Param("endDate") String endDate);

    List<Matting> selectMkTpMattingGroupBy();

    List<Fit> query(@Param("factory")  String factory, @Param("brandNo")  String brandNo, @Param("startTime")  String startTime,
                    @Param("endTime")  String endTime, @Param("cfmFlag")  String cfmFlag,  @Param("clFlag")  String clFlag );

    List<Fit> queryList(@Param("factory")  String factory, @Param("brandNo")  String brandNo, @Param("startTime")  String startTime,
                    @Param("endTime")  String endTime, @Param("cfmFlag")  String cfmFlag,  @Param("clFlag")  String clFlag );

    List<Image>  queryImages(@Param("modelNoList") List<String> modelNoList);

    List<BottomOrder> bottomList(@Param("startTime") String startTime, @Param("endTime") String endTime);

    List<DaYuan> queryDayuanList(@Param("brandNo") String brandNo, @Param("startTime")  String startTime, @Param("endTime") String endTime);

    List<Brand> queryDayuanBrands();

    List<BottomOrderItem> getBottomItem(@Param("modelNo") String modelNo, @Param("mline") String mline);
}