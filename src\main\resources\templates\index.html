<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org" lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <title>鞋面作业流程</title>
    <style>
        * {
            margin: 0;
            padding: 0;
        }

        body {
            font-family: Arial, "宋体", sans-serif;
            color: #333;
            background-color: #ffff;
        }

        .container-info {
            margin-top: 20px;
            margin-bottom: 10px;
            margin-left: 50px;
        }
        .bottom-info {
            margin-top: 20px;
        }
        .column {
            flex: 1;
            padding: 20px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            box-sizing: border-box; /* 边框包含在宽度内 */
        }

        th,
        td {
            border: 1px solid black;
            text-align: center;
        }



        .content-left {
            text-align: left;
            font-weight: normal;
        }

        .content-left-top, .content-left-top2 {
            padding-top: 10px;
            vertical-align: top;
            text-align: left;
            font-weight: normal;
        }

        .text,
        td {
            border: 1px solid black;
            font-weight: bold;
            /* text-align: left; */
            text-align: center;
        }

        .text2 {
            font-weight: bold;
            text-align: center;
        }


        th {
            background-color: #f2f2f2;
        }

        thead th {
            height: 50px;
        }

        h1 {
            text-align: center;
        }

        .icon {
            height: 40px;
        }

        .container-title {
            margin-top: 20px;
            display: inline-block;
            vertical-align: middle;
            /*line-height: 60px;*/
            /* 高度应该与图片的高度一致 */
        }


        .title-text {
            margin-left: 240px;
            font-size: 30px;
            font-weight: bold;
            display: inline-block;
            vertical-align: middle;
        }

        h1 {
            margin-left: auto;
            /* 使标题在其容器中水平居中 */
            margin-right: auto;
            /* 使标题在其容器中水平居中 */
            text-align: center;
            /* 使文本在h1内水平居中，如果需要的话 */
        }


        .fixed-cell {
            width: 333px;
            height: 421px;
            font-size: 0px;
        }

        .image-container {
            display: inline-block;
            width: 33.34%;
            height: 33.34%;
        }

        .image-container img {
            width: 100%;
            height: 100%;
        }

        .container-info span {
            font-weight: bold;
        }

        td, th {
            word-wrap: break-word;
        }

        .content-left-top > span {
            display: block; /* 或者 display: flex; */
            max-height: 270px;
            overflow: hidden;
        }


        .right-bordered-td {
            border-right: 1px solid black; /* 为具有right-bordered-td类的td添加右边框 */
        }

        .content-left-top2 > span {
            display: block;
            max-height: 360px;
            overflow: hidden;
        }




        .content > span {
            height: 110px;
            /*position: absolute; !* 绝对定位 *!*/
            top: 50%; /* 从顶部偏移50% */
            left: 50%; /* 从左侧偏移50% */
            transform: translate(-50%, -50%); /* 使用transform调整位置 */

        }
        .content {
            height: 110px; /* 设置td的高度 */
            display: table-cell; /* 使td表现得像表格单元格 */
            vertical-align: middle; /* 垂直居中 */
            text-align: center; /* 水平居中 */
        }

        .content > div {
            max-height: 110px;
            display: inline-block; /* 使div成为内联块级元素 */
            vertical-align: middle; /* 垂直居中 */
            text-align: center; /* 水平居中 */
            max-width: 180px; /* 确保div的宽度不会超过td */
            overflow: hidden; /* 防止内容溢出 */
        }

        .icon-wrapper {
            /*margin-top: 40px;*/
            position: relative;
            display: inline-block;
            margin-left: 100px;
            vertical-align: middle;
        }

        .icon-wrapper .icon {
            width: 100px;
            height: 60px;
        }

        .icon-wrapper::before {
            content: "STELLA";
            position: absolute;
            top: 2px;
            left: 2px;
            color: white;
            font-size: 12px;
            font-weight: bold;
            text-shadow: 0 0 5px black;
            z-index: 1;
            pointer-events: none;
        }


    </style>
</head>

<body>
<div class="container">

    <div class="container-title">
        <div class="icon-wrapper">
            <img class="icon" th:src="'data:image/jpg;base64,'+${base64Image}"/>
        </div>

        <span class="title-text"
              th:text=" ${pdfRequest.simpleName} + '作业流程 Operation process'">鞋面作业流程 Operation process</span>

    </div>


    <div class="container-info">
        <span style="margin-left: 30px;">客户Brand:</span>
        <span th:text="${pdfRequest.brand}"></span>

        <span style="margin-left: 280px;">Style:</span>
        <span th:text="${pdfRequest.modelNo}"></span>

        <span style="margin-left: 280px;">日期Date:</span>
        <span th:text="${item.createDate}">3333</span>
    </div>


    <table>
        <thead>
        <th style="width: 100px">项次 No.</th>
        <th style="width: 230px">流程步骤 Process</th>
        <th style="width: 330px">操作标准 The Operation Standard</th>
        <th style="width: 180px">工具 Tool</th>

        <th style="width: 180px" th:if="${pdfRequest.type == 1}">
            化学品 Chemical
        </th>
        <th style="width: 180px" th:if="${pdfRequest.type == 2}">
            胶水 glue
        </th>

        <th style="width: 180px">机器 machine</th>

        <th style="width: 180px" th:if="${pdfRequest.type == 1}">
            防护用品<br>protectiveGear
        </th>
        <th style="width: 180px" th:if="${pdfRequest.type == 2}">
            车线 thread
        </th>
        </thead>

        <tbody>
        <tr>
            <td rowspan="3" class="content" th:text="${item.item_no}"></td>
            <td rowspan="3" class="content" th:text="${item.actions}"></td>
            <td rowspan="3" class="content-left-top">
                <span th:utext="${item.op_std}"></span>
            </td>

            <td class="content">
                <div th:text="${item.tools}"></div>
            </td>
<!--            <td class="content" >-->
<!--                <div>剂剂技术手册吊牌布袋1112223334剂剂技术手册吊牌布袋剂剂技术手册吊牌布袋剂剂技术手册吊牌布袋剂剂技术手册吊牌布袋剂剂技术手册吊牌布袋剂剂技术手册吊牌布袋剂剂技术手册吊牌布袋剂剂技术手册吊牌布袋剂剂技术手册吊牌布袋剂剂技术手册吊牌布袋剂剂技术手册吊牌布袋剂剂技术手册吊牌布袋剂剂技术手册吊牌布袋剂剂技术手册吊牌布袋剂剂技术手册吊牌布袋剂剂技术手册吊牌布袋剂剂技术手册吊牌布袋剂剂技术手册吊牌布袋</div>-->
<!--            </td>-->


            <td class="content" th:if="${pdfRequest.type == 1}">
                    <div th:text="${item.chemical_substance}">

                </div>

            </td>
            <td class="content" th:if="${pdfRequest.type == 2}">
                <div th:text="${item.glue}"></div>

            </td>


            <td class="content">
                <div th:text="${item.machine}"></div>

            </td>

            <td class="content" th:if="${pdfRequest.type == 1}">
                <div th:text="${item.protective_gear}"></div>
            </td>
            <td class="content" th:if="${pdfRequest.type == 2}">
                <div th:text="${item.car_line}"></div>

            </td>

        </tr>
        <tr class="text">


            <th th:if="${pdfRequest.type == 1}">
                温度 Temp
            </th>
            <th th:if="${pdfRequest.type == 2}">
                边距 margin
            </th>


            <th th:if="${pdfRequest.type == 1}">
                壓力 Pressure
            </th>
            <th th:if="${pdfRequest.type == 2}">
                针距 stitch
            </th>


            <th th:if="${pdfRequest.type == 1}">
                時間 Time
            </th>
            <th th:if="${pdfRequest.type == 2}">
                间距 spac
            </th>


            <th style="height: 50px;">车针 needle</th>
        </tr>
        <tr class="xxxx">


            <td class="content" th:if="${pdfRequest.type == 1}">
                <div th:text="${item.temp}"></div>
            </td>
            <td class="content" th:if="${pdfRequest.type == 2}">
                <div th:text="${item.margin}"></div>
            </td>


            <td class="content" th:if="${pdfRequest.type == 1}">
                <div th:text="${item.pressure}"></div>
            </td>
            <td class="content" th:if="${pdfRequest.type == 2}">
                <div th:text="${item.needle_spacing}"></div>
            </td>


            <td class="content" th:if="${pdfRequest.type == 1}">
                <div th:text="${item.time}"></div>
            </td>
            <td class="content" th:if="${pdfRequest.type == 2}">
                <div th:text="${item.spacing}"></div>
            </td>


            <td  class="content">
                <div th:text="${item.needle}"></div>
            </td>


        </tr>
        <tr>
            <td rowspan="3" colspan="2" class="fixed-cell" style="text-align: left;vertical-align: top">

                <div class="image-container" th:each="imageUrl, iterStat : ${item.type1Imgs}">
                    <img th:src="${imageUrl}">
                </div>


            </td>
            <td rowspan="3" class="fixed-cell" style="text-align: left;vertical-align: top">

                <div class="image-container" th:each="imageUrl, iterStat : ${item.type2Imgs}">
                    <img th:src="${imageUrl}">
                </div>


            </td>
            <th colspan="4" style="height: 50px;" class="text2">自检点 Self-check point</th>
        </tr>
        <tr>

            <td rowspan="2" colspan="4" class="content-left-top2">
        <span th:utext="${item.self_check_points}">

        </span>
            </td>

        </tr>
        </tbody>

    </table>

    <div class="bottom-info">
        <span style="margin-left: 20px;">核准 Approval:</span>

        <span style="margin-left: 350px;">审核 Check by:</span>

        <span style="margin-left: 360px;">制表 Tab:</span>
        <span th:text="${item.create_by}"></span>
    </div>
</body>

</html>