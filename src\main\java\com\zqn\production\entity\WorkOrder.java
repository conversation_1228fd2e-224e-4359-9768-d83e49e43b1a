package com.zqn.production.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 工单实体类
 */
@Data
public class WorkOrder {
    // 选择标志
    private String se_flag;
    // 制作部门
    private String made_dept;
    // 员工姓名
    private String emp_name;
    // 工单号
    private String ord_no;
    // 型号图片
    private byte[] model_pic;
    // 设备类型
    private String dev_type;
    // 出货日期
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date shp_date;
    // 总数量
    private String tot_qty;
    // 条码数量
    private String bar_qty;
    // 生产日期
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date prd_date;
    // 目标PPH
    private Double run_rate;
    // 完成率
    private Double rate1;
    // 描述
    private String pb_desc;
    // 型体
    private String model_no;
    // 底料状况
    private String t2_flag;
} 