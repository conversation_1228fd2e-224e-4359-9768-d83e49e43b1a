package com.zqn.modeldata2.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.entity.MbptDtDto;
import com.zqn.modeldata2.entity.MbptDto;
import com.zqn.modeldata2.entity.MkSorderbarDto;
import com.zqn.modeldata2.entity.QdptDt;
import com.zqn.modeldata2.service.MbptService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 面部料板配套
 * @date 2024/7/25 15:18
 */
@RestController
@RequestMapping("/mbpt")
@Validated
public class MbptController {

    @Resource
    private MbptService mbptService;

    /**
     * @description: 查询
     * @param: pageNo
     * pageSize
     * startTimeTamp
     * endTimeTamp
     * brand
     * devType
     * fileType
     * cutComplType
     * searchOrdNo
     * @return: com.zqn.modeldata2.common.R<com.github.pagehelper.PageInfo < com.zqn.modeldata2.entity.MbptDto>>
     * <AUTHOR> Yang
     * @date: 2024/8/21 14:27
     */
    @GetMapping("/query")
    public R<PageInfo<MbptDto>> query(@RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                      @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
                                      @RequestParam(value = "startTime", required = false) long startTimeTamp,
                                      @RequestParam(value = "endTime", required = false) long endTimeTamp,
                                      @RequestParam(value = "brand", required = false) String brand,
                                      @RequestParam(value = "devType", required = false) String devType,
                                      @RequestParam(value = "fileType", required = false) Integer fileType,
                                      @RequestParam(value = "cutComplType", required = false) Integer cutComplType,
                                      @RequestParam(value = "ordNo", required = false) String searchOrdNo,
                                      @RequestParam(value = "pdLine", required = false) String pdLine,
                                      @RequestParam(value = "t2Flag", required = false) String t2Flag,
                                      @RequestParam(value = "bDate", required = false) String bDate,
                                      @RequestParam(value = "groNo", required = false) String groNo
                                      ) throws ParseException {
        Date startTime = new Date(startTimeTamp); // 将时间戳转换为Date对象
        Date endTime = new Date(endTimeTamp); // 将时间戳转换为Date对象
        if (startTimeTamp == 0 && endTimeTamp == 0) {
            startTime = null;
            endTime = null;
        }
        return mbptService.query(pageNo, pageSize, startTime, endTime, brand, devType, fileType, cutComplType, searchOrdNo, pdLine, t2Flag, bDate,groNo);
    }

    /**
     * @description: 查询所有样品类型
     * @param:
     * @return: com.zqn.modeldata2.common.R<java.util.List < com.alibaba.fastjson.JSONObject>>
     * <AUTHOR> Yang
     * @date: 2024/8/21 14:27
     */
    @GetMapping("/queryAllDevType")
    public R<List<JSONObject>> queryAllDevType() {
        List<JSONObject> result = mbptService.queryAllDevType();
        return R.success(result);
    }

    /**
     * @description: 用戶在平板上鞋面投入欄 設定組別，彈出窗口供用戶選擇製作組別 
     * @param: userNo
     * @return: com.zqn.modeldata2.common.R<java.util.List < com.zqn.modeldata2.entity.MbptDtDto>>
     * <AUTHOR> Yang
     * @date: 2024/8/21 14:27
     */
    @GetMapping("/queryDetailTableData")
    public R<List<MbptDtDto>> queryDetailTableData(@RequestParam(value = "userNo", required = true) String userNo,
                                                   @RequestParam(value = "grpNo", required = true) String grpNo) {
        // 调用qdptService的服务方法，根据订单号查询详细表格数据
        List<MbptDtDto> result = mbptService.queryDetailTableData(userNo, grpNo);
        // 返回查询结果，包装在R对象中，表示操作成功
        return R.success(result);
    }

    /**
     * @description: 设置投入组别
     * @param: mkSorderbarDto
     * @return: com.zqn.modeldata2.common.R<java.lang.String>
     * <AUTHOR> Yang
     * @date: 2024/8/21 14:26
     */
    @PostMapping("/addMadeDept")
    public R<String> addMadeDept(@RequestBody MkSorderbarDto mkSorderbarDto) {
        // 调用qdptService的服务方法，根据订单号查询详细表格数据
        Integer result = mbptService.addMadeDept(mkSorderbarDto);
        if (result == 1) {
            return R.success("修改成功");
        } else {
            return R.error("修改失败");
        }
    }

    /**
     * @description: 修改投入组别
     * @param: mkSorderbarDto
     * @return: com.zqn.modeldata2.common.R<java.lang.String>
     * <AUTHOR> Yang
     * @date: 2024/8/21 14:26
     */
    @PostMapping("/editMadeDept")
    public R<String> editMadeDept(@RequestBody MkSorderbarDto mkSorderbarDto) {
        // 调用qdptService的服务方法，根据订单号查询详细表格数据
        Integer result = mbptService.editMadeDept(mkSorderbarDto);
        if (result == 1) {
            return R.success("修改成功");
        } else {
            return R.error("修改失败");
        }
    }

    /**
     * @description: 查看鞋面投入扫描SQL
     * @param: ordNo
     * @return: com.zqn.modeldata2.common.R<java.util.List < com.zqn.modeldata2.entity.MkSorderbarDto>>
     * <AUTHOR> Yang
     * @date: 2024/8/21 14:26
     */
    @GetMapping("/queryMkSorderbarDtoByOrdNo")
    public R<List<MkSorderbarDto>> queryMkSorderbarDtoByOrdNo(@RequestParam(value = "ordNo", required = true) String ordNo) {
        // 调用qdptService的服务方法，
        return R.success(mbptService.queryMkSorderbarDtoByOrdNo(ordNo));
    }

    /**
     * @description: 发外投入
     * @param: mkSorderbarDto
     * @return: com.zqn.modeldata2.common.R<java.lang.String>
     * <AUTHOR> Yang
     * @date: 2024/8/21 15:01
     */
    @PostMapping("/fwtr")
    public R<String> fwtr(@RequestBody MkSorderbarDto mkSorderbarDto) {
        // 调用qdptService的服务方法，根据订单号查询详细表格数据
        String result = mbptService.fwtr(mkSorderbarDto);
        return R.success(result);
    }

    /**
     * @description: 发外产出
     * @param: mkSorderbarDto
     * @return: com.zqn.modeldata2.common.R<java.lang.String>
     * <AUTHOR> Yang
     * @date: 2024/8/21 15:01
     */
    @PostMapping("/fwcc")
    public R<String> fwcc(@RequestBody MkSorderbarDto mkSorderbarDto) {
        // 调用qdptService的服务方法，根据订单号查询详细表格数据
        String result = mbptService.fwcc(mkSorderbarDto);
        return R.success(result);
    }

    /**
     * @description: 发外投入和发外产出取消
     * @param: mkSorderbarDto
     * @return: com.zqn.modeldata2.common.R<java.lang.String>
     * <AUTHOR> Yang
     * @date: 2024/8/26 10:33
     */
    @PostMapping("/cancel")
    public R<String> cancel(@RequestBody MkSorderbarDto mkSorderbarDto) {
        // 调用qdptService的服务方法，根据订单号查询详细表格数据
        String result = mbptService.cancel(mkSorderbarDto);
        return R.success(result);
    }
}
