package com.zqn.newmodel.entity;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 新型体开发指令单头部字段
 * @date 2024/8/5 15:11
 */
@Data
public class NewModelTitle {

    //表头
    private String title;

    //series
    private String series;

    //chn_sim
    private String chn_sim;

    //pd_line
    private String pd_line;

    //brand_desc
    private String brand_desc;

    //ord_no
    private String ord_no;

    //ord_seq
    private String ord_seq;

    private String ord_type;

    //PRO_SETS
    private String pro_sets;

    //brand_no
    private String brand_no;

    //ord_qty
    private BigDecimal ord_qty;

    //siz_type
    private String siz_type;

    //----------第一个表格


    //型体编号
    private String model_desc;

    //季节名称
    private String season_desc;

    //样品类型
    private String dev_type;

    //楦头编号
    private String last_no;

    //VR 值为A-B
    private String prov_sets;

    //SKU
    private String sku_no;

    //颜色描述
    private String color_desc;

    //鞋组名称
    private String module_desc;

    //结构
    private String constr;

    //客户订单

    //版师 值为A-B
    private String prox_sets;

    //订单二维码
    private byte[] ord_qr;

    //型体图片
    private byte[] model_pic;

    //------------底部

    //中底材料
    private String mb_mater;

    //插中材料（上）
    private String iu_mater;

    //EVA材料
    private String eva_mater;

    //插中材料（下）
    private String id_mater;

    //飞机做法
    private String air_pra;

    //削插中长度
    private String cins_len;

    //削插中刀轮
    private String dw_tic;

    //上下插中长度
    private String ins_len;

    //定型模具
    private String stampo;

    //马口铁材料
    private String tin_mater;

    //铁芯编号
    private String core_num;

    //铁芯后距
    private String core_dist;

    //中底斜度
    private String fresa;

    //跟高
    private String with_high;

    //中底档案备注
    private String bot_rmk;

    //版师
    private String sole_der;

    //中底削边
    private String mb_trim;

    //中底做法备注
    private String bot_method;

    //面部注意事项
    private String upp_desc;

    //底部注意事项
    private String sol_desc;

    //面部版师
    private String dev_upper;

    //底部版师
    private String dev_sole;

    //业务
    private String dutyer;

}
