package com.zqn.modeldata2.entity;

import lombok.Data;

import java.util.Date;

/**
 * 工序步骤
 */
@Data
public class ProcessStep {
    // 型体编号
    private String model_no;
    // 制程
    private String operation;
    // 主要代码
    private String rtg_code;
    // 工序编号
    private String seq_no;
    // 排序号
    private Double skey;
    // 加工段
    private String wk_group;
    // 工序名称
    private String seq_name;
    // 备注
    private String remark;
    // 添加者
    private String ins_user;
    // 添加时间
    private Date ins_date;
    // 修改者
    private String upd_user;
    // 修改时间
    private Date upd_date;
}
