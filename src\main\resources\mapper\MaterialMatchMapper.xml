<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zqn.modeldata2.mapper.MaterialMatchMapper">

    <select id="query" resultType="com.zqn.modeldata2.entity.MaterialMatch">
        SELECT a.shp_date, a.brand_no, a.dev_type, a.ord_no, a.item_no, a.model_pic, a.dutyer, a.model_no, a.last_no,
        a.wo_date, a.tot_qty, a.upp_flag, a.sole_flag, a.t2_flag, a.t3_flag, a.c3_qty, a.c4_qty
        FROM
        VW_MK2_C_FRONTFITTING a
        <where>
            <if test="brand != null and brand != ''">
                AND UPPER(a.brand_no) = UPPER(#{brand})
            </if>
            <if test="devType != null and devType != '' and devType != 'select'">
                AND a.dev_type = #{devType}
            </if>
            <if test="startTime != null and endTime != null">
                and a.shp_date between #{startTime} and #{endTime}
            </if>
            <if test="fileType != null and fileType == 1">
                and a.c_f_status is not null and a.u_f_status is not null
            </if>
        </where>
        and (a.cin_flag is null or a.cin_flag = 'N')
        order by a.shp_date
    </select>

    <select id="queryAllDevType" resultType="java.lang.String">
        select dev_type
        FROM VW_MK2_C_FRONTFITTING
        group by dev_type
    </select>

    <!--   排入生产
    当用户点击投入按钮时-->
    <update id="update">
        UPDATE gc_sorders
        SET cin_flag = DECODE(NVL(cin_flag, 'N'), 'N', 'Y', 'N'),
            cin_date = sysdate,
            cin_area = '' --此处用户说先不用赋值，后续会用
        WHERE ord_no = #{ordNo}
          and item_no = #{itemNo}
    </update>

    <select id="queryDetailTableData" resultType="com.zqn.modeldata2.entity.MaterialMatchDt">
        --ord_no 樣品單號,remark 序號, bar_date 條碼日期,semi_su 製程部位,key_flag 必掃註記,
        --made_dept 製作組別,emp_name  製作人員 ,bar_qty 條碼雙數 ,ins_user 建立人,ins_date 建立日期
        SELECT mat_type,--倉位
               sh_date,--出貨日期
               ord_no,--樣品單號
               brand_no,--品牌簡稱
               season_no,--季節序號
               phase,--階段
               dev_type,--樣品類型
               mat_seq,--品牌料號
               mat_desc,--材料描述
               req_qty,--需求數量
               suom,--庫存單位
               bat_qty,--庫存數量
               shelf_pos,--存放架位
               give_cdate,--交貨日期
               str_date --驗收日期
        FROM VSTOREMATSORDER
--         WHERE mat_type='面料倉' AND sh_date>=SYSDATE-60 and
        where ord_no = #{ordNo}
    </select>

    <select id="selectManualClose" resultType="com.zqn.modeldata2.entity.MaterialMatch">
        SELECT ORD_NO, ITEM_NO, SHP_DATE, C_FLAG, C_date
        FROM gc_sorders
        WHERE ord_no = #{ordNo}
    </select>

    <select id="manualClose" statementType="CALLABLE" resultType="string">
        {call pd_mk_close_orders_c(
                #{ord_no, mode=IN, jdbcType=VARCHAR},
                #{item_no, mode=IN, jdbcType=VARCHAR},
                #{o_return, mode=OUT, jdbcType=VARCHAR}
              )}
    </select>
    <select id="queryTotQtyCount" resultType="java.math.BigDecimal">
        SELECT sum(tot_qty)
        FROM VW_MK2_C_FRONTFITTING
        <where>
            <if test="brand != null and brand != ''">
                AND UPPER(brand_no) = UPPER(#{brand})
            </if>
            <if test="devType != null and devType != '' and devType != 'select'">
                AND dev_type = #{devType}
            </if>
            <if test="startTime != null and endTime != null">
                and shp_date between #{startTime} and #{endTime}
            </if>
            <if test="fileType != null and fileType == 1">
                and c_f_status is not null and u_f_status is not null
            </if>
            <if test="fileType != null and fileType == 2">
                and upp_flag = 'OK'
                and sole_flag = 'OK'
                and t1_flag = 'OK'
                and t2_flag = 'OK'
                and t3_flag = 'OK'
            </if>
            <if test="fileType != null and fileType == 3">
                and upp_flag = 'OK'
                and sole_flag = 'OK'
                and t1_flag = 'OK'
                and t2_flag is null
                and t3_flag = 'OK'
            </if>
            <if test="fileType != null and fileType == 4">
                and upp_flag = 'OK'
                and sole_flag = 'OK'
                and t1_flag = 'OK'
                and t2_flag is null
                and t3_flag is null
            </if>
            <if test="cutComplType != null and cutComplType == 1">
                and c3_qty = 'OK'
                and c4_qty = 'OK'
                and t2_flag = 'OK'
                and t3_flag = 'OK'
            </if>
            <if test="cutComplType != null and cutComplType == 2">
                and (c3_qty is null
                or c4_qty is null
                or t2_flag is null
                or t3_flag is null)
            </if>
        </where>
        and (cin_flag is null or cin_flag = 'N')
    </select>

    <select id="queryMaxShDate" resultType="java.util.Date">
        SELECT MAX(give_cdate) as t1_flag
        FROM VSTOREMATSORDER
        <where>
            <if test="ord_no != null and ord_no != ''">
                AND ord_no = #{ord_no}
            </if>
        </where>
    </select>
</mapper>