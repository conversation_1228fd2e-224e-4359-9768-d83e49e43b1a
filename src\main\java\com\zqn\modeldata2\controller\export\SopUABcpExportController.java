package com.zqn.modeldata2.controller.export;

import cn.afterturn.easypoi.cache.ExcelCache;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import com.zqn.modeldata2.entity.sop.SopFlowPicture;
import com.zqn.modeldata2.entity.sop.SopPreview;
import com.zqn.modeldata2.service.SopService;
import com.zqn.sop.util.ExcelExportUtil;
import org.apache.poi.ss.usermodel.PrintSetup;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFDrawing;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;

@Controller
@RequestMapping("/sop/ua/bcpexport")
public class SopUABcpExportController {

    @Autowired
    private SopService sopService;

    @Value("${uploadUrl}")
    private String TARGET_FOLDER;

    public static byte[] compressImage(String inputImagePath) {
        try {
            // 读取图像
            BufferedImage bufferImg = ImageIO.read(new File(inputImagePath));

            // 获取图片格式
            String formatName = inputImagePath.substring(inputImagePath.lastIndexOf(".") + 1);
            if (formatName.equals("jfif")) {
                formatName = "jpeg";
            }

            // 获取图片写入器
            Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName(formatName);
            if (!writers.hasNext()) throw new IllegalStateException("No writers found");
            ImageWriter writer = writers.next();

            // 设置输出流
            ByteArrayOutputStream byteArrayOut = new ByteArrayOutputStream();
            ImageOutputStream ios = ImageIO.createImageOutputStream(byteArrayOut);
            writer.setOutput(ios);

            // 设置压缩参数
            ImageWriteParam param = writer.getDefaultWriteParam();
            if (param.canWriteCompressed()) {
                param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
                param.setCompressionQuality(0.2f); // 这里设置压缩质量
            }

            // 写入图像
            writer.write(null, new IIOImage(bufferImg, null, null), param);

            // 关闭流
            ios.close();
            writer.dispose();

            return byteArrayOut.toByteArray();

        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 一对多，设置行高
     */
    private static void setRowHeight(Workbook workbook) {
        Sheet sheet = workbook.getSheetAt(0);
        //设置第4列的列宽为60（下标从0开始），TestExportSub2Vo 不知道为什么设置了列宽但是不起作用，只能在这里单独设置
//        sheet.setColumnWidth(3,60*256);
        for (int i = 0; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            int j = i / 9;
            if (i == 3 + (j * 9) || i == 5 + (j * 9)) {
                //设置其他行的行高根据内容自适应
                row.setHeightInPoints(70);
            } else if (i == 5 + (j * 9) || i == 7 + (j * 9)) {
                //设置其他行的行高根据内容自适应
                row.setHeightInPoints(70);
            } else {
                //设置第二行的行高（表格表头）
                row.setHeightInPoints(50);
            }
        }
    }

    private static void setRowHeight(Row row) {
        //根据内容长度设置行高
        int enterCnt = 0;
        for (int j = 0; j < row.getPhysicalNumberOfCells(); j++) {
            if (j < 7) {
                continue;
            }
            int rwsTemp = row.getCell(j).toString().length();
            //这里取每一行中的每一列字符长度最大的那一列的字符
            if (rwsTemp > enterCnt) {
                enterCnt = rwsTemp;
            }
        }
        //设置默认行高为50
        row.setHeightInPoints(50);
        //如果字符长度大于20，判断大了多少倍，根据倍数来设置相应的行高
        if (enterCnt > 15) {
            float d = (float) enterCnt / 15;
            if (d > 1.0f) {
                d = d * 1.0f;
            }
            float f = 50 * d;
            /*if (d>2 && d<4){
                f = 35*2;
            }else if(d>=4 && d<6){
                f = 35*3;
            }else if (d>=6 && d<8){
                f = 35*4;
            }*/
            row.setHeightInPoints(f);
        }
    }

    @GetMapping("/excel")
    public void exportOperationProcess(
            @RequestParam(value = "model") String model,
            @RequestParam(value = "operation") String operation,
            @RequestParam(value = "rtgCode") String rtgCode,
            HttpServletResponse response
    ) throws IOException {
        Map<String, String> operationMap = new HashMap<>();
        operationMap.put("1", "加工");
        operationMap.put("4", "鞋面");
        operationMap.put("5", "半成品");
        operationMap.put("6", "成型");

        // 模板路径
        String filePath = "static/excel/ua/bcp.xlsx";
        Workbook tempWorkbook = ExcelCache.getWorkbook(filePath, new Integer[]{0, 1}, false);
        List<SopPreview> previewList = sopService.getPreviewList(model, operation, rtgCode);

        Map<Integer, List<Map<String, Object>>> resultMap = new TreeMap<>();
        List<Map<String, Object>> mapList1 = new ArrayList<>();
        List<Map<String, Object>> mapList2 = new ArrayList<>();

        //sheet1每8条复制一次，记录需要复制几个，多出来的全部删除
        int pageSize = 8;
        int deleteCount = (previewList.size() + pageSize - 1) / pageSize;
        for (int sheetIndex = previewList.size() - 1; sheetIndex >= 0; sheetIndex--) {
            SopPreview sopPreview = previewList.get(sheetIndex);

            Map<String, Object> map = new TreeMap<>();
            map.put("item", sopPreview.getSkey());
            map.put("model_no", model);
            map.put("actions", sopPreview.getActions());
            map.put("op_std", sopPreview.getStandard());
            map.put("tools", sopPreview.getTools());
            map.put("chemical_substance", sopPreview.getChemical());
            map.put("machine", sopPreview.getMachine());
            map.put("temp", sopPreview.getTemp());
            map.put("pressure", sopPreview.getPressure());
            map.put("time", sopPreview.getTime());
            map.put("self_check_points", sopPreview.getCheckPoint());
            map.put("createBy", sopPreview.getTab());
            map.put("empty", "");

            List<Map<String, Object>> list = new ArrayList<>();
            if (sheetIndex <= deleteCount) {
                for (int i = 0; i < previewList.size(); i++) {
                    if (i >= sheetIndex * pageSize && i <= ((sheetIndex + 1) * pageSize) - 1) {
                        SopPreview sopPreview1 = previewList.get(i);
                        String opStd = "";
                        HashMap<String, Object> hashMap = new HashMap<>();
                        hashMap.put("item", sopPreview1.getSkey());
                        hashMap.put("actions", sopPreview1.getActions());
                        hashMap.put("chemical_substance", sopPreview1.getChemical() != null ? sopPreview1.getChemical().trim() : "");
                        hashMap.put("empty", "");
                        hashMap.put("temp", sopPreview1.getTemp());
                        hashMap.put("pressure", sopPreview1.getPressure());
                        hashMap.put("time", sopPreview1.getTime());
                        if (sopPreview1.getTools() != null) {
                            opStd += sopPreview1.getTools();
                        }
                        if (sopPreview1.getMachine() != null) {
                            opStd += sopPreview1.getMachine();
                        }
                        if (sopPreview1.getStandard() != null) {
                            opStd += sopPreview1.getStandard();
                        }
                        hashMap.put("op_std", opStd.trim());
                        list.add(hashMap);
                    }
                }
            }
            map.put("list", list);
            //sheet3需要根据分页来决定复制几次
            if (mapList1.size() < deleteCount && list.size() > 0) {
                mapList1.add(map);
            }
            mapList2.add(map);
        }
        // 将 mapList 加入到 resultMap 中
        resultMap.put(0, mapList1); // sheet1
        resultMap.put(1, mapList2); // sheet2

        TemplateExportParams params = new TemplateExportParams(filePath, true);
        params.setTemplateWb(tempWorkbook);
        XSSFWorkbook workbook = (XSSFWorkbook) ExcelExportUtil.exportExcelClone(resultMap, params);

        // 重新设置 sheet 名称，确保名称唯一
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            if (i < deleteCount) {
                settingPrintSetup(workbook.getSheetAt(i), (short) 80);
                workbook.setSheetName(i, "半成品梗概 " + (i + 1));
            } else {
                settingPrintSetup(workbook.getSheetAt(i), (short) 70);
                workbook.setSheetName(i, "半成品流程 (" + (i + 1 - deleteCount) + ")");
            }
        }

        for (int pageIndex = 0; pageIndex < deleteCount; pageIndex++) {
            for (int imageIndex = pageSize * pageIndex; imageIndex < Math.min(pageSize * (pageIndex + 1), previewList.size()); imageIndex++) {
                SopPreview sopPreview = previewList.get(imageIndex);
                List<SopFlowPicture> assetsPicture = sopPreview.getImgList();

                if (!CollectionUtils.isEmpty(assetsPicture)) {
                    List<byte[]> imageList = new ArrayList<>();

                    for (SopFlowPicture sopFlowPicture : assetsPicture) {
                        String picturePath = TARGET_FOLDER + sopFlowPicture.getImgUrl();
                        byte[] compressedImage = compressImage(picturePath);
                        imageList.add(compressedImage);
                    }

                    XSSFSheet sheet = (XSSFSheet) workbook.getSheet("半成品梗概 " + (pageIndex + 1));
                    XSSFDrawing patriarch = sheet.createDrawingPatriarch();
                    insertImages(patriarch, workbook, imageList, imageIndex % pageSize + 5, 50, 1, 3);
                }
            }
        }

        // 插入图片
        for (int imageIndex = 0; imageIndex < previewList.size(); imageIndex++) {
            SopPreview sopPreview = previewList.get(imageIndex);
            List<SopFlowPicture> assetsPicture = sopPreview.getImgList();
            if (!CollectionUtils.isEmpty(assetsPicture)) {
                List<byte[]> type1Images = new ArrayList<>();

                for (SopFlowPicture sopFlowPicture : assetsPicture) {
                    String picturePath = TARGET_FOLDER + sopFlowPicture.getImgUrl();
                    byte[] compressedImage = compressImage(picturePath);
                    type1Images.add(compressedImage);
                }

                // 插入图片到红框
                XSSFSheet sheet = (XSSFSheet) workbook.getSheet("半成品流程 (" + (imageIndex + 1) + ")");
                XSSFDrawing patriarch = sheet.createDrawingPatriarch();
                //根据图片数量判读单元格坐标
                insertImagesInGrid(patriarch, workbook, type1Images, 6, 1, 9, 15, 2, 3);
            }
        }

        String fileName = model + operationMap.get(operation) + rtgCode + ".xlsx";
        response.reset();
        response.setContentType("application/octet-stream;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
        response.setHeader("Access-Control-Allow-Origin", "*"); // 或者指定具体的域名
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With");
        try {
            workbook.write(response.getOutputStream());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                workbook.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private void insertImages(XSSFDrawing patriarch, XSSFWorkbook workbook, List<byte[]> imageBytesList, int startRow, int startCol, int rowHeight, int colWidth) {
        for (int i = 0; i < imageBytesList.size(); i++) {
            int col1 = startCol + i * colWidth;
            int row1 = startRow;
            int col2 = col1 + colWidth;
            int row2 = row1 + rowHeight;
            byte[] imageBytes = imageBytesList.get(i);
            XSSFClientAnchor anchor = new XSSFClientAnchor(0, 0, 0, 0, col1, row1, col2, row2);
            patriarch.createPicture(anchor, workbook.addPicture(imageBytes, XSSFWorkbook.PICTURE_TYPE_PNG));
        }
    }

    private void insertImagesInGrid(XSSFDrawing patriarch, XSSFWorkbook workbook, List<byte[]> imageBytesList, int startRow, int startCol, int endRow, int endCol, int rowHeight, int colWidth) {
        int numRows = 2; // 网格行数
        int numCols = 5; // 网格列数
        for (int i = 0; i < numRows; i++) {
            for (int j = 0; j < numCols; j++) {
                int index = i * numCols + j;
                if (index >= imageBytesList.size()) {
                    return; // 如果图片数量少于网格单元格数量，提前返回
                }
                byte[] imageBytes = imageBytesList.get(index);
                int row1 = startRow + i * rowHeight;
                int col1 = startCol + j * colWidth;
                int row2 = row1 + rowHeight;
                int col2 = col1 + colWidth;

                // 创建锚点
                XSSFClientAnchor anchor = new XSSFClientAnchor(0, 0, 0, 0, (short) col1, row1, (short) col2, row2);
                patriarch.createPicture(anchor, workbook.addPicture(imageBytes, XSSFWorkbook.PICTURE_TYPE_PNG));
            }
        }
    }

    // Copy print setup settings from source sheet to target sheet
    private void settingPrintSetup(Sheet targetSheet, short scale) {
        PrintSetup targetPrintSetup = targetSheet.getPrintSetup();
        targetPrintSetup.setPaperSize((short) 9);
        targetPrintSetup.setScale(scale);
        targetPrintSetup.setLandscape(true);
    }
}
