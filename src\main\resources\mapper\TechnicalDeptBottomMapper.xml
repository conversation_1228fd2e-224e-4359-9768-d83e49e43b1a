<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zqn.analysis.mapper.TechnicalDeptBottomMapper">

    <select id="query" resultType="com.zqn.analysis.entity.TechnicalDept">
        --技术科总人数
        SELECT COUNT(1) AS technicalTotalNum
        FROM GETHRDBPSNACCOUNT
        WHERE (layer3 = '技術科' OR department LIKE '%技術部%')
--           AND NOT (layer3 IS NULL AND layer4 IS NULL)
          and (LAYER4 LIKE '%底%' OR layer3 LIKE '%底%')
          AND employmentstatus = 'Formal(正式)'
          AND factory IN
              ('東莞興雄', '廣寧廠', '太平廠', '海防廠', '雙峰興昂', '孟加拉(BSL)', '孟加拉(BFL)', '孟加拉廠',
               '印尼一廠', '菲律賓一廠', '印尼二廠')
    </select>

    <!--    每个工厂的人数-->
    <select id="queryQtyByFactory" resultType="com.zqn.analysis.entity.TechnicalDeptFactoryQty">
        SELECT
          fl.factory,
          NVL(COUNT(g.factory), 0) AS bottomQty
        FROM (
          SELECT '東莞興雄' AS factory FROM DUAL UNION ALL
          SELECT '廣寧廠' FROM DUAL UNION ALL
          SELECT '太平廠' FROM DUAL UNION ALL
          SELECT '海防廠' FROM DUAL UNION ALL
          SELECT '雙峰興昂' FROM DUAL UNION ALL
          SELECT '孟加拉(BSL)' FROM DUAL UNION ALL
          SELECT '孟加拉(BFL)' FROM DUAL UNION ALL
          SELECT '孟加拉廠' FROM DUAL UNION ALL
          SELECT '印尼一廠' FROM DUAL UNION ALL
          SELECT '菲律賓一廠' FROM DUAL UNION ALL
          SELECT '印尼二廠' FROM DUAL
        ) fl
        LEFT JOIN GETHRDBPSNACCOUNT g
          ON fl.factory = g.factory
          AND (g.layer3 = '技術科' OR g.department LIKE '%技術部%')
          AND (g.LAYER4 LIKE '%底%' OR g.layer3 LIKE '%底%')
          AND g.employmentstatus != 'Dimission(離職)'
          AND g.JOBTITLE LIKE '%技師%'
        GROUP BY fl.factory
        ORDER BY fl.factory
    </select>

    <select id="queryTechnicalDeptDetail" resultType="com.zqn.analysis.entity.TechnicalDept">
        select factory,
               employeeid,
               legalname,
               joblevel,
               jobtitle,
               department,
               layer1,
               layer2,
               layer3,
               layer4
        from GETHRDBPSNACCOUNT
        where (layer3 = '技術科' or department like '%技術部%')
          and employmentstatus = 'Formal(正式)'
          and factory in ('東莞興雄', '廣寧廠', '太平廠', '海防廠', '雙峰興昂', '孟加拉(BSL)', '孟')
    </select>

    <!--    当日客户数-->
    <select id="queryBrandQty" resultType="com.zqn.analysis.entity.TechnicalDept">
        SELECT COUNT(DISTINCT brand_no) AS todayCustomerNum
        FROM (SELECT DISTINCT x.brand_no
              FROM (SELECT DISTINCT b.brand_no, b.model_no
                    FROM mk_sorderbar a,
                         gc_sorder b,
                         be_brand c,
                         vck_smodel d,
                         (SELECT a.model_no, b.mold_no
                          FROM bg_moldd a,
                               bg_mold b
                          WHERE a.mold_seq = b.mold_seq
                            AND b.mold_type = 'A') e
                    WHERE a.semi_no = 'W'
                      AND a.TYPE = 'I'
                      AND a.ord_no = b.ord_no
                      AND b.brand_no = c.brand_no
                      AND b.model_no = d.model_no
                      AND TRUNC(a.bar_date) = TRUNC(SYSDATE)
                      AND b.model_no = e.model_no) x,
                   vck_smodel y
              WHERE x.model_no = y.model_no)
    </select>

    <!-- 当日开版型体数量   -->
    <select id="queryModelNoQty" resultType="com.zqn.analysis.entity.TechnicalDept">
        SELECT COUNT(DISTINCT MODEL_NO) AS todayModelNum
        FROM (SELECT DISTINCT x.model_no
              FROM (SELECT DISTINCT b.brand_no, b.model_no
                    FROM mk_sorderbar a,
                         gc_sorder b,
                         be_brand c,
                         vck_smodel d,
                         (SELECT a.model_no, b.mold_no
                          FROM bg_moldd a,
                               bg_mold b
                          WHERE a.mold_seq = b.mold_seq
                            AND b.mold_type = 'A') e
                    WHERE a.semi_no = 'W'
                      AND a.TYPE = 'I'
                      AND a.ord_no = b.ord_no
                      AND b.brand_no = c.brand_no
                      AND b.model_no = d.model_no
                      AND TRUNC(a.bar_date) = TRUNC(SYSDATE)
                      AND b.model_no = e.model_no) x,
                   vck_smodel y
              WHERE x.model_no = y.model_no)
    </select>

    <!--  抓取版師收到派工后兩天還未開版的數據-->
    <select id="queryModelList" resultType="com.zqn.analysis.entity.TechnicalDeptModelList">
        <![CDATA[
        SELECT x.brand_no, x.model_no
        FROM (SELECT DISTINCT b.brand_no, b.model_no
              FROM mk_sorderbar a,
                   gc_sorder b,
                   be_brand c,
                   vck_smodel d,
                   (SELECT a.model_no, b.mold_no
                    FROM bg_moldd a,
                         bg_mold b
                    WHERE a.mold_seq = b.mold_seq
                      AND b.mold_type = 'A') e
              WHERE a.semi_no = 'W'
                AND a.TYPE = 'I'
                AND a.ord_no = b.ord_no
                AND b.brand_no = c.brand_no
                AND b.model_no = d.model_no
                AND to_char(a.bar_date, 'yyyymm') = to_char(sysdate, 'yyyymm')
                AND b.model_no = e.model_no
                AND NOT EXISTS (SELECT NULL
                                FROM mk_sorderbar
                                WHERE ord_no = b.ord_no
                                  AND semi_no = 'W'
                                  AND TYPE = 'O'
                                  AND TRUNC(bar_date) < TRUNC(a.bar_date) + 2)) x
        ]]>
    </select>

    <select id="queryPeopleDetail" resultType="java.util.Map">
        SELECT legalname, englishname, employmentstatus, joblevel, jobtitle, factory, division, department, 
               organizationcode, organizationname, layer1, layer2, layer3, layer4, layer5
        FROM GETHRDBPSNACCOUNT
        WHERE (layer3 = '技術科' or department LIKE '%技術部%')
          and (LAYER4 LIKE '%底%' OR layer3 LIKE '%底%')
          AND employmentstatus != 'Dimission(離職)'
          and JOBTITLE like '%技師%'
          AND factory IN
              ('東莞興雄', '廣寧廠', '太平廠', '海防廠', '雙峰興昂', '孟加拉(BSL)', '孟加拉(BFL)', '孟加拉廠',
               '印尼一廠', '菲律賓一廠', '印尼二廠')
          AND factory = #{factory}
    </select>

</mapper>