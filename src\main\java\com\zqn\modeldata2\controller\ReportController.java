package com.zqn.modeldata2.controller;

import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.entity.CkSmodelp;
import com.zqn.modeldata2.entity.Sign;
import com.zqn.modeldata2.entity.SignFile;
import com.zqn.modeldata2.service.ReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/report")
public class ReportController {
    @Autowired
    private ReportService reportService;

    @PostMapping("/getPicture")
    public R<Map<String, Object>> getPicture(@RequestBody Map<String, Object> params) {
        String model_no = params.get("model_no").toString();
        Map<String, Object> result = reportService.getPicture(model_no);
        return R.success(result);
    }

    @PostMapping("/getInfo")
    public R<Map<String, Object>> getInfo(@RequestBody Map<String, Object> params) {
        String model_no = params.get("model_no").toString();
        Map<String, Object> result = reportService.getInfo(model_no);
        return R.success(result);
    }

    @PostMapping("/getTitle")
    public R<List<Object>> getTitle(@RequestBody Map<String, Object> params) {
        String model_no = params.get("model_no").toString();
        String siz_type = params.get("siz_type").toString();
        double bas_size = (params.get("bas_size") != null && params.get("bas_size").toString().length() > 0) ? Double.parseDouble(params.get("bas_size").toString()) : 0;
        List<Object> result = reportService.getTitle(model_no, siz_type, bas_size);
        return R.success(result);
    }

    @PostMapping("/getFullSize")
    public R<Boolean> getFullSize(@RequestBody Map<String, Object> params) {
        String model_no = params.get("model_no").toString();
        Boolean result = reportService.getFullSize(model_no);
        return R.success(result);
    }

    @PostMapping("/updateFullSize")
    public R<String> updateFullSize(@RequestBody Map<String, Object> params) {
        String model_no = params.get("model_no").toString();
        Boolean full_size = (Boolean) params.get("full_size");
        Integer result = reportService.updateFullSize(model_no, full_size);
        if (result < 0) {
            return R.error("切换失败！");
        }
        return R.success("切换成功！");
    }

    @PostMapping("/getCount1")
    public R<List<Object>> getCount1(@RequestBody Map<String, Object> params) {
        String model_no = params.get("model_no").toString();
        String siz_type = params.get("siz_type").toString();
        double bas_size = (params.get("bas_size") != null && params.get("bas_size").toString().length() > 0) ? Double.parseDouble(params.get("bas_size").toString()) : 0;
        int procs_type = Integer.parseInt(params.get("procs_type").toString());
        List<Object> result = reportService.getCount1(model_no, siz_type, bas_size, procs_type);
        return R.success(result);
    }

    @PostMapping("/getCount2")
    public R<List<Object>> getCount2(@RequestBody Map<String, Object> params) {
        String model_no = params.get("model_no").toString();
        String siz_type = params.get("siz_type").toString();
        double bas_size = (params.get("bas_size") != null && params.get("bas_size").toString().length() > 0) ? Double.parseDouble(params.get("bas_size").toString()) : 0;
        int procs_type = Integer.parseInt(params.get("procs_type").toString());
        List<Object> result = reportService.getCount2(model_no, siz_type, bas_size, procs_type);
        return R.success(result);
    }

    @PostMapping("/getInsertInfo")
    public R<CkSmodelp> getInsertInfo(@RequestBody Map<String, Object> params) {
        String model_no = params.get("model_no").toString();
        CkSmodelp result = reportService.getInsertInfo(model_no);
        return R.success(result);
    }

    @PostMapping("getSignature")
    public R<Sign> getSignature(@RequestBody Sign sign) {
        Sign result = reportService.getSignature(sign);
        return R.success(result);
    }

    @PostMapping("/saveChkSignature")
    public R<String> saveChkSignature(@RequestBody Sign sign) {
        if (sign.getUser_id() != null && sign.getUser_id().length() > 3) {
            sign.setUser_id(sign.getUser_id().substring(3));
        } else {
            return R.error("核准签名保存失败！");
        }
        Integer result = reportService.saveChkSignature(sign);
        if (result < 0) {
            return R.error("核准签名保存失败！");
        }
        return R.success("核准签名保存成功！");
    }

    @PostMapping("/saveUppSignature")
    public R<String> saveUppSignature(@RequestBody Sign sign) {
        if (sign.getUser_id() != null && sign.getUser_id().length() > 3) {
            sign.setUser_id(sign.getUser_id().substring(3));
        } else {
            return R.error("核准签名保存失败！");
        }
        Integer result = reportService.saveUppSignature(sign);
        if (result < 0) {
            return R.error("面版师签名保存失败！");
        }
        return R.success("面版师签名保存成功！");
    }

    @PostMapping("/saveSolSignature")
    public R<String> saveSolSignature(@RequestBody Sign sign) {
        if (sign.getUser_id() != null && sign.getUser_id().length() > 3) {
            sign.setUser_id(sign.getUser_id().substring(3));
        } else {
            return R.error("核准签名保存失败！");
        }
        Integer result = reportService.saveSolSignature(sign);
        if (result < 0) {
            return R.error("底版师签名保存失败！");
        }
        return R.success("底版师签名保存成功！");
    }

    @PostMapping("/resetChkSignature")
    public R<String> resetChkSignature(@RequestBody SignFile signFile) {
        Integer result = reportService.resetChkSignature(signFile);
        if (result < 0) {
            return R.error("核准签名档重置失败！");
        }
        return R.success("核准签名档重置成功！");
    }

    @PostMapping("/resetUppSignature")
    public R<String> resetUppSignature(@RequestBody SignFile signFile) {
        Integer result = reportService.resetUppSignature(signFile);
        if (result < 0) {
            return R.error("面版师签名档重置失败！");
        }
        return R.success("面版师签名档重置成功！");
    }

    @PostMapping("/resetSolSignature")
    public R<String> resetSolSignature(@RequestBody SignFile signFile) {
        Integer result = reportService.resetSolSignature(signFile);
        if (result < 0) {
            return R.error("底版师签名档重置失败！");
        }
        return R.success("底版师签名档重置成功！");
    }
}
