package com.zqn.prodctrl.mapper;

import com.zqn.prodctrl.entity.SchedProd;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SchedProdMapper {
    List<SchedProd> query(@Param("startTime") String startTime,
                          @Param("endTime") String endTime,
                          @Param("brand") String brand,
                          @Param("ordNo") String ordNo,
                          @Param("devType") String devType,
                          @Param("pFlag") String pFlag,
                          @Param("department") String department,
                          @Param("phase") String phase,
                          @Param("pdLine") String pdLine
                          );

    SchedProd queryCount(@Param("startTime") String startTime,
                         @Param("endTime") String endTime,
                         @Param("brand") String brand,
                         @Param("ordNo") String ordNo,
                         @Param("devType") String devType,
                         @Param("pFlag") String pFlag,
                          @Param("department") String department,
                         @Param("phase") String phase,
                         @Param("pdLine") String pdLine
                         );

    List<String> queryAllDevType();

    void edit(@Param("ordNo") String ordNo, @Param("itemNo") String itemNo);
}