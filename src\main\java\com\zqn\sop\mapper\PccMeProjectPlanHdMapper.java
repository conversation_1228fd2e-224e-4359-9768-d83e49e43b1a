package com.zqn.sop.mapper;

import com.zqn.sop.entity.PccMeProjectPlanHd;
import com.zqn.sop.vo.PccMeProjectPlanDtVo;
import com.zqn.sop.vo.PccMeProjectPlanHdVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PccMeProjectPlanHdMapper {

    List<PccMeProjectPlanHd> query(@Param("model") String model, @Param("dept") String dept,
                                   @Param("brand")String brand, @Param("loginUser") String loginUser,
                                   @Param("userFactory") String userFactory, @Param("selectFactory") String selectFactory,
                                   @Param("hasAudit") Boolean hasAudit);

    int save(@Param("planHd") PccMeProjectPlanHd planHd);

    List<PccMeProjectPlanHd> queryModel(@Param("model") String model);

    List<String> queryShoeLastByModelNo(@Param("modelNo") String model, @Param("brand") String brand);

    void deleteContent(@Param("hdId") Integer hdId);

    void deleteDt(@Param("hdId") Integer hdId);

    void deleteImgs(@Param("hdId") Integer hdId);

    void deleteHd(@Param("hdId") Integer hdId);

    void copyDt(@Param("parentId") Integer parentId, @Param("oldId") Integer id);

    List<PccMeProjectPlanDtVo> queryDt(Integer id);

    int insertCont(@Param("model") String model,
                   @Param("itemNo") Integer itemNo,
                   @Param("version") Integer version,
                   @Param("dept") String sourceDept,
                   @Param("id") Integer id,
                   @Param("factory") String factory);

    int insertImg(@Param("model") String model,
                  @Param("itemNo") Integer itemNo,
                  @Param("version") Integer version,
                  @Param("dept") String sourceDept,
                  @Param("id") Integer id,
                  @Param("factory") String factory);

    List<String> queryAllPlanFactory();

    Integer audit(@Param("id") Integer id);

    Integer revAudit(@Param("id") Integer id);

    PccMeProjectPlanHdVo queryById(@Param("id") Integer id);
}