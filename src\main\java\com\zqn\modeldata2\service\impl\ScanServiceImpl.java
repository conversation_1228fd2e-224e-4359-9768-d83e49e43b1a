package com.zqn.modeldata2.service.impl;

import com.zqn.modeldata2.entity.ColumnMap;
import com.zqn.modeldata2.entity.MkSlastbar;
import com.zqn.modeldata2.entity.MkSlastbarPlus;
import com.zqn.modeldata2.mapper.ScanMapper;
import com.zqn.modeldata2.service.ScanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ScanServiceImpl implements ScanService {
    @Autowired
    private ScanMapper scanMapper;

    @Override
    public Map<String, Object> getCount(String user_id, String ins_user, String cl_flag, boolean today) {
        return scanMapper.getCount(user_id, ins_user, cl_flag, today);
    }

    @Override
    public List<MkSlastbarPlus> getScanException(String user_id, String ins_user, String cl_flag, boolean today, int page_no, int page_size) {
        List<MkSlastbarPlus> result = scanMapper.getScanException(user_id, ins_user, cl_flag, today, page_no, page_size);

        List<ColumnMap> deptList = scanMapper.getDept();
        Map<String, String> deptMap = new HashMap<>();
        for (ColumnMap item : deptList) {
            deptMap.put(item.getColumn_seq(), item.getColumn_no());
        }

        List<ColumnMap> descList = scanMapper.getDesc();
        Map<String, String> descMap = new HashMap<>();
        for (ColumnMap item : descList) {
            descMap.put(item.getColumn_seq(), item.getColumn_no());
        }

        List<ColumnMap> addrList = scanMapper.getAddr();
        Map<String, String> addrMap = new HashMap<>();
        for (ColumnMap item : addrList) {
            addrMap.put(item.getColumn_seq(), item.getColumn_no());
        }

        for (MkSlastbarPlus mkSlastbarPlus : result) {
            mkSlastbarPlus.setPb_dept(mkSlastbarPlus.getPb_dept() + "-" + deptMap.get(mkSlastbarPlus.getPb_dept()));

            mkSlastbarPlus.setPb_desc(mkSlastbarPlus.getPb_desc() + "-" + descMap.get(mkSlastbarPlus.getPb_desc()));

            mkSlastbarPlus.setPb_addr(mkSlastbarPlus.getPb_addr() + "-" + addrMap.get(mkSlastbarPlus.getPb_addr()));
        }

        return result;
    }

    @Override
    public Map<String, Object> getBarCodeA(String ord_no) {
        Map<String, Object> map = scanMapper.getBarCodeA(ord_no);
        if (map != null) {
            map.putIfAbsent("pb_qty", 0);
        }
        return map;
    }

    @Override
    public Map<String, Object> getBarCodeB(String column_seq) {
        return scanMapper.getBarCodeB(column_seq);
    }

    @Override
    public Map<String, Object> getBarCodeC(String column_seq) {
        return scanMapper.getBarCodeC(column_seq);
    }

    @Override
    public Map<String, Object> getBarCodeD(String column_seq) {
        return scanMapper.getBarCodeD(column_seq);
    }

    @Override
    public List<Object> getOption(MkSlastbar mkSlastbar) {
        List<ColumnMap> list = scanMapper.getOption(mkSlastbar);
        List<Object> result = new ArrayList<>();
        for (ColumnMap columnMap : list) {
            Map<String, Object> map = new HashMap<>();
            map.put("text", columnMap.getColumn_no());
            map.put("value", columnMap.getColumn_no());
            result.add(map);
        }
        return result;
    }

    @Override
    public int addException(MkSlastbar mkSlastbar) {
        int result = -1;
        try {
            result = scanMapper.addException(mkSlastbar);
//            result = 1 / 0;
        } catch (Exception e) {
            return result;
        }
        return result;
    }

    @Override
    public int updateException(MkSlastbar mkSlastbar) {
        int result = -1;
        try {
            result = scanMapper.updateException(mkSlastbar);
//            result = 1 / 0;
        } catch (Exception e) {
            return result;
        }
        return result;
    }

    @Override
    public int updateMethod(MkSlastbar mkSlastbar) {
        int result = -1;
        try {
            result = scanMapper.updateMethod(mkSlastbar);
//            result = 1 / 0;
        } catch (Exception e) {
            return result;
        }
        return result;
    }

    @Override
    public int updateState(MkSlastbar mkSlastbar) {
        int result = -1;
        try {
            result = scanMapper.updateState(mkSlastbar);
//            result = 1 / 0;
        } catch (Exception e) {
            return result;
        }
        return result;
    }

    @Override
    public int deleteException(MkSlastbar mkSlastbar) {
        int result = -1;
        try {
            result = scanMapper.deleteException(mkSlastbar);
//            result = 1 / 0;
        } catch (Exception e) {
            return result;
        }
        return result;
    }
}
