package com.zqn.factorymanager.mapper;

import com.zqn.factorymanager.entity.DevTracking;
import com.zqn.factorymanager.entity.RetryAlert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface RetryAlertMapper {

    List<RetryAlert> queryTop(@Param("startTime") Date startTime,
                              @Param("endTime") Date endTime);

    List<RetryAlert> queryBottom(@Param("startTime") Date startTime,
                                 @Param("endTime") Date endTime);
}