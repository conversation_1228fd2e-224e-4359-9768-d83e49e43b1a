package com.zqn.modeldata2.entity;

import lombok.Data;

import java.util.List;

@Data
public class Menu {

    private Integer menuId;

    /**
     * 菜单编号
     */
    private Integer menuNo;

    /**
     * 菜单名称
     */
    private String menuDesc;

    /**
     * 菜单父级
     */
    private String menuParent;//菜单父级

    /**
     * 用户编号
     */
    private String userNo;

    /**
     * 菜单父级
     */
    private Integer menuParentNo;

    /**
     * 菜单路径
     */
    private String menuUrl;

    private List<Menu> menuList;

    /**
     * 是否选中
     */
    private Boolean checked;

    /**
     * 菜单类型
     * 1.系统菜单
     * 2.用户菜单
     * 3.常用菜单
     */
    private Integer type;

    /**
     * @description: 1为菜单，2为按钮
     * @param: null
     * @return:
     * <AUTHOR>
     * @date: 2024/11/27 13:43
     */
    private Integer menuLevel;
}
