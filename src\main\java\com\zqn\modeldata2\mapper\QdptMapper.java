package com.zqn.modeldata2.mapper;

import com.zqn.modeldata2.entity.Qdpt;
import com.zqn.modeldata2.entity.QdptDt;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Mapper
public interface QdptMapper {

    List<Qdpt> query(@Param("startTime") Date startTime,
                     @Param("endTime") Date endTime,
                     @Param("brand") String brand,
                     @Param("devType") String devType,
                     @Param("fileType") Integer fileType,
                     @Param("cutComplType") Integer cutComplType,
                     @Param("sortType") String sortType);

    Integer update(@Param("ordNo") String ord_no, @Param("itemNo") String itemNo);

    List<String> queryAllDevType();

    List<QdptDt> queryDetailTableData(@Param("ordNo") String ordNo);

    List<Qdpt> selectManualClose(@Param("ordNo") String ordNo);

    void manualClose(Map<String, Object> params);

    BigDecimal queryTotQtyCount(@Param("startTime") Date startTime,
                                @Param("endTime") Date endTime,
                                @Param("brand") String brand,
                                @Param("devType") String devType,
                                @Param("fileType") Integer fileType,
                                @Param("cutComplType") Integer cutComplType);

    void insertLog(@Param("ordNo") String ordNo, @Param("itemNo") String itemNo);

    List<Qdpt> queryLog();

    BigDecimal queryTouRuLogTotQtyCount(@Param("orderNos") List<String> orderNos);
}