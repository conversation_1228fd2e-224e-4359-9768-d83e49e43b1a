package com.zqn.modeldata2.controller;

import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.entity.MkMating;
import com.zqn.modeldata2.entity.MkNpatQcDet;
import com.zqn.modeldata2.entity.MkNpatQcTot;
import com.zqn.modeldata2.service.MatchService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/match")
public class MatchController {
    @Autowired
    private MatchService matchService;

    /**
     * 已配套待配送
     */
    @PostMapping("/getWaitSend")
    public R<List<MkMating>> getWaitSend(@RequestBody Map<String, Object> params) {
        int page_no = params.get("page_no") != null ? Integer.parseInt(params.get("page_no").toString()) : 0;
        int page_size = params.get("page_size") != null ? Integer.parseInt(params.get("page_size").toString()) : 0;
        List<MkMating> result = matchService.getWaitSend(page_no, page_size);
        return R.success(result);
    }

    /**
     * 待配送数量
     */
    @PostMapping("/getSendCount")
    public R<Integer> getSendCount() {
        Integer result = matchService.getSendCount();
        return R.success(result);
    }

    /**
     * 修改配送状态
     */
    @PostMapping("/updateState")
    public R<String> updateState(@RequestBody Map<String, Object> params) {
        String mating_no = params.get("mating_no") != null ? params.get("mating_no").toString() : "";
        String pdLine = params.get("pdLine") != null ? params.get("pdLine").toString() : "";
        String ord_no = params.get("ord_no") != null ? params.get("ord_no").toString() : "";
        int result = matchService.updateState(pdLine, ord_no, mating_no);
        if (result < 0) {
            return R.error("配送状态修改失败！");
        }
        return R.success("配送状态修改成功！");
    }

    /**
     * 已配套待投入
     */
    @PostMapping("/getWaitInput")
    public R<List<MkMating>> getWaitInput(@RequestBody Map<String, Object> params) {
        int page_no = params.get("page_no") != null ? Integer.parseInt(params.get("page_no").toString()) : 0;
        int page_size = params.get("page_size") != null ? Integer.parseInt(params.get("page_size").toString()) : 0;
        int status = params.get("status") != null ? Integer.valueOf(params.get("status").toString()) : 0;
        List<MkMating> result = matchService.getWaitInput(page_no, page_size, status);
        return R.success(result);
    }

    /**
     * 待投入数量
     */
    @PostMapping("/getInputCount")
    public R<Integer> getInputCount(@RequestBody Map<String, Object> params) {
        Integer status = params.get("status") != null ? Integer.valueOf(params.get("status").toString()) : 0;
        Integer result = matchService.getInputCount(status);
        return R.success(result);
    }

    /**
     * 未配套库存查询
     */
    @PostMapping("/getNoMatch")
    public R<List<MkNpatQcTot>> getNoMatch(@RequestBody Map<String, Object> params) {
        Integer status = params.get("status") != null ? Integer.valueOf(params.get("status").toString()) : 0;
        List<MkNpatQcTot> result = matchService.getNoMatch(status);
        return R.success(result);
    }

    /**
     * 未配套库存明细
     */
    @PostMapping("/getNoMatchDetail")
    public R<List<MkNpatQcDet>> getNoMatchDetail(@RequestBody Map<String, Object> params) {
        String brand_no = params.get("brand_no") != null ? params.get("brand_no").toString() : "";
        Integer status = params.get("status") != null ? Integer.valueOf(params.get("status").toString()) : 0;
        List<MkNpatQcDet> result = matchService.getNoMatchDetail(brand_no, status);
        return R.success(result);
    }

    /**
     * 已配套楦头确认
     */
    @PostMapping("/getMatchConfirm")
    public R<List<MkNpatQcDet>> getMatchConfirm(@RequestBody Map<String, Object> params) {
        int page_no = params.get("page_no") != null ? Integer.parseInt(params.get("page_no").toString()) : 1;
        int page_size = params.get("page_size") != null ? Integer.parseInt(params.get("page_size").toString()) : 15;
        Integer status = params.get("status") != null ? Integer.valueOf(params.get("status").toString()) : 0;
        List<MkNpatQcDet> result = matchService.getMatchConfirm(page_no, page_size, status);
        return R.success(result);
    }

    /**
     * 保存
     */
    @PostMapping("/insertConfirmDetail")
    public R<String> insertConfirmDetail(@RequestBody Map<String, Object> params) {

        @SuppressWarnings("unchecked")
        List<String> sizeList = params.get("sizeList") != null ? (List<String>) params.get("sizeList") : null;
        @SuppressWarnings("unchecked")
        List<List<String>> sizeListCot = params.get("sizeListCot") != null ? (List<List<String>>) params.get("sizeListCot") : null;
        String ord_no = params.get("ord_no") != null ? params.get("ord_no").toString() : "";
        String last_no = params.get("last_no") != null ? params.get("last_no").toString() : "";
        @SuppressWarnings("unchecked")
        List<String> width = params.get("width") != null ? (List<String>) params.get("width") : null;
        @SuppressWarnings("unchecked")
        List<String> lr_mark = params.get("lr_mark") != null ? (List<String>) params.get("lr_mark") : null;
        int result = 0;
        for (int i = 0; i < sizeListCot.size(); i++) {
            System.out.println(width.get(i) + "**" + lr_mark.get(i));
            result += matchService.insertConfirmDetail(sizeList, sizeListCot.get(i), ord_no, last_no, width.get(i), lr_mark.get(i));
        }
        if (result < 0) {
            return R.error("楦头保存失败！");
        }
        return R.success("楦头保存成功！");
    }

    /**
     * 已配套楦头确认明细
     */
    @PostMapping("/getMatchConfirmDetail")
    public R<List<Object>> getMatchConfirmDetail(@RequestBody Map<String, Object> params) {
        String ord_No = params.get("ord_No") != null ? params.get("ord_No").toString() : "";
        String last_no = params.get("last_no") != null ? params.get("last_no").toString() : "";
        List<Object> result = matchService.getMatchConfirmMXXX(ord_No, last_no);
        return R.success(result);
    }

    /**
     * 修改楦头状态
     */
    @PostMapping("/updateXuanTouState")
    public R<String> updateXuanTouState(@RequestBody Map<String, Object> params) {
        String mating_no = params.get("mating_no") != null ? params.get("mating_no").toString() : "";
        int result = matchService.updateXuanTouState(mating_no);
        if (result < 0) {
            return R.error("楦头状态修改失败！");
        }
        return R.success("楦头状态修改成功！");
    }

    /**
     * 成型已投入未产出查询
     */
    @PostMapping("/formingQuery")
    public R<List<MkNpatQcDet>> formingQuery(@RequestBody Map<String, Object> params) {
        int page_no = params.get("page_no") != null ? Integer.parseInt(params.get("page_no").toString()) : 1;
        int page_size = params.get("page_size") != null ? Integer.parseInt(params.get("page_size").toString()) : 15;
        Integer status = params.get("status") != null ? Integer.valueOf(params.get("status").toString()) : 1;
        Integer status2 = params.get("status2") != null ? Integer.valueOf(params.get("status2").toString()) : 6;
        List<MkNpatQcDet> result = matchService.formingQuery(page_no, page_size, status, status2);
        return R.success(result);
    }

    /**
     * 成型已投入未产出入数量
     */
    @PostMapping("/getformingCount")
    public R<List<Integer>> getformingCount(@RequestBody Map<String, Object> params) {
        Integer status = params.get("status") != null ? Integer.valueOf(params.get("status").toString()) : 1;
        Integer status2 = params.get("status2") != null ? Integer.valueOf(params.get("status2").toString()) : 6;
        Integer status3 = params.get("status3") != null ? Integer.valueOf(params.get("status3").toString()) : 9;
        List<Integer> result = matchService.getformingCount(status, status2, status3);
        return R.success(result);
    }
}
