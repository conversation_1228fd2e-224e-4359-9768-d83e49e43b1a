package com.zqn.studentrecord.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 学员记录实体类
 */
@Data
public class StudentRecord {
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 记录日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date recordDate;
    
    /**
     * 单号/数据
     */
    private String orderNo;
    
    /**
     * 学员
     */
    private String student;
    
    /**
     * 订单数量
     */
    private Double ordQty;
    
    /**
     * 型体
     */
    private String modelNo;
    
    /**
     * 鞋图路径
     */
    private byte[] shoePic;
    
    /**
     * 实物动作图片(JSON数组)
     */
    private List<String> actionPics;
    
    /**
     * 改善动作图片(JSON数组)
     */
    private List<String> improvePics;
    
    /**
     * 成品图片(JSON数组)
     */
    private List<String> productPics;
    
    /**
     * 实物动作图片JSON字符串(用于数据库存储)
     */
    @JsonIgnore
    private String actionPicsJson;
    
    /**
     * 改善动作图片JSON字符串(用于数据库存储)
     */
    @JsonIgnore
    private String improvePicsJson;
    
    /**
     * 成品图片JSON字符串(用于数据库存储)
     */
    @JsonIgnore
    private String productPicsJson;
    
    /**
     * 学员感想
     */
    private String studentThoughts;
    
    /**
     * 沟通障碍
     */
    private String communicationIssues;
    
    /**
     * 动作学会(false-否,true-是)
     */
    private Boolean learned;
    
    /**
     * 动作学会Integer值(用于数据库存储，0-否,1-是)
     */
    @JsonIgnore
    private Integer learnedInt;
    
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    
    /**
     * 创建用户
     */
    private String createUser;
    
    /**
     * 更新用户
     */
    private String updateUser;
    
    /**
     * 状态(0-删除,1-正常)
     */
    private Integer status;
} 