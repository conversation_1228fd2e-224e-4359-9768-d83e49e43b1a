package com.zqn.sop.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.zqn.sop.entity.PccSopCont;
import com.zqn.sop.mapper.PccSopContMapper;
import com.zqn.sop.service.PccSopContService;
import com.zqn.sop.vo.PccMeProjectPlanDtVo;
import com.zqn.sop.vo.PccMeProjectPlanHdVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@DS("app")
public class PccSopContServiceImpl implements PccSopContService {

    private static final Logger logger = LoggerFactory.getLogger(PccSopContServiceImpl.class);

    @Autowired
    private PccSopContMapper pccSopContMapper;

    @Transactional
    @Override
    public int add(PccMeProjectPlanHdVo vo) throws Exception {
        PccMeProjectPlanDtVo pccMeProjectPlanDts = vo.getPccMeProjectPlanDts();
        Integer parent_id = pccMeProjectPlanDts.getId();
        List<PccSopCont> pccSopConts = vo.getPccSopConts();
        for (PccSopCont pccSopCont : pccSopConts) {
            pccSopCont.setParent_id(parent_id);
            if (pccSopCont.getAction_id() == null) {
                pccSopCont.setAction_id(0);
            }
            pccSopContMapper.add(pccSopCont);
        }
        return 1;
    }

    @Override
    public List<PccSopCont> query(Integer parentId) {
        return pccSopContMapper.query(parentId);
    }
}
