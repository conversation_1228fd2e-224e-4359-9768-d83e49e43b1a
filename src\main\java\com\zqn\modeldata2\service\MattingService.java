package com.zqn.modeldata2.service;

import com.github.pagehelper.PageInfo;
import com.zqn.modeldata2.entity.*;

import java.util.List;

public interface MattingService {


    List<BottomOrder> bottomList(String startTime, String endTime);

    List<BottomOrderItem> getBottomItem(String modelNo, String mline);

    List<Matting> callMatting(String date);

//    PageInfo<Fit> query(int pageNo, int pageSize, String factory, String brandNo, String startTime, String endTime , String cfmFlag, String clFlag);

    List<Fit> query(String factory, String brandNo, String startTime, String endTime , String cfmFlag, String clFlag);

    List<Fit> queryList(String factory, String brandNo, String startTime,  String endTime, String cfmFlag, String clFlag);

    List<DaYuan> queryDayuanList(String brandNo,  String startTime, String endTime);

    List<Brand> queryDayuanBrands();
}