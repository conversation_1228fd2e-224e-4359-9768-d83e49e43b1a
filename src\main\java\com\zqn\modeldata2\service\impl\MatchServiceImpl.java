package com.zqn.modeldata2.service.impl;

import com.zqn.modeldata2.entity.MkMating;
import com.zqn.modeldata2.entity.MkNpatQcDet;
import com.zqn.modeldata2.entity.MkNpatQcTot;
import com.zqn.modeldata2.mapper.MatchMapper;
import com.zqn.modeldata2.service.MatchService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class MatchServiceImpl implements MatchService {
    @Autowired
    private MatchMapper matchMapper;

    /**
     * 已配套待配送
     */
    @Override
    public List<MkMating> getWaitSend(int page_no, int page_size) {
        return matchMapper.getWaitSend(page_no, page_size);
    }

    /**
     * 待配送数量
     */
    @Override
    public Integer getSendCount() {
        return matchMapper.getSendCount();
    }

    /**
     * 修改配送状态
     */
    @Override
    public Integer updateState(String pdLine, String ord_no, String mating_no) {
        int result = -1;
        try {
            result += matchMapper.updateState(pdLine, mating_no);
            result += matchMapper.updateState2(pdLine, ord_no);
//            result = 1 / 0;
        } catch (Exception e) {
            return result;
        }
        return result;
    }

    /**
     * 已配套待投入
     */
    @Override
    public List<MkMating> getWaitInput(int page_no, int page_size, int status) {
        return matchMapper.getWaitInput(page_no, page_size, status);
    }

    /**
     * 待投入数量
     */
    @Override
    public Integer getInputCount(int status) {
        return matchMapper.getInputCount(status);
    }

    /**
     * 未配套库存查询
     */
    @Override
    public List<MkNpatQcTot> getNoMatch(Integer status) {
        return matchMapper.getNoMatch(status);
    }

    /**
     * 未配套库存明细
     */
    @Override
    public List<MkNpatQcDet> getNoMatchDetail(String brand_no, Integer status) {
        return matchMapper.getNoMatchDetail(brand_no, status);
    }

    /**
     * 已配套楦头确认
     */
    @Override
    public List<MkNpatQcDet> getMatchConfirm(int page_no, int page_size, int status) {
        return matchMapper.getMatchConfirm(page_no, page_size, status);
    }

    /**
     * 已配套楦头确认--明细
     */
    @Override
    public List<Object> getMatchConfirmDetail(String ord_No, String last_no) {
        return matchMapper.getMatchConfirmDetail(ord_No, last_no);
    }

    /**
     * 已配套楦头确认--操作
     */
    @SuppressWarnings("unchecked")
    @Override
    public List<Object> getMatchConfirmMXXX(String ord_No, String last_no) {
        List<Object> obj = matchMapper.getMatchConfirmMXXX(ord_No, last_no);
        String sizeAr = "";
        String sizeAr2 = "";
        String cmSize = "";
        for (int i = 0; i < obj.size(); i++) {
            HashMap<String, Object> ha = (HashMap<String, Object>) obj.get(i);
            sizeAr += ha.get("t_name").toString().trim() + ",";
            cmSize += ha.get("cm").toString().trim() + ",";
        }

        sizeAr = sizeAr.substring(0, sizeAr.length() - 1);
        String[] sizeArr = sizeAr.split(",");
        Set<String> set = new HashSet<>();
        for (String str : sizeArr) {
            set.add(str);
        }
        //真实码
        cmSize = cmSize.substring(0, cmSize.length() - 1);
        String[] cmArr = cmSize.split(",");
        Set<String> set2 = new HashSet<>();
        for (String str : cmArr) {
            set2.add(str);
        }
        //全部SIZE
        List<String> listStr = new ArrayList<>(set);
        Collections.sort(listStr);
        sizeArr = listStr.toArray(new String[0]);

        List<String> cmList = new ArrayList<>(set2);
        Collections.sort(cmList, (s1, s2) -> Float.compare(Float.parseFloat(s1), Float.parseFloat(s2)));

        //Collections.sort(cmList);
        cmArr = cmList.toArray(new String[0]);
        for (int i = 0; i < sizeArr.length; i++) {
            sizeAr2 += "0,";
        }
        sizeAr2 = sizeAr2.substring(0, sizeAr2.length() - 1);
        String[] sizeArr2 = sizeAr2.split(",");
        for (int i = 0; i < obj.size(); i++) {
            HashMap<String, Object> ha = (HashMap<String, Object>) obj.get(i);
            String tnameStr = ha.get("t_name").toString();
            String ypStr = ha.get("yp").toString();
            String[] tnameArr = tnameStr.split(",");
            String[] ypArr = ypStr.split(",");
            if (sizeArr.length != tnameArr.length) {
                for (int j = 0; j < tnameArr.length; j++) {
                    for (int k = 0; k < sizeArr.length; k++) {
                        if (tnameArr[j].equals(sizeArr[k])) {
                            sizeArr2[k] = ypArr[j];
                            break;
                        }
                    }
                }
                ha.put("yp", sizeArr2);
            } else {
                ha.put("yp", ypStr.split(","));
            }
            ((HashMap<String, Object>) obj.get(i)).put("t_name", sizeArr);
        }

        ((HashMap<String, Object>) obj.get(0)).put("cm", cmArr);
        return obj;
    }


    /**
     * 修改楦头状态
     */
    @Override
    public Integer updateXuanTouState(String mating_no) {
        int result = -1;
        try {
            result = matchMapper.updateXuanTouState(mating_no);
        } catch (Exception e) {
            return result;
        }
        return result;
    }

    /**
     * 未配套库存明细
     */
    @Override
    public List<MkNpatQcDet> formingQuery(int page_no, int page_size, Integer status, Integer status2) {
        return matchMapper.formingQuery(page_no, page_size, status, status2);
    }

    /**
     * 未配套库存明细数量
     */
    @Override
    public List<Integer> getformingCount(int status, int status2, int status3) {
        if (status3 < 3) {

            return matchMapper.getMatchCount(status3);
        }
        return matchMapper.getformingCount(status, status2, status3);
    }

    @Override
    public Integer insertConfirmDetail(List<String> sizeList, List<String> sizeListCot, String ord_no,
                                       String last_no, String width, String lr_mark) {
        // TODO Auto-generated method stub
        return matchMapper.insertConfirmDetail(sizeList, sizeListCot, ord_no, last_no, width, lr_mark);
    }
}
