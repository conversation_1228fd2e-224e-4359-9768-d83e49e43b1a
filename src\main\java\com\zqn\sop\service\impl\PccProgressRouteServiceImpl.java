package com.zqn.sop.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zqn.sop.entity.PccMeProjectPlanHd;
import com.zqn.sop.entity.PccProgressRoute;
import com.zqn.sop.mapper.PccProgressRouteMapper;
import com.zqn.sop.service.PccProgressRouteService;
import com.zqn.sop.vo.PccProgressRouteVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

@Service
public class PccProgressRouteServiceImpl implements PccProgressRouteService {

    @Resource
    private PccProgressRouteMapper mapper;

    @DS("app")
    @Override
    public PageInfo<PccProgressRoute> query(int pageNo, int pageSize, String factory, String dept) {
        PageHelper.startPage(pageNo, pageSize);
        List<PccProgressRoute> list = mapper.query(factory, dept);
//        for (PccMeProjectPlanHd pccMeProjectPlanHd : list) {
//            if (pccMeProjectPlanHd.getDept() != null) {
//                pccMeProjectPlanHd.setDept_name(pccMeProjectPlanHd.getDept());
//            }
//            Integer maxItem = pccMeProjectPlanDtService.selectMax(pccMeProjectPlanHd.getModel_no(), pccMeProjectPlanHd.getDept());
//            pccMeProjectPlanHd.setItem_num(maxItem);
//        }
        PageInfo<PccProgressRoute> pageInfo = new PageInfo<>(list);
        return pageInfo;
    }

    @DS("app")
    @Transactional
    @Override
    public int create(PccProgressRouteVo vo) throws Exception {
        return mapper.save(vo);
    }

    @DS("app")
    @Transactional
    @Override
    public int update(PccProgressRouteVo vo) throws Exception {
        return mapper.udpate(vo);
    }

    @DS("app")
    @Override
    public PccProgressRoute getById(Integer id) {
        return mapper.getById(id);
    }

    @DS("app")
    @Override
    public PccProgressRoute getByFactoryAndDept(PccProgressRouteVo vo) {
        return mapper.getByFactoryAndDept(vo.getFactory(), vo.getDept());
    }

    @Override
    @Transactional
    @DS("app")
    public Integer delete(Integer id) throws Exception {
        mapper.delete(id);
        return 1;
    }
}
