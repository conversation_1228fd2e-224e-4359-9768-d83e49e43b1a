package com.zqn.newmodel.entity;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 新型体开发指令第二个表格
 * @date 2024/8/5 15:32
 */
@Data
public class NewModelTable2 {

    //项次
    private String item_no;

    //日期
    @JSONField(format = "MM/dd")
    private Date shp_date;

    //肥度
    private String width;

    //左右
    private String lr_mark;

    private BigDecimal size01;
    private BigDecimal size02;
    private BigDecimal size03;
    private BigDecimal size04;
    private BigDecimal size05;
    private BigDecimal size06;
    private BigDecimal size07;
    private BigDecimal size08;
    private BigDecimal size09;
    private BigDecimal size10;
    private BigDecimal size11;
    private BigDecimal size12;
    private BigDecimal size13;
    private BigDecimal size14;
    private BigDecimal size15;
    private BigDecimal size16;
    private BigDecimal size17;
    private BigDecimal size18;
    private BigDecimal size19;
    private BigDecimal size20;
    private BigDecimal size21;
    private BigDecimal size22;
    private BigDecimal size23;
    private BigDecimal size24;
    private BigDecimal size25;
    private BigDecimal size26;
    private BigDecimal size27;
    private BigDecimal size28;
    private BigDecimal size29;
    private BigDecimal size30;

    //合计
    private BigDecimal tot_qty;

    //备注
    private String remark;
}
