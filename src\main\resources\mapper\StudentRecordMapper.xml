<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zqn.studentrecord.mapper.StudentRecordMapper">

    <!-- 结果映射 -->
    <resultMap id="StudentRecordResultMap" type="com.zqn.studentrecord.entity.StudentRecord">
        <id property="id" column="ID"/>
        <result property="recordDate" column="RECORD_DATE"/>
        <result property="orderNo" column="ORDER_NO"/>
        <result property="student" column="STUDENT"/>
        <result property="ordQty" column="ORD_QTY"/>
        <result property="modelNo" column="MODEL_NO"/>
        <result property="actionPicsJson" column="ACTION_PICS"/>
        <result property="improvePicsJson" column="IMPROVE_PICS"/>
        <result property="productPicsJson" column="PRODUCT_PICS"/>
        <result property="studentThoughts" column="STUDENT_THOUGHTS"/>
        <result property="communicationIssues" column="COMMUNICATION_ISSUES"/>
        <result property="learnedInt" column="LEARNED"/>
        <result property="createTime" column="CREATE_TIME"/>
        <result property="updateTime" column="UPDATE_TIME"/>
        <result property="createUser" column="CREATE_USER"/>
        <result property="updateUser" column="UPDATE_USER"/>
        <result property="status" column="STATUS"/>
        <result property="shoePic" column="MODEL_PIC" jdbcType="BLOB"/>
    </resultMap>

    <!-- 插入学员记录 -->
    <insert id="insert" parameterType="com.zqn.studentrecord.entity.StudentRecord">
        INSERT INTO PCC_STUDENT_RECORD (
            ID, RECORD_DATE, ORDER_NO, STUDENT, SHOE_PIC, ACTION_PICS, IMPROVE_PICS,
            PRODUCT_PICS, STUDENT_THOUGHTS, COMMUNICATION_ISSUES, LEARNED, 
            CREATE_TIME, UPDATE_TIME, CREATE_USER, UPDATE_USER, STATUS
        ) VALUES (
            #{id}, #{recordDate}, #{orderNo}, #{student}, #{shoePic}, #{actionPicsJson}, #{improvePicsJson},
            #{productPicsJson}, #{studentThoughts}, #{communicationIssues}, #{learnedInt}, 
            SYSDATE, SYSDATE, #{createUser}, #{updateUser}, 1
        )
    </insert>

    <!-- 根据ID查询学员记录 -->
    <select id="selectById" parameterType="long" resultMap="StudentRecordResultMap">
        SELECT ID, RECORD_DATE, ORDER_NO, STUDENT, ACTION_PICS, IMPROVE_PICS, PRODUCT_PICS,
               STUDENT_THOUGHTS, COMMUNICATION_ISSUES, LEARNED, CREATE_TIME, UPDATE_TIME, 
               CREATE_USER, UPDATE_USER, STATUS 
        FROM PCC_STUDENT_RECORD 
        WHERE ID = #{id} AND STATUS = 1
    </select>

    <!-- 更新学员记录 -->
    <update id="updateById" parameterType="com.zqn.studentrecord.entity.StudentRecord">
        UPDATE PCC_STUDENT_RECORD SET 
            RECORD_DATE = #{recordDate}, 
            ORDER_NO = #{orderNo},
            STUDENT = #{student},
            SHOE_PIC = #{shoePic}, 
            ACTION_PICS = #{actionPicsJson}, 
            IMPROVE_PICS = #{improvePicsJson},
            PRODUCT_PICS = #{productPicsJson}, 
            STUDENT_THOUGHTS = #{studentThoughts},
            COMMUNICATION_ISSUES = #{communicationIssues}, 
            LEARNED = #{learnedInt},
            UPDATE_TIME = SYSDATE, 
            UPDATE_USER = #{updateUser}
        WHERE ID = #{id} AND STATUS = 1
    </update>

    <!-- 逻辑删除学员记录 -->
    <update id="deleteById" parameterType="long">
        UPDATE PCC_STUDENT_RECORD SET 
            STATUS = 0, 
            UPDATE_TIME = SYSDATE 
        WHERE ID = #{id}
    </update>

    <!-- 分页查询学员记录列表 -->
    <select id="selectPage" resultMap="StudentRecordResultMap">
        SELECT ID, RECORD_DATE, ORDER_NO, STUDENT, ACTION_PICS, IMPROVE_PICS, PRODUCT_PICS,
               STUDENT_THOUGHTS, COMMUNICATION_ISSUES, LEARNED, CREATE_TIME, UPDATE_TIME,
               CREATE_USER, UPDATE_USER, STATUS 
        FROM PCC_STUDENT_RECORD 
        WHERE STATUS = 1
        <if test="orderNo != null and orderNo != ''">
            AND ORDER_NO LIKE '%' || #{orderNo} || '%'
        </if>
        <if test="student != null and student != ''">
            AND STUDENT LIKE '%' || #{student} || '%'
        </if>
        <if test="startDate != null">
            AND RECORD_DATE >= #{startDate}
        </if>
        <if test="endDate != null">
            AND RECORD_DATE <![CDATA[<=]]> #{endDate}
        </if>
        ORDER BY RECORD_DATE DESC, CREATE_TIME DESC
        OFFSET #{offset} ROWS FETCH NEXT #{size} ROWS ONLY
    </select>

    <!-- 查询记录总数 -->
    <select id="countTotal" resultType="long">
        SELECT COUNT(*) 
        FROM PCC_STUDENT_RECORD 
        WHERE STATUS = 1
        <if test="orderNo != null and orderNo != ''">
            AND ORDER_NO LIKE '%' || #{orderNo} || '%'
        </if>
        <if test="startDate != null">
            AND RECORD_DATE >= #{startDate}
        </if>
        <if test="endDate != null">
            AND RECORD_DATE <![CDATA[<=]]> #{endDate}
        </if>
    </select>

    <!-- 根据单号获取鞋图 -->
    <select id="getShoeImageByOrderNo" parameterType="string" resultMap="StudentRecordResultMap">
        SELECT b.model_pic
        FROM gc_sorder a 
        LEFT JOIN CK_SMODEL b ON a.MODEL_NO = b.model_no 
        WHERE a.ORD_NO = #{orderNo}
    </select>

    <!-- 获取下一个序列值 -->
    <select id="getNextSequenceValue" resultType="long">
        SELECT PCC_STUDENT_RECORD_SEQ.NEXTVAL FROM DUAL
    </select>

    <!-- 批量根据单号获取鞋图 -->
    <select id="getShoeImagesByOrderNos" resultMap="StudentRecordResultMap">
        SELECT a.ORD_NO as ORDER_NO, b.model_pic
        FROM gc_sorder a 
        LEFT JOIN CK_SMODEL b ON a.MODEL_NO = b.model_no 
        WHERE a.ORD_NO IN 
        <foreach collection="orderNos" item="orderNo" open="(" separator="," close=")">
            #{orderNo}
        </foreach>
    </select>

    <!-- 批量根据单号获取订单数量和型体 -->
    <select id="getOrderInfoByOrderNos" resultMap="StudentRecordResultMap">
        SELECT ORD_NO as ORDER_NO, ORD_QTY, MODEL_NO
        FROM gc_sorder 
        WHERE ORD_NO IN 
        <foreach collection="orderNos" item="orderNo" open="(" separator="," close=")">
            #{orderNo}
        </foreach>
    </select>

</mapper> 