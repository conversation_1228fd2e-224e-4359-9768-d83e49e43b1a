package com.zqn.factorymanager.controller;

import com.github.pagehelper.PageInfo;
import com.zqn.factorymanager.entity.RetryAlert;
import com.zqn.factorymanager.service.RetryAlertService;
import com.zqn.modeldata2.common.R;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:重做预警
 * @date 2024/8/6 8:02
 */
@RestController
@RequestMapping("/retryAlert")
public class RetryAlertController {

    @Resource
    private RetryAlertService retryAlertService;

    @GetMapping("/query")
    public R<PageInfo<RetryAlert>> query(@RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                         @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
                                         @RequestParam(value = "startTime", required = false) long startTimeTamp,
                                         @RequestParam(value = "endTime", required = false) long endTimeTamp,
                                         @RequestParam(value = "type") Integer type) {
        Date startTime = new Date(startTimeTamp); // 将时间戳转换为Date对象
        Date endTime = new Date(endTimeTamp); // 将时间戳转换为Date对象
        return retryAlertService.query(pageNo, pageSize, startTime, endTime, type);
    }
}
