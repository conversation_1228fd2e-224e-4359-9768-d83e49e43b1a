package com.zqn.sop.controller;

import cn.afterturn.easypoi.cache.ExcelCache;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import com.zqn.modeldata2.service.ProcessService;
import com.zqn.sop.entity.PccMeProjectPlanImg;
import com.zqn.sop.service.PccMeProjectPlanDtService;
import com.zqn.sop.util.ExcelExportUtil;
import com.zqn.sop.vo.PccMeProjectPlanDtVo;
import org.apache.poi.ss.usermodel.PrintSetup;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFDrawing;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

@Controller
@RequestMapping("/export")
public class ExportController {

    @Resource
    private PccMeProjectPlanDtService pccMeProjectPlanDtService;

    @Value("${uploadUrl}")
    private String TARGET_FOLDER;

    @Resource
    private ProcessService processService;

    public static byte[] compressImage(String inputImagePath) {
        try {
            // 读取图像
            BufferedImage bufferImg = ImageIO.read(new File(inputImagePath));

            // 获取图片格式
            String formatName = inputImagePath.substring(inputImagePath.lastIndexOf(".") + 1);
            if (formatName.equals("jfif")) {
                formatName = "jpeg";
            }

            // 获取图片写入器
            Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName(formatName);
            if (!writers.hasNext()) throw new IllegalStateException("No writers found");
            ImageWriter writer = writers.next();

            // 设置输出流
            ByteArrayOutputStream byteArrayOut = new ByteArrayOutputStream();
            ImageOutputStream ios = ImageIO.createImageOutputStream(byteArrayOut);
            writer.setOutput(ios);

            // 设置压缩参数
            ImageWriteParam param = writer.getDefaultWriteParam();
            if (param.canWriteCompressed()) {
                param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
                param.setCompressionQuality(0.2f); // 这里设置压缩质量
            }

            // 写入图像
            writer.write(null, new IIOImage(bufferImg, null, null), param);

            // 关闭流
            ios.close();
            writer.dispose();

            return byteArrayOut.toByteArray();

        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    @GetMapping("/excel")
    public void exportOperationProcess(HttpServletResponse response, @RequestParam(value = "modelNo") String modelNo,
                                       @RequestParam(value = "dept") String dept,
                                       @RequestParam(value = "brand") String brand,
                                       @RequestParam(value = "shoeLast") String shoeLast,
                                       @RequestParam(value = "id") Integer parentId,
                                       @RequestParam(value = "type") Integer exportType) throws IOException {
        // 模板路径
        String filePath = "static/excel/operation_process_fs.xlsx";
        if (exportType == 2) {
            filePath = "static/excel/operation_process_mb_fs.xlsx";
        }
        Workbook tempWorkbook = ExcelCache.getWorkbook(filePath, new Integer[]{0}, false);
        List<PccMeProjectPlanDtVo> pccMeProjectPlanDtVos = pccMeProjectPlanDtService.queryAllDt(parentId);
        byte[] modelImg = getModelPic(modelNo);

        Map<Integer, List<Map<String, Object>>> resultMap = new TreeMap<>();
        List<String> sheetList = new ArrayList<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");

        if (exportType == 2) {
            for (int sheetIndex = 0; sheetIndex < pccMeProjectPlanDtVos.size(); sheetIndex++) {
                PccMeProjectPlanDtVo pccMeProjectPlanDtVo = pccMeProjectPlanDtVos.get(sheetIndex);
                Map<String, Object> map = new TreeMap<>();
                map.put("title", "SOP");
                map.put("brand", "客户 Brand：" + brand);
                map.put("style", "Style：" + modelNo + "/" + shoeLast);
                String[] depts = dept.split("-");
                map.put("title", depts[1] + "作业流程 Operation process");
                map.put("item", pccMeProjectPlanDtVo.getItem_no());
                map.put("actions", pccMeProjectPlanDtVo.getActions());
                map.put("op_std", pccMeProjectPlanDtVo.getOp_std());
                map.put("tools", pccMeProjectPlanDtVo.getTools());
                map.put("chemical_substance", pccMeProjectPlanDtVo.getChemical_substance());
                map.put("machine", pccMeProjectPlanDtVo.getMachine());
                map.put("protective_gear", pccMeProjectPlanDtVo.getProtective_gear());
                String tempValue = (pccMeProjectPlanDtVo.getTemp() != null ? pccMeProjectPlanDtVo.getTemp() : "") +
                        (pccMeProjectPlanDtVo.getTime() != null ? pccMeProjectPlanDtVo.getTime() : "") +
                        (pccMeProjectPlanDtVo.getPressure() != null ? pccMeProjectPlanDtVo.getPressure() : "");
                map.put("temp", tempValue);
                map.put("pressure", pccMeProjectPlanDtVo.getPressure());
                map.put("time", pccMeProjectPlanDtVo.getTime());
                map.put("needle", pccMeProjectPlanDtVo.getNeedle());
                map.put("self_check_points", pccMeProjectPlanDtVo.getSelf_check_points());
                map.put("createBy", "制表 Tab：" + pccMeProjectPlanDtVo.getCreate_by());
                map.put("glue", pccMeProjectPlanDtVo.getGlue());
                String threadValue = (pccMeProjectPlanDtVo.getCar_line() != null ? pccMeProjectPlanDtVo.getCar_line() : "") +
                        (pccMeProjectPlanDtVo.getNeedle() != null ? pccMeProjectPlanDtVo.getNeedle() : "");
                map.put("thread", threadValue);
                String stitchValue = (pccMeProjectPlanDtVo.getNeedle_spacing() != null ? pccMeProjectPlanDtVo.getNeedle_spacing() : "") +
                        (pccMeProjectPlanDtVo.getSpacing() != null ? pccMeProjectPlanDtVo.getSpacing() : "") +
                        (pccMeProjectPlanDtVo.getMargin() != null ? pccMeProjectPlanDtVo.getMargin() : "");
                map.put("stitch", stitchValue);
                map.put("space", pccMeProjectPlanDtVo.getSpacing());
                map.put("empty", "");

                // Format the date
                Date updateDate = pccMeProjectPlanDtVo.getUpdate_date();
                String formattedDate = dateFormat.format(updateDate);
                map.put("createDate", "日期 Date:" + formattedDate);

                List<Map<String, Object>> mapList = new ArrayList<>();
                mapList.add(map);

                resultMap.put(sheetIndex, mapList);
                sheetList.add("Sheet" + (sheetIndex + 1));
            }
        } else {
            for (int sheetIndex = 0; sheetIndex < pccMeProjectPlanDtVos.size(); sheetIndex++) {
                PccMeProjectPlanDtVo pccMeProjectPlanDtVo = pccMeProjectPlanDtVos.get(sheetIndex);
                Map<String, Object> map = new TreeMap<>();
                map.put("title", "SOP");
                map.put("brand", "客户 Brand：" + brand);
                map.put("style", "Style：" + modelNo + "/" + shoeLast);
                String[] depts = dept.split("-");
                map.put("title", depts[1] + "作业流程 Operation process");
                map.put("item", pccMeProjectPlanDtVo.getItem_no());
                map.put("actions", pccMeProjectPlanDtVo.getActions());
                map.put("op_std", pccMeProjectPlanDtVo.getOp_std());
                map.put("tools", pccMeProjectPlanDtVo.getTools());
                map.put("chemical_substance", pccMeProjectPlanDtVo.getChemical_substance());
                map.put("machine", pccMeProjectPlanDtVo.getMachine());
                map.put("protective_gear", pccMeProjectPlanDtVo.getProtective_gear());
                map.put("temp", pccMeProjectPlanDtVo.getTemp() != null ? pccMeProjectPlanDtVo.getTemp() : "");
                map.put("pressure", pccMeProjectPlanDtVo.getPressure() != null ? pccMeProjectPlanDtVo.getPressure() : "");
                map.put("time", pccMeProjectPlanDtVo.getTime() != null ? pccMeProjectPlanDtVo.getTime() : "");
                map.put("needle", pccMeProjectPlanDtVo.getNeedle() != null ? pccMeProjectPlanDtVo.getNeedle() : "");
                map.put("self_check_points", pccMeProjectPlanDtVo.getSelf_check_points());
                map.put("createBy", "制表 Tab：" + pccMeProjectPlanDtVo.getCreate_by());
                map.put("glue", pccMeProjectPlanDtVo.getGlue() != null ? pccMeProjectPlanDtVo.getGlue() : "");
                map.put("thread", pccMeProjectPlanDtVo.getCar_line() != null ? pccMeProjectPlanDtVo.getCar_line() : "");
                map.put("margin", pccMeProjectPlanDtVo.getMargin() != null ? pccMeProjectPlanDtVo.getMargin() : "");
                map.put("stitch", pccMeProjectPlanDtVo.getNeedle_spacing() != null ? pccMeProjectPlanDtVo.getNeedle_spacing() : "");
                map.put("space", pccMeProjectPlanDtVo.getSpacing() != null ? pccMeProjectPlanDtVo.getSpacing() : "");
                map.put("empty", "");

                // Format the date
                Date updateDate = pccMeProjectPlanDtVo.getUpdate_date();
                String formattedDate = dateFormat.format(updateDate);
                map.put("createDate", "日期 Date:" + formattedDate);

                List<Map<String, Object>> mapList = new ArrayList<>();
                mapList.add(map);

                resultMap.put(sheetIndex, mapList);
                sheetList.add("Sheet" + (sheetIndex + 1));
            }
        }

        TemplateExportParams params = new TemplateExportParams(filePath, true);
        params.setTemplateWb(tempWorkbook);
        XSSFWorkbook workbook = (XSSFWorkbook) ExcelExportUtil.exportExcelClone(resultMap, params);

        // Set sheet names after all sheets are created
        for (int i = 0; i < sheetList.size(); i++) {
            workbook.setSheetName(i, sheetList.get(i));
        }

        // Ensure the page settings are copied
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            copyPrintSetup(tempWorkbook.getSheetAt(0), workbook.getSheetAt(i));
        }

        if (workbook != null) {
            // 判断是否根据内容自适应行高
            //暂时取消，多sheet导出高度不会失效
//            setRowHeight(workbook);
        }

        // 插入图片
        for (int imageIndex = 0; imageIndex < pccMeProjectPlanDtVos.size(); imageIndex++) {
            PccMeProjectPlanDtVo pccMeProjectPlanDtVo = pccMeProjectPlanDtVos.get(imageIndex);
            List<PccMeProjectPlanImg> assetsPicture = pccMeProjectPlanDtVo.getImgUrls();
            // 获取标签页
            XSSFSheet sheet = (XSSFSheet) workbook.getSheet("Sheet" + (imageIndex + 1));
            XSSFDrawing patriarch = sheet.createDrawingPatriarch();
            if (!CollectionUtils.isEmpty(assetsPicture)) {
                List<byte[]> type1Images = new ArrayList<>();

                //读取本地文件流并压缩
                for (PccMeProjectPlanImg pccMeProjectPlanImg : assetsPicture) {
                    String picturePath = TARGET_FOLDER + pccMeProjectPlanImg.getRemark();
                    byte[] compressedImage = compressImage(picturePath);
                    type1Images.add(compressedImage);
                }
                insertImagesInGrid(patriarch, workbook, type1Images, 6, 1, 8, 6);
            }

            //插入型体图片
            if (modelImg != null) {
                // 创建锚点
                XSSFClientAnchor anchor = new XSSFClientAnchor(0, 0, 0, 0, (short) 1, 0, (short) 2, 1);
                patriarch.createPicture(anchor, workbook.addPicture(modelImg, XSSFWorkbook.PICTURE_TYPE_PNG));
            }
        }

        String fileName = modelNo + ".xlsx";
        response.reset();
        response.setContentType("application/octet-stream;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
        response.setHeader("Access-Control-Allow-Origin", "*"); // 或者指定具体的域名
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With");
        try {
            workbook.write(response.getOutputStream());
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            workbook.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void insertImagesInGrid(XSSFDrawing patriarch, XSSFWorkbook workbook, List<byte[]> imageBytesList, int startRow, int startCol, int endRow, int endCol) {
        int numRows = 2; // 网格行数
        int numCols = 5; // 网格列数
        int rowHeight = (endRow - startRow) / numRows;
        int colWidth = (endCol - startCol) / numCols;

        for (int i = 0; i < numRows; i++) {
            for (int j = 0; j < numCols; j++) {
                int index = i * numCols + j;
                if (index >= imageBytesList.size()) {
                    return; // 如果图片数量少于网格单元格数量，提前返回
                }
                byte[] imageBytes = imageBytesList.get(index);
                int row1 = startRow + i * rowHeight;
                int col1 = startCol + j * colWidth;
                int row2 = row1 + rowHeight;
                int col2 = col1 + colWidth;

                // 创建锚点
                XSSFClientAnchor anchor = new XSSFClientAnchor(0, 0, 0, 0, (short) col1, row1, (short) col2, row2);
                patriarch.createPicture(anchor, workbook.addPicture(imageBytes, XSSFWorkbook.PICTURE_TYPE_PNG));
            }
        }
    }

    private byte[] getModelPic(String modelNo) {
        Map<String, Object> picture = processService.getPicture(modelNo);
        Object modelImg = picture.get("model_pic");
        if (modelImg instanceof byte[]) {
            return (byte[]) modelImg;
        } else {
            throw new IllegalArgumentException("model_pic is not of type byte[]");
        }
    }

    // Copy print setup settings from source sheet to target sheet
    private void copyPrintSetup(Sheet sourceSheet, Sheet targetSheet) {
        PrintSetup sourcePrintSetup = sourceSheet.getPrintSetup();
        PrintSetup targetPrintSetup = targetSheet.getPrintSetup();

        targetPrintSetup.setPaperSize(sourcePrintSetup.getPaperSize());
        targetPrintSetup.setScale(sourcePrintSetup.getScale());
        targetPrintSetup.setPageStart(sourcePrintSetup.getPageStart());
        targetPrintSetup.setFitWidth(sourcePrintSetup.getFitWidth());
        targetPrintSetup.setFitHeight(sourcePrintSetup.getFitHeight());
        targetPrintSetup.setHeaderMargin(sourcePrintSetup.getHeaderMargin());
        targetPrintSetup.setFooterMargin(sourcePrintSetup.getFooterMargin());
        targetPrintSetup.setLandscape(sourcePrintSetup.getLandscape());

        targetSheet.setMargin(Sheet.TopMargin, sourceSheet.getMargin(Sheet.TopMargin));
        targetSheet.setMargin(Sheet.BottomMargin, sourceSheet.getMargin(Sheet.BottomMargin));
        targetSheet.setMargin(Sheet.LeftMargin, sourceSheet.getMargin(Sheet.LeftMargin));
        targetSheet.setMargin(Sheet.RightMargin, sourceSheet.getMargin(Sheet.RightMargin));
        targetSheet.setMargin(Sheet.HeaderMargin, sourceSheet.getMargin(Sheet.HeaderMargin));
        targetSheet.setMargin(Sheet.FooterMargin, sourceSheet.getMargin(Sheet.FooterMargin));

        // Copy any other necessary settings
    }

    /**
     * 一对多，设置行高
     */
    private static void setRowHeight(Workbook workbook) {
        Sheet sheet = workbook.getSheetAt(0);
        //设置第4列的列宽为60（下标从0开始），TestExportSub2Vo 不知道为什么设置了列宽但是不起作用，只能在这里单独设置
//        sheet.setColumnWidth(3,60*256);
        for (int i = 0; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            int j = i / 9;
            if (i == 3 + (j * 9) || i == 5 + (j * 9)) {
                //设置其他行的行高根据内容自适应
                row.setHeightInPoints(70);
            } else if (i == 5 + (j * 9) || i == 7 + (j * 9)) {
                //设置其他行的行高根据内容自适应
                row.setHeightInPoints(70);
            } else {
                //设置第二行的行高（表格表头）
                row.setHeightInPoints(50);
            }
        }
    }

    private static void setRowHeight(Row row) {
        //根据内容长度设置行高
        int enterCnt = 0;
        for (int j = 0; j < row.getPhysicalNumberOfCells(); j++) {
            if (j < 7) {
                continue;
            }
            int rwsTemp = row.getCell(j).toString().length();
            //这里取每一行中的每一列字符长度最大的那一列的字符
            if (rwsTemp > enterCnt) {
                enterCnt = rwsTemp;
            }
        }
        //设置默认行高为50
        row.setHeightInPoints(50);
        //如果字符长度大于20，判断大了多少倍，根据倍数来设置相应的行高
        if (enterCnt > 15) {
            float d = (float) enterCnt / 15;
            if (d > 1.0f) {
                d = d * 1.0f;
            }
            float f = 50 * d;
            /*if (d>2 && d<4){
                f = 35*2;
            }else if(d>=4 && d<6){
                f = 35*3;
            }else if (d>=6 && d<8){
                f = 35*4;
            }*/
            row.setHeightInPoints(f);
        }
    }
}