package com.zqn.modeldata2.controller;

import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.entity.MkSlastbar;
import com.zqn.modeldata2.entity.MkSlastbarPlus;
import com.zqn.modeldata2.service.ScanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/scan")
public class ScanController {
    @Autowired
    private ScanService scanService;

    @PostMapping("/getCount")
    public R<Map<String, Object>> getCount(@RequestBody(required = false) Map<String, Object> params) {
        String user_id = "";
        String ins_user = "";
        String cl_flag = "";
        boolean today = false;

        if (params != null) {
            if (params.get("user_id") != null) {
                user_id = params.get("user_id").toString();
            }

            if (params.get("ins_user") != null) {
                ins_user = params.get("ins_user").toString();
            }

            if (params.get("cl_flag") != null) {
                cl_flag = params.get("cl_flag").toString();
            }

            if (params.get("today") != null) {
                today = (boolean) params.get("today");
            }
        }

        Map<String, Object> result = scanService.getCount(user_id, ins_user, cl_flag, today);
        return R.success(result);
    }

    @PostMapping("/getScanException")
    public R<List<MkSlastbarPlus>> getScanException(@RequestBody(required = false) Map<String, Object> params) {
        String user_id = "";
        String ins_user = "";
        String cl_flag = "";
        boolean today = false;
        int page_no = 0;
        int page_size = 0;

        if (params != null) {
            if (params.get("user_id") != null) {
                user_id = params.get("user_id").toString();
            }

            if (params.get("ins_user") != null) {
                ins_user = params.get("ins_user").toString();
            }

            if (params.get("cl_flag") != null) {
                cl_flag = params.get("cl_flag").toString();
            }

            if (params.get("today") != null) {
                today = (boolean) params.get("today");
            }

            if (params.get("page_no") != null) {
                page_no = Integer.parseInt(params.get("page_no").toString());
            }

            if (params.get("page_size") != null) {
                page_size = Integer.parseInt(params.get("page_size").toString());
            }
        }

        List<MkSlastbarPlus> result = scanService.getScanException(user_id, ins_user, cl_flag, today, page_no, page_size);

        return R.success(result);
    }

    @PostMapping("/getBarCodeA")
    public R<Map<String, Object>> getBarCodeA(@RequestBody Map<String, Object> params) {
        String ord_no = params.get("ord_no") == null ? "" : params.get("ord_no").toString();
        Map<String, Object> result = scanService.getBarCodeA(ord_no);
        if (result == null) {
            return R.error("样品单号错误！");
        }
        return R.success(result);
    }

    @PostMapping("/getBarCodeB")
    public R<Map<String, Object>> getBarCodeB(@RequestBody Map<String, Object> params) {
        String column_seq = params.get("column_seq") == null ? "" : params.get("column_seq").toString();
        Map<String, Object> result = scanService.getBarCodeB(column_seq);
        if (result == null) {
            return R.error("责任部门错误！");
        }
        return R.success(result);
    }

    @PostMapping("/getBarCodeC")
    public R<Map<String, Object>> getBarCodeC(@RequestBody Map<String, Object> params) {
        String column_seq = params.get("column_seq") == null ? "" : params.get("column_seq").toString();
        Map<String, Object> result = scanService.getBarCodeC(column_seq);
        if (result == null) {
            return R.error("异常说明错误！");
        }
        return R.success(result);
    }

    @PostMapping("/getBarCodeD")
    public R<Map<String, Object>> getBarCodeD(@RequestBody Map<String, Object> params) {
        String column_seq = params.get("column_seq") == null ? "" : params.get("column_seq").toString();
        Map<String, Object> result = scanService.getBarCodeD(column_seq);
        if (result == null) {
            return R.error("异常地点错误！");
        }
        return R.success(result);
    }

    @PostMapping("/getOption")
    public R<List<Object>> getOption(@RequestBody MkSlastbar mkSlastbar) {
        if (mkSlastbar.getPb_desc() == null || mkSlastbar.getPb_addr() == null) {
            return R.error("异常说明和异常地点不能为空！");
        }
        List<Object> result = scanService.getOption(mkSlastbar);
        return R.success(result);
    }

    @PostMapping("/addException")
    public R<String> addException(@RequestBody MkSlastbar mkSlastbar) {
        int result = scanService.addException(mkSlastbar);
        if (result < 0) {
            return R.error("异常数据添加失败！");
        }
        return R.success("异常数据添加成功！");
    }

    @PostMapping("/updateException")
    public R<String> updateException(@RequestBody MkSlastbar mkSlastbar) {
        int result = scanService.updateException(mkSlastbar);
        if (result < 0) {
            return R.error("异常数据修改失败！");
        }
        return R.success("异常数据修改成功！");
    }

    @PostMapping("/updateMethod")
    public R<String> updateMethod(@RequestBody MkSlastbar mkSlastbar) {
        int result = scanService.updateMethod(mkSlastbar);
        if (result < 0) {
            return R.error("解决方法修改失败！");
        }
        return R.success("解决方法修改成功！");
    }

    @PostMapping("/updateState")
    public R<String> updateState(@RequestBody MkSlastbar mkSlastbar) {
        int result = scanService.updateState(mkSlastbar);
        if (result < 0) {
            return R.error("结案状态修改失败！");
        }
        return R.success("结案状态修改成功！");
    }

    @PostMapping("/deleteException")
    public R<String> deleteException(@RequestBody MkSlastbar mkSlastbar) {
        int result = scanService.deleteException(mkSlastbar);
        if (result < 0) {
            return R.error("异常数据删除失败！");
        }
        return R.success("异常数据删除成功！");
    }
}