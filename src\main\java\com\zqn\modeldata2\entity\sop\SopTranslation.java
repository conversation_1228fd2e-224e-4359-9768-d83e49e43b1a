package com.zqn.modeldata2.entity.sop;

import lombok.Data;

/**
 * SOP 工序翻译
 */
@Data
public class SopTranslation {
    // 型体编号
    private String model_no;
    // 制程
    private String operation;
    // 详细制程
    private String pro_seq;
    // 主要代码
    private String rtg_code;
    // 工序编号
    private String seq_no;
    // 排序号
    private String skey;
    // 内容
    private String content;
    // 语言
    private String lang;
    // 类型
    private int type;
    // 动作 - 中文
    private String action_cn;
    // 动作 - 英文
    private String action_en;
    // 动作 - 越南文
    private String action_vi;
    // 动作 - 印尼文
    private String action_id;
    // 动作 - 孟加拉文
    private String action_bn;
    // 操作标准 - 中文
    private String standard_cn;
    // 操作标准 - 英文
    private String standard_en;
    // 操作标准 - 越南文
    private String standard_vi;
    // 操作标准 - 印尼文
    private String standard_id;
    // 操作标准 - 孟加拉文
    private String standard_bn;
    // 自检点 - 中文
    private String check_point_cn;
    // 自检点 - 英文
    private String check_point_en;
    // 自检点 - 越南文
    private String check_point_vi;
    // 自检点 - 印尼文
    private String check_point_id;
    // 自检点 - 孟加拉文
    private String check_point_bn;
}
