package com.zqn.modeldata2.entity;

import lombok.Data;

import java.util.Date;

/**
 * 工序信息
 */
@Data
public class ProcessInfo {
    // 型体编号
    private String model_no;
    // 制程
    private String operation;
    // 主要代码
    private String rtg_code;
    // 生产类型
    private String rtg_type;
    // 材质
    private String material;
    // 添加者
    private String ins_user;
    // 添加时间
    private Date ins_date;
    // 修改者
    private String upd_user;
    // 修改时间
    private Date upd_date;
}
