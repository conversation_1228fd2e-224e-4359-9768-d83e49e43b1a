package com.zqn.sop.service;

import com.github.pagehelper.PageInfo;
import com.zqn.sop.entity.PccProgressRoute;
import com.zqn.sop.vo.PccProgressRouteVo;

public interface PccProgressRouteService {

    PageInfo<PccProgressRoute> query(int pageNo, int pageSize, String factory, String dept );


    int create(PccProgressRouteVo vo) throws Exception;

    int update(PccProgressRouteVo vo) throws Exception;

    PccProgressRoute getById(Integer vo);

    PccProgressRoute getByFactoryAndDept(PccProgressRouteVo vo);

    Integer delete(Integer id) throws Exception;

}