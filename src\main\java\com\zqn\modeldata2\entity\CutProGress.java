package com.zqn.modeldata2.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/6/3 14:34
 */
@Data
public class CutProGress {

    //出貨日
    @JsonFormat(pattern = "YYYY/MM/dd")
    private Date shp_date;

    //品牌
    private String brand_no;

    //样品类型
    private String dev_type;

    //樣品單號
    private String ord_no;

    //項次
    private String item_no;

    //鞋圖
    private byte[] model_pic;

    //業務
    private String dutyer;

    //型體
    private String model_no;

    //楦頭編號
    private String last_no;

    //派工日
    @JsonFormat(pattern = "YYYY/MM/dd")
    private Date wo_date;

    //訂單量
    private String tot_qty;

    //中底皮
    private String c2_qty;

    //鞋面
    private String c1_qty;

    //中底
    private String c3_qty;

    //大底
    private String c4_qty;

    //滾條/頭片
    private String c5_qty;

    //跟皮
    private String c6_qty;

    //裁斷發外數量
    private String c7_qty;

    //裁断发外
    private String c_f_status;

    //鞋面發外
    private String u_f_status;

    //排入生产
    private String cin_flag;

    //排入時間
    private String cin_date;

    //排入區域
    private String cin_area;

    //異常信息
    private String pb_desc;

    //合计行
    //订单
    private BigDecimal total_order_quantity1;
    //鞋面
    private BigDecimal total_order_quantity2;
    //中底皮
    private BigDecimal total_order_quantity3;
    //中底
    private BigDecimal total_order_quantity4;
    //大底
    private BigDecimal total_order_quantity5;
    //滾片
    private BigDecimal total_order_quantity6;
    //跟皮
    private BigDecimal total_order_quantity7;

    private Date cinStartTime;
    private Date cinEndTime;
}
