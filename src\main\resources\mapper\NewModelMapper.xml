<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zqn.newmodel.mapper.NewModelMapper">

    <select id="queryHd" resultType="com.zqn.newmodel.entity.NewModelTitle">
        SELECT a.brand_no,
               d.brand_desc,
               a.season_no,
               a.series,
               a.pd_line,
               d.season_desc,
               A.frm_flag,
               a.season_no,
               a.ord_no,
               a.ord_seq,
               a.phase,
               a.model_no,
               a.factory,
               e.chn_sim,
               a.model_no ||
               DECODE(nvl(c.model_ver, a.model_ver), NULL, NULL, '-' || nvl(c.model_ver, a.model_ver) ||
                                                                 '(' || nvl(c.model_vardesc, a.model_vardesc) || ' ' ||
                                                                 c.material || ')') || '    ' || b.model_desc ||
               DECODE(nvl(c.model_ver, a.model_ver), NULL, NULL, '-' || nvl(c.model_ver, a.model_ver)) model_desc,
               a.pro_no,
               c.sku_no,
               b.module_desc,
               b.constr,
               b.last_no,
               c.color_desc,
               a.ord_nos,
               a.dev_type,
               a.ord_qty,
               a.ord_date,
               a.siz_type,
               a.ord_type,
               c.model_pic,
               fn_gcsordersets(a.proc_sets + a.prou_sets * POWER(2, 7) + a.prob_sets * POWER(2, 9)
                   + a.prop_sets * POWER(2, 13))                                                       pro_sets,
               fn_gcsordersets(a.prov_sets)                                                            prov_sets,
               fn_gcsordersets(a.prox_sets)                                                            prox_sets,
               NVL(a.upp_pic, c.upp_pic)                                                               upp_pic,
               nvl(a.sol_pic, c.sol_pic)                                                               sol_pic,
               USER                                                                                    sc_name,
               nvl((SELECT MIN(v.online_date) FROM vgc_factordall v WHERE v.pro_no = a.pro_no),
                   c.online_date)                                                                      online_date,
               decode(c.upp_desc, null, a.upp_desc, c.upp_desc || CHR(10) || a.upp_desc)
                   || DECODE(SUBSTR(USER, 1, 2), 'SR', CHR(13) || CHR(13)
                   || '襯布                   熱熔膠                    港寶', '')                     upp_desc,
               decode(c.sol_desc, null, a.sol_desc, c.sol_desc || CHR(10) || a.sol_desc)               sol_desc,
               a.dutyer,
               fn_sy_userdescp(a.ins_user)                                                             ins_user,
               a.ins_date,
               ROWNUM,
               b.mb_mater,
               b.iu_mater,
               b.id_mater,
               b.eva_mater,
               b.air_mater,
               b.air_pra,
               b.ins_len,
               b.cins_len,
               b.dw_tic,
               b.stampo,
               b.tin_mater,
               b.core_num,
               b.core_dist,
               b.fresa,
               b.sole_der,
               b.with_high,
               b.mb_trim,
               b.bot_rmk,
               b.bot_method,
               b.upper_der                                                                             dev_upper,
               b.sole_der                                                                              dev_sole,
               (SELECT COUNT(1) FROM gc_sordersd WHERE ord_no = a.ord_no)                              s_count
        FROM gc_sorder a,
             vck_smodels b,
             vcm_spros c,
             vbe_season d,
             ba_factory e
        WHERE a.ord_no = #{ordNo}
          and a.model_no = b.model_no
          AND a.frm_flag = b.frm_flag
          AND a.pro_no = c.pro_no
          AND a.frm_flag = c.frm_flag
          AND a.season_no = d.season_no
          AND a.factory = e.eng_sim(+)
        ORDER BY a.ord_no
    </select>

    <select id="pdGcSordereq" statementType="CALLABLE" resultType="string">
        {call pd_gc_sordereq(
                #{i_ord_no, mode=IN, jdbcType=VARCHAR},
                #{i_ord_type, mode=IN, jdbcType=VARCHAR},
                #{o_return, mode=INOUT, jdbcType=VARCHAR}
              )}
    </select>

    <select id="querySize" resultType="com.zqn.newmodel.entity.NewModelTable2">
        SELECT a.columna                               ord_no,
               CAST(a.columnb AS DATE)                 shp_date,
               a.columnc                               width,
               fn_sy_codeseqtodesc('BC010', a.columnd) lr_mark,
               to_number(a.columne)                    tot_qty,
               a.size01,
               a.size02,
               a.size03,
               a.size04,
               a.size05,
               a.size06,
               a.size07,
               a.size08,
               a.size09,
               a.size10,
               a.size11,
               a.size12,
               a.size13,
               a.size14,
               a.size15,
               a.size16,
               a.size17,
               a.size18,
               a.size19,
               a.size20,
               a.size21,
               a.size22,
               a.size23,
               a.size24,
               a.size25,
               a.size26,
               a.size27,
               a.size28,
               a.size29,
               a.size30,
               (SELECT f_link(DECODE(remark, NULL, NULL, remark || ' '))
                FROM gc_sordersd
                WHERE ord_no = a.columna)              remark
        FROM TABLE (fn_sy_cvsizerun('tp_gd_packsz', 'pack_no,ins_date,shoe_with,size_run,tot_qty', #{ordNo})) a
        WHERE a.columna = #{ordNo}
        ORDER BY ord_no, WIDTH, shp_date, lr_mark
    </select>

    <select id="queryDt" resultType="com.zqn.newmodel.entity.NewModelTable3">
        SELECT a.ord_no,
               a.part_no,
               NVL(a.part_seq, d.part_seq)                       part_seq,
               a.part_name,
               a.list_sets,
               b.mat_seq,
               CASE
                   WHEN #{brand_no} IN ('UA', 'SC') THEN (SELECT MAX(remark)
                                                          FROM sy_xcodeval
                                                          WHERE code_no = 'BC031'
                                                            AND column_seq = SUBSTR(FN_POWERSTR(a.list_sets), 1, 1)) ||
                                                         '.' ||
                                                         fn_sy_codeseqtodesc('BC031',
                                                                             SUBSTR(FN_POWERSTR(a.list_sets), 1, 1))
                   ELSE '.' END                                  part_type,
               nvl(a.mat_desc, b.mat_desc)                       mat_desc,
               nvl(b.euom, a.suom)                               suom,
               numtochar(a.dev_qty) || TRIM(nvl(b.euom, a.suom)) dev_qtya,
               a.ord_qty,
               numtochar(a.dev_qty)                              dev_qty,
               numtochar(a.dev_qty * #{ord_qty})                 req_qty,
               a.vend_no,
               c.vnd_sim,
               a.remark,
               a.ins_user,
               a.ins_date
        FROM (SELECT ord_no,
                     ord_qty,
                     MIN(part_seq)            part_seq,
                     MIN(part_no)             part_no,
                     F_LINK(part_name || ';') part_name,
                     MIN(list_sets)           list_sets,
                     mat_seq,
                     MAX(mat_desc)            mat_desc,
                     vend_no,
                     SUM(dev_qty)             dev_qty,
                     SUM(req_qty)             req_qty,
                     MAX(suom)                suom,
                     remark,
                     MAX(ins_user)            ins_user,
                     MAX(ins_date)            ins_date
              FROM gc_sorderd
              WHERE ord_no = #{ordNo}
              GROUP BY ord_no, mat_seq, mat_desc, vend_no, remark, ord_qty) a,
             cb_materialb b,
             hc_vendor c,
             vba_shoepart d
        WHERE a.mat_seq = b.mat_seq(+)
          AND a.vend_no = c.vendor(+)
          AND a.part_no = d.part_no(+)
        ORDER BY ord_no, part_type, part_seq, part_no
    </select>
    <select id="querySizeCaption" resultType="com.zqn.newmodel.entity.NewModelTable2">
        SELECT siz_type,
               size01,
               size02,
               size03,
               size04,
               size05,
               size06,
               size07,
               size08,
               size09,
               size10,
               size11,
               size12,
               size13,
               size14,
               size15,
               size16,
               size17,
               size18,
               size19,
               size20,
               size21,
               size22,
               size23,
               size24,
               size25,
               size26,
               size27,
               size28,
               size29,
               size30
        FROM TABLE (fn_sy_cvsizetype('gc_sorders', 'ord_no', #{ordNo}, #{sizType}))
    </select>

</mapper>