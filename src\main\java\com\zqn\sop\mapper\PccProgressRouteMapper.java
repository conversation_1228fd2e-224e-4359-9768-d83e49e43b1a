package com.zqn.sop.mapper;

import com.zqn.sop.entity.PccProgressRoute;
import com.zqn.sop.vo.PccProgressRouteVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PccProgressRouteMapper {

    List<PccProgressRoute> query(@Param("factory") String factory, @Param("dept") String dept);

    int save(@Param("route") PccProgressRouteVo route);

    int udpate(@Param("route") PccProgressRouteVo route);

    PccProgressRoute getById(@Param("id") Integer id);

    PccProgressRoute getByFactoryAndDept(@Param("factory") String factory, @Param("dept")String dept);

    void delete(@Param("id") Integer id);
}