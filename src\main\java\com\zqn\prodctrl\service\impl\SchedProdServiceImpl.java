package com.zqn.prodctrl.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zqn.prodctrl.entity.SchedProd;
import com.zqn.prodctrl.mapper.SchedProdMapper;
import com.zqn.prodctrl.service.SchedProdService;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/8/27 11:03
 */
@Service
public class SchedProdServiceImpl implements SchedProdService {

    private static final Logger logger = LoggerFactory.getLogger(SchedProdServiceImpl.class);
    @Autowired
    private SchedProdMapper scheduledProdMapper;

    @Override
    public PageInfo<SchedProd> query(int pageNo, int pageSize, Date startTime, Date endTime, String brand, String ordNo, String devType,String pFlag, String department, String phase, String pdLine) {
        // 创建一个SimpleDateFormat对象，指定日期格式
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy/MM/dd");
        // 将Date对象格式化为指定格式的字符串
        String startTimeStr = sdf.format(startTime);
        String endTimeStr = sdf.format(endTime);
        SchedProd count = scheduledProdMapper.queryCount(startTimeStr, endTimeStr, brand, ordNo, devType,pFlag,department,phase,pdLine);
        PageHelper.startPage(pageNo, pageSize);
        List<SchedProd> schedProds = scheduledProdMapper.query(startTimeStr, endTimeStr, brand, ordNo, devType,pFlag,department,phase,pdLine);
        PageInfo<SchedProd> pageInfo = new PageInfo<>(schedProds);
        for (SchedProd schedProd : pageInfo.getList()) {
            //处理订单总量
            if (count != null) {
                schedProd.setTot_qty_sum(count.getTot_qty_sum());
            }
        }
        return pageInfo;
    }

    @Override
    public List<JSONObject> queryAllDevType() {
        List<JSONObject> result = new ArrayList<JSONObject>();
        List<String> devTypes = scheduledProdMapper.queryAllDevType();
        if (devTypes.size() > 0) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("value", "");
            jsonObject.put("text", "請選擇");
            result.add(jsonObject);
        }
        for (String devType : devTypes) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("value", devType);
            jsonObject.put("text", devType);
            result.add(jsonObject);
        }
        return result;
    }

    @Override
    @Transactional
    public String edit(SchedProd schedProd) throws IllegalArgumentException {
        // 验证订单号和订单明细是否为空
        if (StringUtils.isBlank(schedProd.getOrd_no()) || StringUtils.isBlank(schedProd.getItem_no())) {
            throw new IllegalArgumentException("订单号或订单明细不能为空");
        }

        try {
            // 调用数据库操作方法
            scheduledProdMapper.edit(schedProd.getOrd_no(), schedProd.getItem_no());
            logger.info("订单 {} 的明细 {} 已成功更新", schedProd.getOrd_no(), schedProd.getItem_no());
            return "投入成功！";
        } catch (Exception e) {
            logger.error("编辑订单时发生异常：{}", e.getMessage(), e);
            throw new RuntimeException("编辑订单时发生异常，请稍后重试", e);
        }
    }
}
