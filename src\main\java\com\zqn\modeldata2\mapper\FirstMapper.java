package com.zqn.modeldata2.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface FirstMapper {
    List<Object> getBrands();

    Map<String, Object> getModel(@Param("model_no") String model_no);

    List<Object> getModels(@Param("brand_no") String brand_no, @Param("model_no") String model_no);

    List<Object> getModelsByShoeLast(@Param("brand_no") String brand_no, @Param("shoe_last") String shoe_last);

    Map<String, Object> getModelPicture(@Param("model_no") String model_no);

    List<String> getSizeTypeList(@Param("brand_no") String brand_no);

    List<String> getSizeOption(@Param("siz_type") String siz_type);

    Integer updateSizeType(@Param("model_no") String model_no, @Param("siz_type") String siz_type, @Param("upd_user") String upd_user);

    Integer updateBaseSize(@Param("model_no") String model_no, @Param("bas_size") String bas_size, @Param("upd_user") String upd_user);
}
