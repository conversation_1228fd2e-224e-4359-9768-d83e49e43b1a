package com.zqn.modeldata2.service.impl;

import com.github.houbb.heaven.util.lang.StringUtil;
import com.zqn.modeldata2.entity.UserDto;
import com.zqn.modeldata2.mapper.UserMapper;
import com.zqn.modeldata2.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private UserMapper userMapper;

    @Override
    public List<UserDto> getUser(String userNo) {
        if (StringUtil.isEmpty(userNo)) {
            return null;
        }
        return userMapper.getUser(userNo);
    }
}
