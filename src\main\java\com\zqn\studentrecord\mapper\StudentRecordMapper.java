package com.zqn.studentrecord.mapper;

import com.zqn.studentrecord.entity.StudentRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 学员记录Mapper接口
 */
@Mapper
public interface StudentRecordMapper {
    
    /**
     * 插入学员记录
     * @param record 学员记录
     * @return 影响行数
     */
    int insert(StudentRecord record);
    
    /**
     * 根据ID查询学员记录
     * @param id 记录ID
     * @return 学员记录
     */
    StudentRecord selectById(@Param("id") Long id);
    
    /**
     * 更新学员记录
     * @param record 学员记录
     * @return 影响行数
     */
    int updateById(StudentRecord record);
    
    /**
     * 逻辑删除学员记录
     * @param id 记录ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 分页查询学员记录列表
     * @param offset 偏移量
     * @param size 每页大小
     * @param orderNo 单号（可选）
     * @param student 学员（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 学员记录列表
     */
    List<StudentRecord> selectPage(@Param("offset") int offset, @Param("size") int size, 
                                  @Param("orderNo") String orderNo, @Param("student") String student,
                                  @Param("startDate") Date startDate, @Param("endDate") Date endDate);
    
    /**
     * 查询记录总数
     * @param orderNo 单号（可选）
     * @param student 学员（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 总数
     */
    long countTotal(@Param("orderNo") String orderNo, @Param("student") String student,
                   @Param("startDate") Date startDate, @Param("endDate") Date endDate);
    
    /**
     * 根据单号获取鞋图
     * 从gc_sorder表关联CK_SMODEL表获取model_pic(BLOB数据)
     * 使用Oracle的UTL_RAW.CAST_TO_RAW函数处理BLOB
     * @param orderNo 单号
     * @return 鞋图的byte数组
     */
    StudentRecord getShoeImageByOrderNo(@Param("orderNo") String orderNo);
    
    /**
     * 获取下一个序列值
     * @return 序列值
     */
    Long getNextSequenceValue();
    
    /**
     * 批量根据单号获取鞋图
     * @param orderNos 单号列表
     * @return 包含单号和鞋图的记录列表
     */
    List<StudentRecord> getShoeImagesByOrderNos(@Param("orderNos") List<String> orderNos);
    
    /**
     * 批量根据单号获取订单数量和型体
     * @param orderNos 单号列表
     * @return 包含单号、订单数量和型体的记录列表
     */
    List<StudentRecord> getOrderInfoByOrderNos(@Param("orderNos") List<String> orderNos);
} 