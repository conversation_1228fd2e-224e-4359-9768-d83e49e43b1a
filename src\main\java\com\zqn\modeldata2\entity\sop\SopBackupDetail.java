package com.zqn.modeldata2.entity.sop;

import lombok.Data;

import java.sql.Timestamp;

/**
 * 备份工序详情
 */
@Data
public class SopBackupDetail {
    // ID
    private long id;
    // 型体编号
    private String model_no;
    // 制程
    private String operation;
    // 主要代码
    private String rtg_code;
    // 工序编号
    private String seq_no;
    // 排序号
    private int skey;
    // 工序名称
    private String seq_name;
    // 加工段
    private String wk_group;
    // 材质
    private String material;
    // 每双片数
    private int piece;
    // 裁断层数
    private int layer;
    // 基本刀数
    private int cut_die;
    // 纯粹时间(秒@双)
    private int normal;
    // 附带时间(秒@双)
    private int add_time;
    // 标准时间(秒@双)
    private int std_time;
    // 标准配置人员
    private int std_emp;
    // 建议配置人员
    private int pps_emp;
    // 技术等次
    private String level_code;
    // 人工成本
    private int lab_cost;
    // 配色方式
    private String pline;
    // 设备编号
    private String item_no;
    // 设备名称
    private String description;
    // 设备数量
    private int use_qty;
    // 防护用品
    private String defence;
    // 备注
    private String remark;
    // 停用
    private String inv_flag;
    // 停用人
    private String inv_user;
    // 停用日期
    private Timestamp inv_date;
    // 建立人
    private String ins_user;
    // 建立日期
    private Timestamp ins_date;
    // 修改人
    private String upd_user;
    // 修改日期
    private Timestamp upd_date;
    // 试做原因注记
    private String szr_flag;
    // 无价值动作
    private String novalue;
    // 附加价值动作时间
    private int normal1;
    // 加工方法2
    private String seq_name2;
    // 型体版次
    private String model_ver;
    // 斩刀序号
    private String cut_seq;
    // 动作改善
    private String am_action;
    // 图片
    private byte[] seq_pic;
    // 分类
    private String class_name;
    // 规格
    private String spec;
    // 模数
    private int pmodule;
    // 每模双数
    private int ppmodule;
    // 重量/双
    private int matwht;
    // 一手料重量
    private int whtpair;
    // 动作
    private String actions;
    // 工具
    private String tools;
    // 边距
    private String margin;
    // 温度
    private String temp;
    // 压力
    private String pressure;
    // 胶水
    private String glue;
    // 车线
    private String car_line;
    // 化学品
    private String chemical_substance;
    // 针距
    private String needle_spacing;
    // 间距
    private String spacing;
    // 车针
    private String needle;
    // 图片备注 1
    private String img_tit1;
    // 图片备注 2
    private String img_tit2;
    //  时间
    private String time;
    // 版本
    private int version;
    // 加工选项
    private String process_option1;
    private String process_option2;
    private String process_option3;
    private String process_option4;
    private String process_option5;
    private String process_option6;
    private String process_option7;
    private String process_option8;
    private String process_option9;
    private String process_option10;
}
