package com.zqn.modeldata2.controller;

import com.github.pagehelper.PageInfo;
import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.entity.MaterialMatch;
import com.zqn.modeldata2.entity.MaterialMatchDt;
import com.zqn.modeldata2.service.MaterialMatchService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 仓库物料配套
 * @date 2024/7/19 9:04
 */
@RestController
@RequestMapping("/materialmatch")
@Validated
public class MaterialMatchController {

    @Resource
    private MaterialMatchService materialMatchService;

    @GetMapping("/query")
    public R<PageInfo<MaterialMatch>> query(@RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                            @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
                                            @RequestParam(value = "startTime", required = false) long startTimeTamp,
                                            @RequestParam(value = "endTime", required = false) long endTimeTamp,
                                            @RequestParam(value = "brand", required = false) String brand,
                                            @RequestParam(value = "devType", required = false) String devType,
                                            @RequestParam(value = "fileType", required = false) Integer fileType,
                                            @RequestParam(value = "cutComplType", required = false) Integer cutComplType) {
        Date startTime = new Date(startTimeTamp); // 将时间戳转换为Date对象
        Date endTime = new Date(endTimeTamp); // 将时间戳转换为Date对象
        return materialMatchService.query(pageNo, pageSize, startTime, endTime, brand, devType, fileType, cutComplType);
    }

    @GetMapping("/queryDetailTableData")
    public List<MaterialMatchDt> queryDetailTableData(@RequestParam(value = "ord_no", required = false) String ord_no) {
        return materialMatchService.queryDetailTableData(ord_no);
    }
}
