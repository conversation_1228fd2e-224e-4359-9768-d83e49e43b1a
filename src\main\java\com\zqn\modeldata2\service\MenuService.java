package com.zqn.modeldata2.service;

import com.zqn.modeldata2.entity.Menu;

import java.util.List;

public interface MenuService {

    /**
     * 通过父菜单查询
     *
     * @param partNo
     * @param userNo
     * @return
     */
    List<Menu> findByPart(String partNo, String userNo);

    List<Menu> findCommonyUsed(String userNo);

    Integer editCommonyUsed(List<Menu> menus);

    List<Menu> findByUser(String userNo);

    List<Menu> findReportByUser(String userNo);

    Integer editMenuByUser(List<Menu> menus);

    Boolean buttonQuery(String url, String loginUser);
}