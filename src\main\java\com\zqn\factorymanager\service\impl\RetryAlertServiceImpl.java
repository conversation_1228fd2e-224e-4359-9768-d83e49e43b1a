package com.zqn.factorymanager.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zqn.factorymanager.entity.RetryAlert;
import com.zqn.factorymanager.mapper.RetryAlertMapper;
import com.zqn.factorymanager.service.RetryAlertService;
import com.zqn.modeldata2.common.R;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/11/15 10:36
 */
@Service
public class RetryAlertServiceImpl implements RetryAlertService {

    @Resource
    private RetryAlertMapper retryAlertMapper;

    @Override
    public R<PageInfo<RetryAlert>> query(int pageNo, int pageSize, Date startTime, Date endTime, Integer type) {
        PageHelper.startPage(pageNo, pageSize);
        List<RetryAlert> list = new ArrayList<>();
        if (type == 1) {
            list = retryAlertMapper.queryTop(startTime, endTime);
        } else if (type == 2) {
            list = retryAlertMapper.queryBottom(startTime, endTime);
        }
        PageInfo<RetryAlert> pageInfo = new PageInfo<>(list);
        return R.success(pageInfo);
    }
}
