package com.zqn.modeldata2.entity;

import lombok.Data;

import java.util.Date;

@Data
public class CkSmodelpa {
    // 型体
    private String model_no;

    // 规格类型
    private Integer procs_type;

    // 部位序号
    private String part_seq;

    // 部位名称
    private String part_name;

    // 部位规格
    private Double part_spec;

    // 级放数
    private Double add_per;

    // 添加者
    private String ins_user;

    // 添加时间
    private Date ins_date;

    // 修改者
    private String upd_user;

    // 修改时间
    private Date upd_date;
}
