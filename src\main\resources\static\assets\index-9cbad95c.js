!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver((e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)})).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();const e={};function t(e,t){const n=Object.create(null),o=e.split(",");for(let r=0;r<o.length;r++)n[o[r]]=!0;return t?e=>!!n[e.toLowerCase()]:e=>!!n[e]}function n(e){if(k(e)){const t={};for(let o=0;o<e.length;o++){const r=e[o],i=L(r)?s(r):n(r);if(i)for(const e in i)t[e]=i[e]}return t}return L(e)||A(e)?e:void 0}const o=/;(?![^(]*\))/g,r=/:([^]+)/,i=/\/\*.*?\*\//gs;function s(e){const t={};return e.replace(i,"").split(o).forEach((e=>{if(e){const n=e.split(r);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}function a(e){let t="";if(L(e))t=e;else if(k(e))for(let n=0;n<e.length;n++){const o=a(e[n]);o&&(t+=o+" ")}else if(A(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const l=t("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function c(e){return!!e||""===e}function u(e,t){if(e===t)return!0;let n=$(e),o=$(t);if(n||o)return!(!n||!o)&&e.getTime()===t.getTime();if(n=P(e),o=P(t),n||o)return e===t;if(n=k(e),o=k(t),n||o)return!(!n||!o)&&function(e,t){if(e.length!==t.length)return!1;let n=!0;for(let o=0;n&&o<e.length;o++)n=u(e[o],t[o]);return n}(e,t);if(n=A(e),o=A(t),n||o){if(!n||!o)return!1;if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e){const o=e.hasOwnProperty(n),r=t.hasOwnProperty(n);if(o&&!r||!o&&r||!u(e[n],t[n]))return!1}}return String(e)===String(t)}function d(e,t){return e.findIndex((e=>u(e,t)))}const f=e=>L(e)?e:null==e?"":k(e)||A(e)&&(e.toString===j||!O(e.toString))?JSON.stringify(e,p,2):String(e),p=(e,t)=>t&&t.__v_isRef?p(e,t.value):E(t)?{[`Map(${t.size})`]:[...t.entries()].reduce(((e,[t,n])=>(e[`${t} =>`]=n,e)),{})}:T(t)?{[`Set(${t.size})`]:[...t.values()]}:!A(t)||k(t)||I(t)?t:String(t),h={},g=[],m=()=>{},v=()=>!1,y=/^on[^a-z]/,b=e=>y.test(e),_=e=>e.startsWith("onUpdate:"),w=Object.assign,x=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},S=Object.prototype.hasOwnProperty,C=(e,t)=>S.call(e,t),k=Array.isArray,E=e=>"[object Map]"===R(e),T=e=>"[object Set]"===R(e),$=e=>"[object Date]"===R(e),O=e=>"function"==typeof e,L=e=>"string"==typeof e,P=e=>"symbol"==typeof e,A=e=>null!==e&&"object"==typeof e,N=e=>A(e)&&O(e.then)&&O(e.catch),j=Object.prototype.toString,R=e=>j.call(e),I=e=>"[object Object]"===R(e),B=e=>L(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,M=t(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),F=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},V=/-(\w)/g,W=F((e=>e.replace(V,((e,t)=>t?t.toUpperCase():"")))),H=/\B([A-Z])/g,U=F((e=>e.replace(H,"-$1").toLowerCase())),D=F((e=>e.charAt(0).toUpperCase()+e.slice(1))),z=F((e=>e?`on${D(e)}`:"")),q=(e,t)=>!Object.is(e,t),X=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},Y=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},K=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let J;const Z=["ad","ad-content-page","ad-draw","audio","button","camera","canvas","checkbox","checkbox-group","cover-image","cover-view","editor","form","functional-page-navigator","icon","image","input","label","live-player","live-pusher","map","movable-area","movable-view","navigator","official-account","open-data","picker","picker-view","picker-view-column","progress","radio","radio-group","rich-text","scroll-view","slider","swiper","swiper-item","switch","text","textarea","video","view","web-view"].map((e=>"uni-"+e));function G(e){if(!e)return;let t=e.type.name;for(;t&&(n=U(t),-1!==Z.indexOf("uni-"+n.replace("v-uni-","")));)t=(e=e.parent).type.name;var n;return e.proxy}function Q(e){return 1===e.nodeType}function ee(e){return 0===e.indexOf("/")}function te(e){return ee(e)?e:"/"+e}function ne(e,t=null){let n;return(...o)=>(e&&(n=e.apply(t,o),e=null),n)}function oe(e){return W(e.substring(5))}const re=ne((()=>{const e=HTMLElement.prototype,t=e.setAttribute;e.setAttribute=function(e,n){if(e.startsWith("data-")&&this.tagName.startsWith("UNI-")){(this.__uniDataset||(this.__uniDataset={}))[oe(e)]=n}t.call(this,e,n)};const n=e.removeAttribute;e.removeAttribute=function(e){this.__uniDataset&&e.startsWith("data-")&&this.tagName.startsWith("UNI-")&&delete this.__uniDataset[oe(e)],n.call(this,e)}}));function ie(e){return w({},e.dataset,e.__uniDataset)}const se=new RegExp("\"[^\"]+\"|'[^']+'|url\\([^)]+\\)|(\\d*\\.?\\d+)[r|u]px","g");function ae(e){return{passive:e}}function le(e){const{id:t,offsetTop:n,offsetLeft:o}=e;return{id:t,dataset:ie(e),offsetTop:n,offsetLeft:o}}function ce(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}function ue(e={}){const t={};return Object.keys(e).forEach((n=>{try{t[n]=ce(e[n])}catch(o){t[n]=e[n]}})),t}const de=/\+/g;function fe(e){const t={};if(""===e||"?"===e)return t;const n=("?"===e[0]?e.slice(1):e).split("&");for(let o=0;o<n.length;++o){const e=n[o].replace(de," ");let r=e.indexOf("="),i=ce(r<0?e:e.slice(0,r)),s=r<0?null:ce(e.slice(r+1));if(i in t){let e=t[i];k(e)||(e=t[i]=[e]),e.push(s)}else t[i]=s}return t}function pe(e,t,{clearTimeout:n,setTimeout:o}){let r;const i=function(){n(r);const i=()=>e.apply(this,arguments);r=o(i,t)};return i.cancel=function(){n(r)},i}class he{constructor(e,t){this.id=e,this.listener={},this.emitCache=[],t&&Object.keys(t).forEach((e=>{this.on(e,t[e])}))}emit(e,...t){const n=this.listener[e];if(!n)return this.emitCache.push({eventName:e,args:t});n.forEach((e=>{e.fn.apply(e.fn,t)})),this.listener[e]=n.filter((e=>"once"!==e.type))}on(e,t){this._addListener(e,"on",t),this._clearCache(e)}once(e,t){this._addListener(e,"once",t),this._clearCache(e)}off(e,t){const n=this.listener[e];if(n)if(t)for(let o=0;o<n.length;)n[o].fn===t&&(n.splice(o,1),o--),o++;else delete this.listener[e]}_clearCache(e){for(let t=0;t<this.emitCache.length;t++){const n=this.emitCache[t],o=e?n.eventName===e?e:null:n.eventName;if(!o)continue;"number"!=typeof this.emit.apply(this,[o,...n.args])?(this.emitCache.splice(t,1),t--):this.emitCache.pop()}}_addListener(e,t,n){(this.listener[e]||(this.listener[e]=[])).push({fn:n,type:t})}}const ge=["onInit","onLoad","onShow","onHide","onUnload","onBackPress","onPageScroll","onTabItemTap","onReachBottom","onPullDownRefresh","onShareTimeline","onShareAppMessage","onAddToFavorites","onSaveExitState","onNavigationBarButtonTap","onNavigationBarSearchInputClicked","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputFocusChanged"],me=["onLoad","onShow"];const ve=["onShow","onHide","onLaunch","onError","onThemeChange","onPageNotFound","onUnhandledRejection","onInit","onLoad","onReady","onUnload","onResize","onBackPress","onPageScroll","onTabItemTap","onReachBottom","onPullDownRefresh","onShareTimeline","onAddToFavorites","onShareAppMessage","onSaveExitState","onNavigationBarButtonTap","onNavigationBarSearchInputClicked","onNavigationBarSearchInputChanged","onNavigationBarSearchInputConfirmed","onNavigationBarSearchInputFocusChanged"];const ye=[];const be=ne(((e,t)=>{if(O(e._component.onError))return t(e)})),_e=function(){};_e.prototype={on:function(e,t,n){var o=this.e||(this.e={});return(o[e]||(o[e]=[])).push({fn:t,ctx:n}),this},once:function(e,t,n){var o=this;function r(){o.off(e,r),t.apply(n,arguments)}return r._=t,this.on(e,r,n)},emit:function(e){for(var t=[].slice.call(arguments,1),n=((this.e||(this.e={}))[e]||[]).slice(),o=0,r=n.length;o<r;o++)n[o].fn.apply(n[o].ctx,t);return this},off:function(e,t){var n=this.e||(this.e={}),o=n[e],r=[];if(o&&t)for(var i=0,s=o.length;i<s;i++)o[i].fn!==t&&o[i].fn._!==t&&r.push(o[i]);return r.length?n[e]=r:delete n[e],this}};var we=_e;const xe={black:"rgba(0,0,0,0.4)",white:"rgba(255,255,255,0.4)"};function Se(e,t={},n="light"){const o=t[n],r={};return o?(Object.keys(e).forEach((i=>{let s=e[i];r[i]=(()=>{if(I(s))return Se(s,t,n);if(k(s))return s.map((e=>I(e)?Se(e,t,n):e));if(L(s)&&s.startsWith("@")){const t=s.replace("@","");let n=o[t]||s;switch(i){case"titleColor":n="black"===n?"#000000":"#ffffff";break;case"borderStyle":n=(e=n)&&e in xe?xe[e]:e}return n}var e;return s})()})),r):e}let Ce;class ke{constructor(e=!1){this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this.parent=Ce,!e&&Ce&&(this.index=(Ce.scopes||(Ce.scopes=[])).push(this)-1)}get active(){return this._active}run(e){if(this._active){const t=Ce;try{return Ce=this,e()}finally{Ce=t}}}on(){Ce=this}off(){Ce=this.parent}stop(e){if(this._active){let t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){const e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0,this._active=!1}}}const Ee=e=>{const t=new Set(e);return t.w=0,t.n=0,t},Te=e=>(e.w&Pe)>0,$e=e=>(e.n&Pe)>0,Oe=new WeakMap;let Le=0,Pe=1;let Ae;const Ne=Symbol(""),je=Symbol("");class Re{constructor(e,t=null,n){this.fn=e,this.scheduler=t,this.active=!0,this.deps=[],this.parent=void 0,function(e,t=Ce){t&&t.active&&t.effects.push(e)}(this,n)}run(){if(!this.active)return this.fn();let e=Ae,t=Be;for(;e;){if(e===this)return;e=e.parent}try{return this.parent=Ae,Ae=this,Be=!0,Pe=1<<++Le,Le<=30?(({deps:e})=>{if(e.length)for(let t=0;t<e.length;t++)e[t].w|=Pe})(this):Ie(this),this.fn()}finally{Le<=30&&(e=>{const{deps:t}=e;if(t.length){let n=0;for(let o=0;o<t.length;o++){const r=t[o];Te(r)&&!$e(r)?r.delete(e):t[n++]=r,r.w&=~Pe,r.n&=~Pe}t.length=n}})(this),Pe=1<<--Le,Ae=this.parent,Be=t,this.parent=void 0,this.deferStop&&this.stop()}}stop(){Ae===this?this.deferStop=!0:this.active&&(Ie(this),this.onStop&&this.onStop(),this.active=!1)}}function Ie(e){const{deps:t}=e;if(t.length){for(let n=0;n<t.length;n++)t[n].delete(e);t.length=0}}let Be=!0;const Me=[];function Fe(){Me.push(Be),Be=!1}function Ve(){const e=Me.pop();Be=void 0===e||e}function We(e,t,n){if(Be&&Ae){let t=Oe.get(e);t||Oe.set(e,t=new Map);let o=t.get(n);o||t.set(n,o=Ee()),He(o)}}function He(e,t){let n=!1;Le<=30?$e(e)||(e.n|=Pe,n=!Te(e)):n=!e.has(Ae),n&&(e.add(Ae),Ae.deps.push(e))}function Ue(e,t,n,o,r,i){const s=Oe.get(e);if(!s)return;let a=[];if("clear"===t)a=[...s.values()];else if("length"===n&&k(e)){const e=Number(o);s.forEach(((t,n)=>{("length"===n||n>=e)&&a.push(t)}))}else switch(void 0!==n&&a.push(s.get(n)),t){case"add":k(e)?B(n)&&a.push(s.get("length")):(a.push(s.get(Ne)),E(e)&&a.push(s.get(je)));break;case"delete":k(e)||(a.push(s.get(Ne)),E(e)&&a.push(s.get(je)));break;case"set":E(e)&&a.push(s.get(Ne))}if(1===a.length)a[0]&&De(a[0]);else{const e=[];for(const t of a)t&&e.push(...t);De(Ee(e))}}function De(e,t){const n=k(e)?e:[...e];for(const o of n)o.computed&&ze(o);for(const o of n)o.computed||ze(o)}function ze(e,t){(e!==Ae||e.allowRecurse)&&(e.scheduler?e.scheduler():e.run())}const qe=t("__proto__,__v_isRef,__isVue"),Xe=new Set(Object.getOwnPropertyNames(Symbol).filter((e=>"arguments"!==e&&"caller"!==e)).map((e=>Symbol[e])).filter(P)),Ye=et(),Ke=et(!1,!0),Je=et(!0),Ze=Ge();function Ge(){const e={};return["includes","indexOf","lastIndexOf"].forEach((t=>{e[t]=function(...e){const n=Mt(this);for(let t=0,r=this.length;t<r;t++)We(n,0,t+"");const o=n[t](...e);return-1===o||!1===o?n[t](...e.map(Mt)):o}})),["push","pop","shift","unshift","splice"].forEach((t=>{e[t]=function(...e){Fe();const n=Mt(this)[t].apply(this,e);return Ve(),n}})),e}function Qe(e){const t=Mt(this);return We(t,0,e),t.hasOwnProperty(e)}function et(e=!1,t=!1){return function(n,o,r){if("__v_isReactive"===o)return!e;if("__v_isReadonly"===o)return e;if("__v_isShallow"===o)return t;if("__v_raw"===o&&r===(e?t?Ot:$t:t?Tt:Et).get(n))return n;const i=k(n);if(!e){if(i&&C(Ze,o))return Reflect.get(Ze,o,r);if("hasOwnProperty"===o)return Qe}const s=Reflect.get(n,o,r);return(P(o)?Xe.has(o):qe(o))?s:(e||We(n,0,o),t?s:Dt(s)?i&&B(o)?s:s.value:A(s)?e?At(s):Pt(s):s)}}function tt(e=!1){return function(t,n,o,r){let i=t[n];if(Rt(i)&&Dt(i)&&!Dt(o))return!1;if(!e&&(It(o)||Rt(o)||(i=Mt(i),o=Mt(o)),!k(t)&&Dt(i)&&!Dt(o)))return i.value=o,!0;const s=k(t)&&B(n)?Number(n)<t.length:C(t,n),a=Reflect.set(t,n,o,r);return t===Mt(r)&&(s?q(o,i)&&Ue(t,"set",n,o):Ue(t,"add",n,o)),a}}const nt={get:Ye,set:tt(),deleteProperty:function(e,t){const n=C(e,t);e[t];const o=Reflect.deleteProperty(e,t);return o&&n&&Ue(e,"delete",t,void 0),o},has:function(e,t){const n=Reflect.has(e,t);return P(t)&&Xe.has(t)||We(e,0,t),n},ownKeys:function(e){return We(e,0,k(e)?"length":Ne),Reflect.ownKeys(e)}},ot={get:Je,set:(e,t)=>!0,deleteProperty:(e,t)=>!0},rt=w({},nt,{get:Ke,set:tt(!0)}),it=e=>e,st=e=>Reflect.getPrototypeOf(e);function at(e,t,n=!1,o=!1){const r=Mt(e=e.__v_raw),i=Mt(t);n||(t!==i&&We(r,0,t),We(r,0,i));const{has:s}=st(r),a=o?it:n?Wt:Vt;return s.call(r,t)?a(e.get(t)):s.call(r,i)?a(e.get(i)):void(e!==r&&e.get(t))}function lt(e,t=!1){const n=this.__v_raw,o=Mt(n),r=Mt(e);return t||(e!==r&&We(o,0,e),We(o,0,r)),e===r?n.has(e):n.has(e)||n.has(r)}function ct(e,t=!1){return e=e.__v_raw,!t&&We(Mt(e),0,Ne),Reflect.get(e,"size",e)}function ut(e){e=Mt(e);const t=Mt(this);return st(t).has.call(t,e)||(t.add(e),Ue(t,"add",e,e)),this}function dt(e,t){t=Mt(t);const n=Mt(this),{has:o,get:r}=st(n);let i=o.call(n,e);i||(e=Mt(e),i=o.call(n,e));const s=r.call(n,e);return n.set(e,t),i?q(t,s)&&Ue(n,"set",e,t):Ue(n,"add",e,t),this}function ft(e){const t=Mt(this),{has:n,get:o}=st(t);let r=n.call(t,e);r||(e=Mt(e),r=n.call(t,e)),o&&o.call(t,e);const i=t.delete(e);return r&&Ue(t,"delete",e,void 0),i}function pt(){const e=Mt(this),t=0!==e.size,n=e.clear();return t&&Ue(e,"clear",void 0,void 0),n}function ht(e,t){return function(n,o){const r=this,i=r.__v_raw,s=Mt(i),a=t?it:e?Wt:Vt;return!e&&We(s,0,Ne),i.forEach(((e,t)=>n.call(o,a(e),a(t),r)))}}function gt(e,t,n){return function(...o){const r=this.__v_raw,i=Mt(r),s=E(i),a="entries"===e||e===Symbol.iterator&&s,l="keys"===e&&s,c=r[e](...o),u=n?it:t?Wt:Vt;return!t&&We(i,0,l?je:Ne),{next(){const{value:e,done:t}=c.next();return t?{value:e,done:t}:{value:a?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}function mt(e){return function(...t){return"delete"!==e&&this}}function vt(){const e={get(e){return at(this,e)},get size(){return ct(this)},has:lt,add:ut,set:dt,delete:ft,clear:pt,forEach:ht(!1,!1)},t={get(e){return at(this,e,!1,!0)},get size(){return ct(this)},has:lt,add:ut,set:dt,delete:ft,clear:pt,forEach:ht(!1,!0)},n={get(e){return at(this,e,!0)},get size(){return ct(this,!0)},has(e){return lt.call(this,e,!0)},add:mt("add"),set:mt("set"),delete:mt("delete"),clear:mt("clear"),forEach:ht(!0,!1)},o={get(e){return at(this,e,!0,!0)},get size(){return ct(this,!0)},has(e){return lt.call(this,e,!0)},add:mt("add"),set:mt("set"),delete:mt("delete"),clear:mt("clear"),forEach:ht(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((r=>{e[r]=gt(r,!1,!1),n[r]=gt(r,!0,!1),t[r]=gt(r,!1,!0),o[r]=gt(r,!0,!0)})),[e,n,t,o]}const[yt,bt,_t,wt]=vt();function xt(e,t){const n=t?e?wt:_t:e?bt:yt;return(t,o,r)=>"__v_isReactive"===o?!e:"__v_isReadonly"===o?e:"__v_raw"===o?t:Reflect.get(C(n,o)&&o in t?n:t,o,r)}const St={get:xt(!1,!1)},Ct={get:xt(!1,!0)},kt={get:xt(!0,!1)},Et=new WeakMap,Tt=new WeakMap,$t=new WeakMap,Ot=new WeakMap;function Lt(e){return e.__v_skip||!Object.isExtensible(e)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}((e=>R(e).slice(8,-1))(e))}function Pt(e){return Rt(e)?e:Nt(e,!1,nt,St,Et)}function At(e){return Nt(e,!0,ot,kt,$t)}function Nt(e,t,n,o,r){if(!A(e))return e;if(e.__v_raw&&(!t||!e.__v_isReactive))return e;const i=r.get(e);if(i)return i;const s=Lt(e);if(0===s)return e;const a=new Proxy(e,2===s?o:n);return r.set(e,a),a}function jt(e){return Rt(e)?jt(e.__v_raw):!(!e||!e.__v_isReactive)}function Rt(e){return!(!e||!e.__v_isReadonly)}function It(e){return!(!e||!e.__v_isShallow)}function Bt(e){return jt(e)||Rt(e)}function Mt(e){const t=e&&e.__v_raw;return t?Mt(t):e}function Ft(e){return Y(e,"__v_skip",!0),e}const Vt=e=>A(e)?Pt(e):e,Wt=e=>A(e)?At(e):e;function Ht(e){Be&&Ae&&He((e=Mt(e)).dep||(e.dep=Ee()))}function Ut(e,t){const n=(e=Mt(e)).dep;n&&De(n)}function Dt(e){return!(!e||!0!==e.__v_isRef)}function zt(e){return function(e,t){if(Dt(e))return e;return new qt(e,t)}(e,!1)}class qt{constructor(e,t){this.__v_isShallow=t,this.dep=void 0,this.__v_isRef=!0,this._rawValue=t?e:Mt(e),this._value=t?e:Vt(e)}get value(){return Ht(this),this._value}set value(e){const t=this.__v_isShallow||It(e)||Rt(e);e=t?e:Mt(e),q(e,this._rawValue)&&(this._rawValue=e,this._value=t?e:Vt(e),Ut(this))}}const Xt={get:(e,t,n)=>{return Dt(o=Reflect.get(e,t,n))?o.value:o;var o},set:(e,t,n,o)=>{const r=e[t];return Dt(r)&&!Dt(n)?(r.value=n,!0):Reflect.set(e,t,n,o)}};function Yt(e){return jt(e)?e:new Proxy(e,Xt)}var Kt;class Jt{constructor(e,t,n,o){this._setter=t,this.dep=void 0,this.__v_isRef=!0,this[Kt]=!1,this._dirty=!0,this.effect=new Re(e,(()=>{this._dirty||(this._dirty=!0,Ut(this))})),this.effect.computed=this,this.effect.active=this._cacheable=!o,this.__v_isReadonly=n}get value(){const e=Mt(this);return Ht(e),!e._dirty&&e._cacheable||(e._dirty=!1,e._value=e.effect.run()),e._value}set value(e){this._setter(e)}}function Zt(e,t,n,o){let r;try{r=o?e(...o):e()}catch(i){Qt(i,t,n)}return r}function Gt(e,t,n,o){if(O(e)){const r=Zt(e,t,n,o);return r&&N(r)&&r.catch((e=>{Qt(e,t,n)})),r}const r=[];for(let i=0;i<e.length;i++)r.push(Gt(e[i],t,n,o));return r}function Qt(e,t,n,o=!0){t&&t.vnode;if(t){let o=t.parent;const r=t.proxy,i=n;for(;o;){const t=o.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,r,i))return;o=o.parent}const s=t.appContext.config.errorHandler;if(s)return void Zt(s,null,10,[e,r,i])}!function(e,t,n,o=!0){console.error(e)}(e,0,0,o)}Kt="__v_isReadonly";let en=!1,tn=!1;const nn=[];let on=0;const rn=[];let sn=null,an=0;const ln=Promise.resolve();let cn=null;function un(e){const t=cn||ln;return e?t.then(this?e.bind(this):e):t}function dn(e){nn.length&&nn.includes(e,en&&e.allowRecurse?on+1:on)||(null==e.id?nn.push(e):nn.splice(function(e){let t=on+1,n=nn.length;for(;t<n;){const o=t+n>>>1;gn(nn[o])<e?t=o+1:n=o}return t}(e.id),0,e),fn())}function fn(){en||tn||(tn=!0,cn=ln.then(vn))}function pn(e,t=(en?on+1:0)){for(;t<nn.length;t++){const e=nn[t];e&&e.pre&&(nn.splice(t,1),t--,e())}}function hn(e){if(rn.length){const e=[...new Set(rn)];if(rn.length=0,sn)return void sn.push(...e);for(sn=e,sn.sort(((e,t)=>gn(e)-gn(t))),an=0;an<sn.length;an++)sn[an]();sn=null,an=0}}const gn=e=>null==e.id?1/0:e.id,mn=(e,t)=>{const n=gn(e)-gn(t);if(0===n){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function vn(e){tn=!1,en=!0,nn.sort(mn);try{for(on=0;on<nn.length;on++){const e=nn[on];e&&!1!==e.active&&Zt(e,null,14)}}finally{on=0,nn.length=0,hn(),en=!1,cn=null,(nn.length||rn.length)&&vn()}}function yn(e,t,...n){if(e.isUnmounted)return;const o=e.vnode.props||h;let r=n;const i=t.startsWith("update:"),s=i&&t.slice(7);if(s&&s in o){const e=`${"modelValue"===s?"model":s}Modifiers`,{number:t,trim:i}=o[e]||h;i&&(r=n.map((e=>L(e)?e.trim():e))),t&&(r=n.map(K))}let a,l=o[a=z(t)]||o[a=z(W(t))];!l&&i&&(l=o[a=z(U(t))]),l&&Gt(l,e,6,bn(e,l,r));const c=o[a+"Once"];if(c){if(e.emitted){if(e.emitted[a])return}else e.emitted={};e.emitted[a]=!0,Gt(c,e,6,bn(e,c,r))}}function bn(e,t,n){if(1!==n.length)return n;if(O(t)){if(t.length<2)return n}else if(!t.find((e=>e.length>=2)))return n;const o=n[0];if(o&&C(o,"type")&&C(o,"timeStamp")&&C(o,"target")&&C(o,"currentTarget")&&C(o,"detail")){const t=e.proxy,o=t.$gcd(t,!0);o&&n.push(o)}return n}function _n(e,t,n=!1){const o=t.emitsCache,r=o.get(e);if(void 0!==r)return r;const i=e.emits;let s={},a=!1;if(!O(e)){const o=e=>{const n=_n(e,t,!0);n&&(a=!0,w(s,n))};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}return i||a?(k(i)?i.forEach((e=>s[e]=null)):w(s,i),A(e)&&o.set(e,s),s):(A(e)&&o.set(e,null),null)}function wn(e,t){return!(!e||!b(t))&&(t=t.slice(2).replace(/Once$/,""),C(e,t[0].toLowerCase()+t.slice(1))||C(e,U(t))||C(e,t))}let xn=null,Sn=null;function Cn(e){const t=xn;return xn=e,Sn=e&&e.type.__scopeId||null,t}function kn(e,t=xn,n){if(!t)return e;if(e._n)return e;const o=(...n)=>{o._d&&ir(-1);const r=Cn(t);let i;try{i=e(...n)}finally{Cn(r),o._d&&ir(1)}return i};return o._n=!0,o._c=!0,o._d=!0,o}function En(e){const{type:t,vnode:n,proxy:o,withProxy:r,props:i,propsOptions:[s],slots:a,attrs:l,emit:c,render:u,renderCache:d,data:f,setupState:p,ctx:h,inheritAttrs:g}=e;let m,v;const y=Cn(e);try{if(4&n.shapeFlag){const e=r||o;m=br(u.call(e,e,d,i,p,f,h)),v=l}else{const e=t;0,m=br(e.length>1?e(i,{attrs:l,slots:a,emit:c}):e(i,null)),v=t.props?l:Tn(l)}}catch(w){tr.length=0,Qt(w,e,1),m=gr(Qo)}let b=m;if(v&&!1!==g){const e=Object.keys(v),{shapeFlag:t}=b;e.length&&7&t&&(s&&e.some(_)&&(v=$n(v,s)),b=mr(b,v))}return n.dirs&&(b=mr(b),b.dirs=b.dirs?b.dirs.concat(n.dirs):n.dirs),n.transition&&(b.transition=n.transition),m=b,Cn(y),m}const Tn=e=>{let t;for(const n in e)("class"===n||"style"===n||b(n))&&((t||(t={}))[n]=e[n]);return t},$n=(e,t)=>{const n={};for(const o in e)_(o)&&o.slice(9)in t||(n[o]=e[o]);return n};function On(e,t,n){const o=Object.keys(t);if(o.length!==Object.keys(e).length)return!0;for(let r=0;r<o.length;r++){const i=o[r];if(t[i]!==e[i]&&!wn(n,i))return!0}return!1}function Ln(e,t){if(Er){let n=Er.provides;const o=Er.parent&&Er.parent.provides;o===n&&(n=Er.provides=Object.create(o)),n[e]=t,"app"===Er.type.mpType&&Er.appContext.app.provide(e,t)}else;}function Pn(e,t,n=!1){const o=Er||xn;if(o){const r=null==o.parent?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides;if(r&&e in r)return r[e];if(arguments.length>1)return n&&O(t)?t.call(o.proxy):t}}const An={};function Nn(e,t,n){return jn(e,t,n)}function jn(e,t,{immediate:n,deep:o,flush:r,onTrack:i,onTrigger:s}=h){const a=Ce===(null==Er?void 0:Er.scope)?Er:null;let l,c,u=!1,d=!1;if(Dt(e)?(l=()=>e.value,u=It(e)):jt(e)?(l=()=>e,o=!0):k(e)?(d=!0,u=e.some((e=>jt(e)||It(e))),l=()=>e.map((e=>Dt(e)?e.value:jt(e)?Bn(e):O(e)?Zt(e,a,2):void 0))):l=O(e)?t?()=>Zt(e,a,2):()=>{if(!a||!a.isUnmounted)return c&&c(),Gt(e,a,3,[p])}:m,t&&o){const e=l;l=()=>Bn(e())}let f,p=e=>{c=b.onStop=()=>{Zt(e,a,4)}};if(Pr){if(p=m,t?n&&Gt(t,a,3,[l(),d?[]:void 0,p]):l(),"sync"!==r)return m;{const e=Br();f=e.__watcherHandles||(e.__watcherHandles=[])}}let g=d?new Array(e.length).fill(An):An;const v=()=>{if(b.active)if(t){const e=b.run();(o||u||(d?e.some(((e,t)=>q(e,g[t]))):q(e,g)))&&(c&&c(),Gt(t,a,3,[e,g===An?void 0:d&&g[0]===An?[]:g,p]),g=e)}else b.run()};let y;v.allowRecurse=!!t,"sync"===r?y=v:"post"===r?y=()=>Xo(v,a&&a.suspense):(v.pre=!0,a&&(v.id=a.uid),y=()=>dn(v));const b=new Re(l,y);t?n?v():g=b.run():"post"===r?Xo(b.run.bind(b),a&&a.suspense):b.run();const _=()=>{b.stop(),a&&a.scope&&x(a.scope.effects,b)};return f&&f.push(_),_}function Rn(e,t,n){const o=this.proxy,r=L(e)?e.includes(".")?In(o,e):()=>o[e]:e.bind(o,o);let i;O(t)?i=t:(i=t.handler,n=t);const s=Er;$r(this);const a=jn(r,i.bind(o),n);return s?$r(s):Or(),a}function In(e,t){const n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}function Bn(e,t){if(!A(e)||e.__v_skip)return e;if((t=t||new Set).has(e))return e;if(t.add(e),Dt(e))Bn(e.value,t);else if(k(e))for(let n=0;n<e.length;n++)Bn(e[n],t);else if(T(e)||E(e))e.forEach((e=>{Bn(e,t)}));else if(I(e))for(const n in e)Bn(e[n],t);return e}function Mn(e){return O(e)?{setup:e,name:e.name}:e}const Fn=e=>!!e.type.__asyncLoader;function Vn(e,t){const{ref:n,props:o,children:r,ce:i}=t.vnode,s=gr(e,o,r);return s.ref=n,s.ce=i,delete t.vnode.ce,s}const Wn=e=>e.type.__isKeepAlive;function Hn(e,t){Dn(e,"a",t)}function Un(e,t){Dn(e,"da",t)}function Dn(e,t,n=Er){const o=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(o.__called=!1,qn(t,o,n),n){let e=n.parent;for(;e&&e.parent;)Wn(e.parent.vnode)&&zn(o,t,n,e),e=e.parent}}function zn(e,t,n,o){const r=qn(t,e,o,!0);Qn((()=>{x(o[t],r)}),n)}function qn(e,t,n=Er,o=!1){if(n){if(r=e,ge.indexOf(r)>-1&&n.$pageInstance){if(n.type.__reserved)return;if(n!==n.$pageInstance&&(n=n.$pageInstance,function(e){return me.indexOf(e)>-1}(e))){const o=n.proxy;Gt(t.bind(o),n,e,"onLoad"===e?[o.$page.options]:[])}}const i=n[e]||(n[e]=[]),s=t.__weh||(t.__weh=(...o)=>{if(n.isUnmounted)return;Fe(),$r(n);const r=Gt(t,n,e,o);return Or(),Ve(),r});return o?i.unshift(s):i.push(s),s}var r}const Xn=e=>(t,n=Er)=>(!Pr||"sp"===e)&&qn(e,((...e)=>t(...e)),n),Yn=Xn("bm"),Kn=Xn("m"),Jn=Xn("bu"),Zn=Xn("u"),Gn=Xn("bum"),Qn=Xn("um"),eo=Xn("sp"),to=Xn("rtg"),no=Xn("rtc");function oo(e,t=Er){qn("ec",e,t)}function ro(e,t){const n=xn;if(null===n)return e;const o=jr(n)||n.proxy,r=e.dirs||(e.dirs=[]);for(let i=0;i<t.length;i++){let[e,n,s,a=h]=t[i];e&&(O(e)&&(e={mounted:e,updated:e}),e.deep&&Bn(n),r.push({dir:e,instance:o,value:n,oldValue:void 0,arg:s,modifiers:a}))}return e}function io(e,t,n,o){const r=e.dirs,i=t&&t.dirs;for(let s=0;s<r.length;s++){const a=r[s];i&&(a.oldValue=i[s].value);let l=a.dir[o];l&&(Fe(),Gt(l,n,8,[e.el,a,e,t]),Ve())}}function so(e,t){return co("components",e,!0,t)||e}const ao=Symbol();function lo(e){return L(e)?co("components",e,!1)||e:e||ao}function co(e,t,n=!0,o=!1){const r=xn||Er;if(r){const n=r.type;if("components"===e){const e=function(e,t=!0){return O(e)?e.displayName||e.name:e.name||t&&e.__name}(n,!1);if(e&&(e===t||e===W(t)||e===D(W(t))))return n}const i=uo(r[e]||n[e],t)||uo(r.appContext[e],t);return!i&&o?n:i}}function uo(e,t){return e&&(e[t]||e[W(t)]||e[D(W(t))])}function fo(e,t,n,o){let r;const i=n&&n[o];if(k(e)||L(e)){r=new Array(e.length);for(let n=0,o=e.length;n<o;n++)r[n]=t(e[n],n,void 0,i&&i[n])}else if("number"==typeof e){r=new Array(e);for(let n=0;n<e;n++)r[n]=t(n+1,n,void 0,i&&i[n])}else if(A(e))if(e[Symbol.iterator])r=Array.from(e,((e,n)=>t(e,n,void 0,i&&i[n])));else{const n=Object.keys(e);r=new Array(n.length);for(let o=0,s=n.length;o<s;o++){const s=n[o];r[o]=t(e[s],s,o,i&&i[o])}}else r=[];return n&&(n[o]=r),r}function po(e,t,n={},o,r){if(xn.isCE||xn.parent&&Fn(xn.parent)&&xn.parent.isCE)return"default"!==t&&(n.name=t),gr("slot",n,o&&o());let i=e[t];i&&i._c&&(i._d=!1),or();const s=i&&ho(i(n)),a=lr(Zo,{key:n.key||s&&s.key||`_${t}`},s||(o?o():[]),s&&1===e._?64:-2);return!r&&a.scopeId&&(a.slotScopeIds=[a.scopeId+"-s"]),i&&i._c&&(i._d=!0),a}function ho(e){return e.some((e=>!cr(e)||e.type!==Qo&&!(e.type===Zo&&!ho(e.children))))?e:null}const go=e=>e?Lr(e)?jr(e)||e.proxy:go(e.parent):null,mo=w(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>go(e.parent),$root:e=>go(e.root),$emit:e=>e.emit,$options:e=>So(e),$forceUpdate:e=>e.f||(e.f=()=>dn(e.update)),$nextTick:e=>e.n||(e.n=un.bind(e.proxy)),$watch:e=>Rn.bind(e)}),vo=(e,t)=>e!==h&&!e.__isScriptSetup&&C(e,t),yo={get({_:e},t){const{ctx:n,setupState:o,data:r,props:i,accessCache:s,type:a,appContext:l}=e;let c;if("$"!==t[0]){const a=s[t];if(void 0!==a)switch(a){case 1:return o[t];case 2:return r[t];case 4:return n[t];case 3:return i[t]}else{if(vo(o,t))return s[t]=1,o[t];if(r!==h&&C(r,t))return s[t]=2,r[t];if((c=e.propsOptions[0])&&C(c,t))return s[t]=3,i[t];if(n!==h&&C(n,t))return s[t]=4,n[t];bo&&(s[t]=0)}}const u=mo[t];let d,f;return u?("$attrs"===t&&We(e,0,t),u(e)):(d=a.__cssModules)&&(d=d[t])?d:n!==h&&C(n,t)?(s[t]=4,n[t]):(f=l.config.globalProperties,C(f,t)?f[t]:void 0)},set({_:e},t,n){const{data:o,setupState:r,ctx:i}=e;return vo(r,t)?(r[t]=n,!0):o!==h&&C(o,t)?(o[t]=n,!0):!C(e.props,t)&&(("$"!==t[0]||!(t.slice(1)in e))&&(i[t]=n,!0))},has({_:{data:e,setupState:t,accessCache:n,ctx:o,appContext:r,propsOptions:i}},s){let a;return!!n[s]||e!==h&&C(e,s)||vo(t,s)||(a=i[0])&&C(a,s)||C(o,s)||C(mo,s)||C(r.config.globalProperties,s)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:C(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};let bo=!0;function _o(e){const t=So(e),n=e.proxy,o=e.ctx;bo=!1,t.beforeCreate&&wo(t.beforeCreate,e,"bc");const{data:r,computed:i,methods:s,watch:a,provide:l,inject:c,created:u,beforeMount:d,mounted:f,beforeUpdate:p,updated:h,activated:g,deactivated:v,beforeDestroy:y,beforeUnmount:b,destroyed:_,unmounted:w,render:x,renderTracked:S,renderTriggered:C,errorCaptured:E,serverPrefetch:T,expose:$,inheritAttrs:L,components:P,directives:N,filters:j}=t;if(c&&function(e,t,n=m,o=!1){k(e)&&(e=To(e));for(const r in e){const n=e[r];let i;i=A(n)?"default"in n?Pn(n.from||r,n.default,!0):Pn(n.from||r):Pn(n),Dt(i)&&o?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>i.value,set:e=>i.value=e}):t[r]=i}}(c,o,null,e.appContext.config.unwrapInjectedRef),s)for(const m in s){const e=s[m];O(e)&&(o[m]=e.bind(n))}if(r){const t=r.call(n,n);A(t)&&(e.data=Pt(t))}if(bo=!0,i)for(const k in i){const e=i[k],t=O(e)?e.bind(n,n):O(e.get)?e.get.bind(n,n):m,r=!O(e)&&O(e.set)?e.set.bind(n):m,s=Rr({get:t,set:r});Object.defineProperty(o,k,{enumerable:!0,configurable:!0,get:()=>s.value,set:e=>s.value=e})}if(a)for(const m in a)xo(a[m],o,n,m);if(l){const e=O(l)?l.call(n):l;Reflect.ownKeys(e).forEach((t=>{Ln(t,e[t])}))}function R(e,t){k(t)?t.forEach((t=>e(t.bind(n)))):t&&e(t.bind(n))}if(u&&wo(u,e,"c"),R(Yn,d),R(Kn,f),R(Jn,p),R(Zn,h),R(Hn,g),R(Un,v),R(oo,E),R(no,S),R(to,C),R(Gn,b),R(Qn,w),R(eo,T),k($))if($.length){const t=e.exposed||(e.exposed={});$.forEach((e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})}))}else e.exposed||(e.exposed={});x&&e.render===m&&(e.render=x),null!=L&&(e.inheritAttrs=L),P&&(e.components=P),N&&(e.directives=N);const I=e.appContext.config.globalProperties.$applyOptions;I&&I(t,e,n)}function wo(e,t,n){Gt(k(e)?e.map((e=>e.bind(t.proxy))):e.bind(t.proxy),t,n)}function xo(e,t,n,o){const r=o.includes(".")?In(n,o):()=>n[o];if(L(e)){const n=t[e];O(n)&&Nn(r,n)}else if(O(e))Nn(r,e.bind(n));else if(A(e))if(k(e))e.forEach((e=>xo(e,t,n,o)));else{const o=O(e.handler)?e.handler.bind(n):t[e.handler];O(o)&&Nn(r,o,e)}}function So(e){const t=e.type,{mixins:n,extends:o}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:s}}=e.appContext,a=i.get(t);let l;return a?l=a:r.length||n||o?(l={},r.length&&r.forEach((e=>Co(l,e,s,!0))),Co(l,t,s)):l=t,A(t)&&i.set(t,l),l}function Co(e,t,n,o=!1){const{mixins:r,extends:i}=t;i&&Co(e,i,n,!0),r&&r.forEach((t=>Co(e,t,n,!0)));for(const s in t)if(o&&"expose"===s);else{const o=ko[s]||n&&n[s];e[s]=o?o(e[s],t[s]):t[s]}return e}const ko={data:Eo,props:Oo,emits:Oo,methods:Oo,computed:Oo,beforeCreate:$o,created:$o,beforeMount:$o,mounted:$o,beforeUpdate:$o,updated:$o,beforeDestroy:$o,beforeUnmount:$o,destroyed:$o,unmounted:$o,activated:$o,deactivated:$o,errorCaptured:$o,serverPrefetch:$o,components:Oo,directives:Oo,watch:function(e,t){if(!e)return t;if(!t)return e;const n=w(Object.create(null),e);for(const o in t)n[o]=$o(e[o],t[o]);return n},provide:Eo,inject:function(e,t){return Oo(To(e),To(t))}};function Eo(e,t){return t?e?function(){return w(O(e)?e.call(this,this):e,O(t)?t.call(this,this):t)}:t:e}function To(e){if(k(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function $o(e,t){return e?[...new Set([].concat(e,t))]:t}function Oo(e,t){return e?w(w(Object.create(null),e),t):t}function Lo(e,t,n,o=!1){const r={},i={};Y(i,dr,1),e.propsDefaults=Object.create(null),Po(e,t,r,i);for(const s in e.propsOptions[0])s in r||(r[s]=void 0);n?e.props=o?r:Nt(r,!1,rt,Ct,Tt):e.type.props?e.props=r:e.props=i,e.attrs=i}function Po(e,t,n,o){const[r,i]=e.propsOptions;let s,a=!1;if(t)for(let l in t){if(M(l))continue;const c=t[l];let u;r&&C(r,u=W(l))?i&&i.includes(u)?(s||(s={}))[u]=c:n[u]=c:wn(e.emitsOptions,l)||l in o&&c===o[l]||(o[l]=c,a=!0)}if(i){const t=Mt(n),o=s||h;for(let s=0;s<i.length;s++){const a=i[s];n[a]=Ao(r,t,a,o[a],e,!C(o,a))}}return a}function Ao(e,t,n,o,r,i){const s=e[n];if(null!=s){const e=C(s,"default");if(e&&void 0===o){const e=s.default;if(s.type!==Function&&O(e)){const{propsDefaults:i}=r;n in i?o=i[n]:($r(r),o=i[n]=e.call(null,t),Or())}else o=e}s[0]&&(i&&!e?o=!1:!s[1]||""!==o&&o!==U(n)||(o=!0))}return o}function No(e,t,n=!1){const o=t.propsCache,r=o.get(e);if(r)return r;const i=e.props,s={},a=[];let l=!1;if(!O(e)){const o=e=>{l=!0;const[n,o]=No(e,t,!0);w(s,n),o&&a.push(...o)};!n&&t.mixins.length&&t.mixins.forEach(o),e.extends&&o(e.extends),e.mixins&&e.mixins.forEach(o)}if(!i&&!l)return A(e)&&o.set(e,g),g;if(k(i))for(let u=0;u<i.length;u++){const e=W(i[u]);jo(e)&&(s[e]=h)}else if(i)for(const u in i){const e=W(u);if(jo(e)){const t=i[u],n=s[e]=k(t)||O(t)?{type:t}:Object.assign({},t);if(n){const t=Bo(Boolean,n.type),o=Bo(String,n.type);n[0]=t>-1,n[1]=o<0||t<o,(t>-1||C(n,"default"))&&a.push(e)}}}const c=[s,a];return A(e)&&o.set(e,c),c}function jo(e){return"$"!==e[0]}function Ro(e){const t=e&&e.toString().match(/^\s*(function|class) (\w+)/);return t?t[2]:null===e?"null":""}function Io(e,t){return Ro(e)===Ro(t)}function Bo(e,t){return k(t)?t.findIndex((t=>Io(t,e))):O(t)&&Io(t,e)?0:-1}const Mo=e=>"_"===e[0]||"$stable"===e,Fo=e=>k(e)?e.map(br):[br(e)],Vo=(e,t,n)=>{if(t._n)return t;const o=kn(((...e)=>Fo(t(...e))),n);return o._c=!1,o},Wo=(e,t,n)=>{const o=e._ctx;for(const r in e){if(Mo(r))continue;const n=e[r];if(O(n))t[r]=Vo(0,n,o);else if(null!=n){const e=Fo(n);t[r]=()=>e}}},Ho=(e,t)=>{const n=Fo(t);e.slots.default=()=>n};function Uo(){return{app:null,config:{isNativeTag:v,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Do=0;function zo(e,t){return function(n,o=null){O(n)||(n=Object.assign({},n)),null==o||A(o)||(o=null);const r=Uo(),i=new Set;let s=!1;const a=r.app={_uid:Do++,_component:n,_props:o,_container:null,_context:r,_instance:null,version:Mr,get config(){return r.config},set config(e){},use:(e,...t)=>(i.has(e)||(e&&O(e.install)?(i.add(e),e.install(a,...t)):O(e)&&(i.add(e),e(a,...t))),a),mixin:e=>(r.mixins.includes(e)||r.mixins.push(e),a),component:(e,t)=>t?(r.components[e]=t,a):r.components[e],directive:(e,t)=>t?(r.directives[e]=t,a):r.directives[e],mount(i,l,c){if(!s){const u=gr(n,o);return u.appContext=r,l&&t?t(u,i):e(u,i,c),s=!0,a._container=i,i.__vue_app__=a,a._instance=u.component,jr(u.component)||u.component.proxy}},unmount(){s&&(e(null,a._container),delete a._container.__vue_app__)},provide:(e,t)=>(r.provides[e]=t,a)};return a}}function qo(e,t,n,o,r=!1){if(k(e))return void e.forEach(((e,i)=>qo(e,t&&(k(t)?t[i]:t),n,o,r)));if(Fn(o)&&!r)return;const i=4&o.shapeFlag?jr(o.component)||o.component.proxy:o.el,s=r?null:i,{i:a,r:l}=e,c=t&&t.r,u=a.refs===h?a.refs={}:a.refs,d=a.setupState;if(null!=c&&c!==l&&(L(c)?(u[c]=null,C(d,c)&&(d[c]=null)):Dt(c)&&(c.value=null)),O(l))Zt(l,a,12,[s,u]);else{const t=L(l),o=Dt(l);if(t||o){const a=()=>{if(e.f){const n=t?C(d,l)?d[l]:u[l]:l.value;r?k(n)&&x(n,i):k(n)?n.includes(i)||n.push(i):t?(u[l]=[i],C(d,l)&&(d[l]=u[l])):(l.value=[i],e.k&&(u[e.k]=l.value))}else t?(u[l]=s,C(d,l)&&(d[l]=s)):o&&(l.value=s,e.k&&(u[e.k]=s))};s?(a.id=-1,Xo(a,n)):a()}}}const Xo=function(e,t){var n;t&&t.pendingBranch?k(e)?t.effects.push(...e):t.effects.push(e):(k(n=e)?rn.push(...n):sn&&sn.includes(n,n.allowRecurse?an+1:an)||rn.push(n),fn())};function Yo(e){return function(e,t){(J||(J="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{})).__VUE__=!0;const{insert:n,remove:o,patchProp:r,forcePatchProp:i,createElement:s,createText:a,createComment:l,setText:c,setElementText:u,parentNode:d,nextSibling:f,setScopeId:p=m,insertStaticContent:v}=e,y=(e,t,n,o=null,r=null,i=null,s=!1,a=null,l=!!t.dynamicChildren)=>{if(e===t)return;e&&!ur(e,t)&&(o=te(e),K(e,r,i,!0),e=null),-2===t.patchFlag&&(l=!1,t.dynamicChildren=null);const{type:c,ref:u,shapeFlag:d}=t;switch(c){case Go:b(e,t,n,o);break;case Qo:_(e,t,n,o);break;case er:null==e&&x(t,n,o,s);break;case Zo:j(e,t,n,o,r,i,s,a,l);break;default:1&d?E(e,t,n,o,r,i,s,a,l):6&d?R(e,t,n,o,r,i,s,a,l):(64&d||128&d)&&c.process(e,t,n,o,r,i,s,a,l,oe)}null!=u&&r&&qo(u,e&&e.ref,i,t||e,!t)},b=(e,t,o,r)=>{if(null==e)n(t.el=a(t.children),o,r);else{const n=t.el=e.el;t.children!==e.children&&c(n,t.children)}},_=(e,t,o,r)=>{null==e?n(t.el=l(t.children||""),o,r):t.el=e.el},x=(e,t,n,o)=>{[e.el,e.anchor]=v(e.children,t,n,o,e.el,e.anchor)},S=({el:e,anchor:t},o,r)=>{let i;for(;e&&e!==t;)i=f(e),n(e,o,r),e=i;n(t,o,r)},k=({el:e,anchor:t})=>{let n;for(;e&&e!==t;)n=f(e),o(e),e=n;o(t)},E=(e,t,n,o,r,i,s,a,l)=>{s=s||"svg"===t.type,null==e?T(t,n,o,r,i,s,a,l):L(e,t,r,i,s,a,l)},T=(e,t,o,i,a,l,c,d)=>{let f,p;const{type:h,props:g,shapeFlag:m,transition:v,dirs:y}=e;if(f=e.el=s(e.type,l,g&&g.is,g),8&m?u(f,e.children):16&m&&O(e.children,f,null,i,a,l&&"foreignObject"!==h,c,d),y&&io(e,null,i,"created"),$(f,e,e.scopeId,c,i),g){for(const t in g)"value"===t||M(t)||r(f,t,null,g[t],l,e.children,i,a,ee);"value"in g&&r(f,"value",null,g.value),(p=g.onVnodeBeforeMount)&&Sr(p,i,e)}Object.defineProperty(f,"__vueParentComponent",{value:i,enumerable:!1}),y&&io(e,null,i,"beforeMount");const b=(!a||a&&!a.pendingBranch)&&v&&!v.persisted;b&&v.beforeEnter(f),n(f,t,o),((p=g&&g.onVnodeMounted)||b||y)&&Xo((()=>{p&&Sr(p,i,e),b&&v.enter(f),y&&io(e,null,i,"mounted")}),a)},$=(e,t,n,o,r)=>{if(n&&p(e,n),o)for(let i=0;i<o.length;i++)p(e,o[i]);if(r){if(t===r.subTree){const t=r.vnode;$(e,t,t.scopeId,t.slotScopeIds,r.parent)}}},O=(e,t,n,o,r,i,s,a,l=0)=>{for(let c=l;c<e.length;c++){const l=e[c]=a?_r(e[c]):br(e[c]);y(null,l,t,n,o,r,i,s,a)}},L=(e,t,n,o,s,a,l)=>{const c=t.el=e.el;let{patchFlag:d,dynamicChildren:f,dirs:p}=t;d|=16&e.patchFlag;const g=e.props||h,m=t.props||h;let v;n&&Ko(n,!1),(v=m.onVnodeBeforeUpdate)&&Sr(v,n,t,e),p&&io(t,e,n,"beforeUpdate"),n&&Ko(n,!0);const y=s&&"foreignObject"!==t.type;if(f?P(e.dynamicChildren,f,c,n,o,y,a):l||H(e,t,c,null,n,o,y,a,!1),d>0){if(16&d)A(c,t,g,m,n,o,s);else if(2&d&&g.class!==m.class&&r(c,"class",null,m.class,s),4&d&&r(c,"style",g.style,m.style,s),8&d){const a=t.dynamicProps;for(let t=0;t<a.length;t++){const l=a[t],u=g[l],d=m[l];(d!==u||"value"===l||i&&i(c,l))&&r(c,l,u,d,s,e.children,n,o,ee)}}1&d&&e.children!==t.children&&u(c,t.children)}else l||null!=f||A(c,t,g,m,n,o,s);((v=m.onVnodeUpdated)||p)&&Xo((()=>{v&&Sr(v,n,t,e),p&&io(t,e,n,"updated")}),o)},P=(e,t,n,o,r,i,s)=>{for(let a=0;a<t.length;a++){const l=e[a],c=t[a],u=l.el&&(l.type===Zo||!ur(l,c)||70&l.shapeFlag)?d(l.el):n;y(l,c,u,null,o,r,i,s,!0)}},A=(e,t,n,o,s,a,l)=>{if(n!==o){if(n!==h)for(const i in n)M(i)||i in o||r(e,i,n[i],null,l,t.children,s,a,ee);for(const c in o){if(M(c))continue;const u=o[c],d=n[c];(u!==d&&"value"!==c||i&&i(e,c))&&r(e,c,d,u,l,t.children,s,a,ee)}"value"in o&&r(e,"value",n.value,o.value)}},j=(e,t,o,r,i,s,l,c,u)=>{const d=t.el=e?e.el:a(""),f=t.anchor=e?e.anchor:a("");let{patchFlag:p,dynamicChildren:h,slotScopeIds:g}=t;g&&(c=c?c.concat(g):g),null==e?(n(d,o,r),n(f,o,r),O(t.children,o,f,i,s,l,c,u)):p>0&&64&p&&h&&e.dynamicChildren?(P(e.dynamicChildren,h,o,i,s,l,c),(null!=t.key||i&&t===i.subTree)&&Jo(e,t,!0)):H(e,t,o,f,i,s,l,c,u)},R=(e,t,n,o,r,i,s,a,l)=>{t.slotScopeIds=a,null==e?512&t.shapeFlag?r.ctx.activate(t,n,o,s,l):I(t,n,o,r,i,s,l):B(e,t,l)},I=(e,t,n,o,r,i,s)=>{const a=e.component=function(e,t,n){const o=e.type,r=(t?t.appContext:e.appContext)||Cr,i={uid:kr++,vnode:e,type:o,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,scope:new ke(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:No(o,r),emitsOptions:_n(o,r),emit:null,emitted:null,propsDefaults:h,inheritAttrs:o.inheritAttrs,ctx:h,data:h,props:h,attrs:h,slots:h,refs:h,setupState:h,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,bda:null,da:null,ba:null,a:null,rtg:null,rtc:null,ec:null,sp:null};i.ctx={_:i},i.root=t?t.root:i,i.emit=yn.bind(null,i),i.$pageInstance=t&&t.$pageInstance,e.ce&&e.ce(i);return i}(e,o,r);if(Wn(e)&&(a.ctx.renderer=oe),function(e,t=!1){Pr=t;const{props:n,children:o}=e.vnode,r=Lr(e);Lo(e,n,r,t),((e,t)=>{if(32&e.vnode.shapeFlag){const n=t._;n?(e.slots=Mt(t),Y(t,"_",n)):Wo(t,e.slots={})}else e.slots={},t&&Ho(e,t);Y(e.slots,dr,1)})(e,o);const i=r?function(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=Ft(new Proxy(e.ctx,yo));const{setup:o}=n;if(o){const n=e.setupContext=o.length>1?function(e){const t=t=>{e.exposed=t||{}};let n;return{get attrs(){return n||(n=function(e){return new Proxy(e.attrs,{get:(t,n)=>(We(e,0,"$attrs"),t[n])})}(e))},slots:e.slots,emit:e.emit,expose:t}}(e):null;$r(e),Fe();const r=Zt(o,e,0,[e.props,n]);if(Ve(),Or(),N(r)){if(r.then(Or,Or),t)return r.then((n=>{Ar(e,n,t)})).catch((t=>{Qt(t,e,0)}));e.asyncDep=r}else Ar(e,r,t)}else Nr(e,t)}(e,t):void 0;Pr=!1}(a),a.asyncDep){if(r&&r.registerDep(a,F),!e.el){const e=a.subTree=gr(Qo);_(null,e,t,n)}}else F(a,e,t,n,r,i,s)},B=(e,t,n)=>{const o=t.component=e.component;if(function(e,t,n){const{props:o,children:r,component:i}=e,{props:s,children:a,patchFlag:l}=t,c=i.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&l>=0))return!(!r&&!a||a&&a.$stable)||o!==s&&(o?!s||On(o,s,c):!!s);if(1024&l)return!0;if(16&l)return o?On(o,s,c):!!s;if(8&l){const e=t.dynamicProps;for(let t=0;t<e.length;t++){const n=e[t];if(s[n]!==o[n]&&!wn(c,n))return!0}}return!1}(e,t,n)){if(o.asyncDep&&!o.asyncResolved)return void V(o,t,n);o.next=t,function(e){const t=nn.indexOf(e);t>on&&nn.splice(t,1)}(o.update),o.update()}else t.el=e.el,o.vnode=t},F=(e,t,n,o,r,i,s)=>{const a=()=>{if(e.isMounted){let t,{next:n,bu:o,u:a,parent:l,vnode:c}=e,u=n;Ko(e,!1),n?(n.el=c.el,V(e,n,s)):n=c,o&&X(o),(t=n.props&&n.props.onVnodeBeforeUpdate)&&Sr(t,l,n,c),Ko(e,!0);const f=En(e),p=e.subTree;e.subTree=f,y(p,f,d(p.el),te(p),e,r,i),n.el=f.el,null===u&&function({vnode:e,parent:t},n){for(;t&&t.subTree===e;)(e=t.vnode).el=n,t=t.parent}(e,f.el),a&&Xo(a,r),(t=n.props&&n.props.onVnodeUpdated)&&Xo((()=>Sr(t,l,n,c)),r)}else{let s;const{el:a,props:l}=t,{bm:c,m:u,parent:d}=e,f=Fn(t);if(Ko(e,!1),c&&X(c),!f&&(s=l&&l.onVnodeBeforeMount)&&Sr(s,d,t),Ko(e,!0),a&&ie){const n=()=>{e.subTree=En(e),ie(a,e.subTree,e,r,null)};f?t.type.__asyncLoader().then((()=>!e.isUnmounted&&n())):n()}else{const s=e.subTree=En(e);y(null,s,n,o,e,r,i),t.el=s.el}if(u&&Xo(u,r),!f&&(s=l&&l.onVnodeMounted)){const e=t;Xo((()=>Sr(s,d,e)),r)}const{ba:p,a:h}=e;(256&t.shapeFlag||d&&Fn(d.vnode)&&256&d.vnode.shapeFlag)&&(p&&function(e){for(let t=0;t<e.length;t++){const n=e[t];n.__called||(n(),n.__called=!0)}}(p),h&&Xo(h,r),p&&Xo((()=>{p.forEach((e=>e.__called=!1))}),r)),e.isMounted=!0,t=n=o=null}},l=e.effect=new Re(a,(()=>dn(c)),e.scope),c=e.update=()=>l.run();c.id=e.uid,Ko(e,!0),c()},V=(e,t,n)=>{t.component=e;const o=e.vnode.props;e.vnode=t,e.next=null,function(e,t,n,o){const{props:r,attrs:i,vnode:{patchFlag:s}}=e,a=Mt(r),[l]=e.propsOptions;let c=!1;if(!(o||s>0)||16&s){let o;Po(e,t,r,i)&&(c=!0);for(const i in a)t&&(C(t,i)||(o=U(i))!==i&&C(t,o))||(l?!n||void 0===n[i]&&void 0===n[o]||(r[i]=Ao(l,a,i,void 0,e,!0)):delete r[i]);if(i!==a)for(const e in i)t&&C(t,e)||(delete i[e],c=!0)}else if(8&s){const n=e.vnode.dynamicProps;for(let o=0;o<n.length;o++){let s=n[o];if(wn(e.emitsOptions,s))continue;const u=t[s];if(l)if(C(i,s))u!==i[s]&&(i[s]=u,c=!0);else{const t=W(s);r[t]=Ao(l,a,t,u,e,!1)}else u!==i[s]&&(i[s]=u,c=!0)}}c&&Ue(e,"set","$attrs")}(e,t.props,o,n),((e,t,n)=>{const{vnode:o,slots:r}=e;let i=!0,s=h;if(32&o.shapeFlag){const e=t._;e?n&&1===e?i=!1:(w(r,t),n||1!==e||delete r._):(i=!t.$stable,Wo(t,r)),s=t}else t&&(Ho(e,t),s={default:1});if(i)for(const a in r)Mo(a)||a in s||delete r[a]})(e,t.children,n),Fe(),pn(),Ve()},H=(e,t,n,o,r,i,s,a,l=!1)=>{const c=e&&e.children,d=e?e.shapeFlag:0,f=t.children,{patchFlag:p,shapeFlag:h}=t;if(p>0){if(128&p)return void z(c,f,n,o,r,i,s,a,l);if(256&p)return void D(c,f,n,o,r,i,s,a,l)}8&h?(16&d&&ee(c,r,i),f!==c&&u(n,f)):16&d?16&h?z(c,f,n,o,r,i,s,a,l):ee(c,r,i,!0):(8&d&&u(n,""),16&h&&O(f,n,o,r,i,s,a,l))},D=(e,t,n,o,r,i,s,a,l)=>{t=t||g;const c=(e=e||g).length,u=t.length,d=Math.min(c,u);let f;for(f=0;f<d;f++){const o=t[f]=l?_r(t[f]):br(t[f]);y(e[f],o,n,null,r,i,s,a,l)}c>u?ee(e,r,i,!0,!1,d):O(t,n,o,r,i,s,a,l,d)},z=(e,t,n,o,r,i,s,a,l)=>{let c=0;const u=t.length;let d=e.length-1,f=u-1;for(;c<=d&&c<=f;){const o=e[c],u=t[c]=l?_r(t[c]):br(t[c]);if(!ur(o,u))break;y(o,u,n,null,r,i,s,a,l),c++}for(;c<=d&&c<=f;){const o=e[d],c=t[f]=l?_r(t[f]):br(t[f]);if(!ur(o,c))break;y(o,c,n,null,r,i,s,a,l),d--,f--}if(c>d){if(c<=f){const e=f+1,d=e<u?t[e].el:o;for(;c<=f;)y(null,t[c]=l?_r(t[c]):br(t[c]),n,d,r,i,s,a,l),c++}}else if(c>f)for(;c<=d;)K(e[c],r,i,!0),c++;else{const p=c,h=c,m=new Map;for(c=h;c<=f;c++){const e=t[c]=l?_r(t[c]):br(t[c]);null!=e.key&&m.set(e.key,c)}let v,b=0;const _=f-h+1;let w=!1,x=0;const S=new Array(_);for(c=0;c<_;c++)S[c]=0;for(c=p;c<=d;c++){const o=e[c];if(b>=_){K(o,r,i,!0);continue}let u;if(null!=o.key)u=m.get(o.key);else for(v=h;v<=f;v++)if(0===S[v-h]&&ur(o,t[v])){u=v;break}void 0===u?K(o,r,i,!0):(S[u-h]=c+1,u>=x?x=u:w=!0,y(o,t[u],n,null,r,i,s,a,l),b++)}const C=w?function(e){const t=e.slice(),n=[0];let o,r,i,s,a;const l=e.length;for(o=0;o<l;o++){const l=e[o];if(0!==l){if(r=n[n.length-1],e[r]<l){t[o]=r,n.push(o);continue}for(i=0,s=n.length-1;i<s;)a=i+s>>1,e[n[a]]<l?i=a+1:s=a;l<e[n[i]]&&(i>0&&(t[o]=n[i-1]),n[i]=o)}}i=n.length,s=n[i-1];for(;i-- >0;)n[i]=s,s=t[s];return n}(S):g;for(v=C.length-1,c=_-1;c>=0;c--){const e=h+c,d=t[e],f=e+1<u?t[e+1].el:o;0===S[c]?y(null,d,n,f,r,i,s,a,l):w&&(v<0||c!==C[v]?q(d,n,f,2):v--)}}},q=(e,t,o,r,i=null)=>{const{el:s,type:a,transition:l,children:c,shapeFlag:u}=e;if(6&u)return void q(e.component.subTree,t,o,r);if(128&u)return void e.suspense.move(t,o,r);if(64&u)return void a.move(e,t,o,oe);if(a===Zo){n(s,t,o);for(let e=0;e<c.length;e++)q(c[e],t,o,r);return void n(e.anchor,t,o)}if(a===er)return void S(e,t,o);if(2!==r&&1&u&&l)if(0===r)l.beforeEnter(s),n(s,t,o),Xo((()=>l.enter(s)),i);else{const{leave:e,delayLeave:r,afterLeave:i}=l,a=()=>n(s,t,o),c=()=>{e(s,(()=>{a(),i&&i()}))};r?r(s,a,c):c()}else n(s,t,o)},K=(e,t,n,o=!1,r=!1)=>{const{type:i,props:s,ref:a,children:l,dynamicChildren:c,shapeFlag:u,patchFlag:d,dirs:f}=e;if(null!=a&&qo(a,null,n,e,!0),256&u)return void t.ctx.deactivate(e);const p=1&u&&f,h=!Fn(e);let g;if(h&&(g=s&&s.onVnodeBeforeUnmount)&&Sr(g,t,e),6&u)Q(e.component,n,o);else{if(128&u)return void e.suspense.unmount(n,o);p&&io(e,null,t,"beforeUnmount"),64&u?e.type.remove(e,t,n,r,oe,o):c&&(i!==Zo||d>0&&64&d)?ee(c,t,n,!1,!0):(i===Zo&&384&d||!r&&16&u)&&ee(l,t,n),o&&Z(e)}(h&&(g=s&&s.onVnodeUnmounted)||p)&&Xo((()=>{g&&Sr(g,t,e),p&&io(e,null,t,"unmounted")}),n)},Z=e=>{const{type:t,el:n,anchor:r,transition:i}=e;if(t===Zo)return void G(n,r);if(t===er)return void k(e);const s=()=>{o(n),i&&!i.persisted&&i.afterLeave&&i.afterLeave()};if(1&e.shapeFlag&&i&&!i.persisted){const{leave:t,delayLeave:o}=i,r=()=>t(n,s);o?o(e.el,s,r):r()}else s()},G=(e,t)=>{let n;for(;e!==t;)n=f(e),o(e),e=n;o(t)},Q=(e,t,n)=>{const{bum:o,scope:r,update:i,subTree:s,um:a}=e;o&&X(o),r.stop(),i&&(i.active=!1,K(s,e,t,n)),a&&Xo(a,t),Xo((()=>{e.isUnmounted=!0}),t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},ee=(e,t,n,o=!1,r=!1,i=0)=>{for(let s=i;s<e.length;s++)K(e[s],t,n,o,r)},te=e=>6&e.shapeFlag?te(e.component.subTree):128&e.shapeFlag?e.suspense.next():f(e.anchor||e.el),ne=(e,t,n)=>{null==e?t._vnode&&K(t._vnode,null,null,!0):y(t._vnode||null,e,t,null,null,null,n),pn(),hn(),t._vnode=e},oe={p:y,um:K,m:q,r:Z,mt:I,mc:O,pc:H,pbc:P,n:te,o:e};let re,ie;t&&([re,ie]=t(oe));return{render:ne,hydrate:re,createApp:zo(ne,re)}}(e)}function Ko({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function Jo(e,t,n=!1){const o=e.children,r=t.children;if(k(o)&&k(r))for(let i=0;i<o.length;i++){const e=o[i];let t=r[i];1&t.shapeFlag&&!t.dynamicChildren&&((t.patchFlag<=0||32===t.patchFlag)&&(t=r[i]=_r(r[i]),t.el=e.el),n||Jo(e,t)),t.type===Go&&(t.el=e.el)}}const Zo=Symbol(void 0),Go=Symbol(void 0),Qo=Symbol(void 0),er=Symbol(void 0),tr=[];let nr=null;function or(e=!1){tr.push(nr=e?null:[])}let rr=1;function ir(e){rr+=e}function sr(e){return e.dynamicChildren=rr>0?nr||g:null,tr.pop(),nr=tr[tr.length-1]||null,rr>0&&nr&&nr.push(e),e}function ar(e,t,n,o,r,i){return sr(hr(e,t,n,o,r,i,!0))}function lr(e,t,n,o,r){return sr(gr(e,t,n,o,r,!0))}function cr(e){return!!e&&!0===e.__v_isVNode}function ur(e,t){return e.type===t.type&&e.key===t.key}const dr="__vInternal",fr=({key:e})=>null!=e?e:null,pr=({ref:e,ref_key:t,ref_for:n})=>null!=e?L(e)||Dt(e)||O(e)?{i:xn,r:e,k:t,f:!!n}:e:null;function hr(e,t=null,n=null,o=0,r=null,i=(e===Zo?0:1),s=!1,a=!1){const l={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&fr(t),ref:t&&pr(t),scopeId:Sn,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:o,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:xn};return a?(wr(l,n),128&i&&e.normalize(l)):n&&(l.shapeFlag|=L(n)?8:16),rr>0&&!s&&nr&&(l.patchFlag>0||6&i)&&32!==l.patchFlag&&nr.push(l),l}const gr=function(e,t=null,o=null,r=0,i=null,s=!1){e&&e!==ao||(e=Qo);if(cr(e)){const n=mr(e,t,!0);return o&&wr(n,o),rr>0&&!s&&nr&&(6&n.shapeFlag?nr[nr.indexOf(e)]=n:nr.push(n)),n.patchFlag|=-2,n}l=e,O(l)&&"__vccOpts"in l&&(e=e.__vccOpts);var l;if(t){t=function(e){return e?Bt(e)||dr in e?w({},e):e:null}(t);let{class:e,style:o}=t;e&&!L(e)&&(t.class=a(e)),A(o)&&(Bt(o)&&!k(o)&&(o=w({},o)),t.style=n(o))}const c=L(e)?1:(e=>e.__isSuspense)(e)?128:(e=>e.__isTeleport)(e)?64:A(e)?4:O(e)?2:0;return hr(e,t,o,r,i,c,s,!0)};function mr(e,t,n=!1){const{props:o,ref:r,patchFlag:i,children:s}=e,a=t?xr(o||{},t):o;return{__v_isVNode:!0,__v_skip:!0,type:e.type,props:a,key:a&&fr(a),ref:t&&t.ref?n&&r?k(r)?r.concat(pr(t)):[r,pr(t)]:pr(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:s,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Zo?-1===i?16:16|i:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&mr(e.ssContent),ssFallback:e.ssFallback&&mr(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce}}function vr(e=" ",t=0){return gr(Go,null,e,t)}function yr(e="",t=!1){return t?(or(),lr(Qo,null,e)):gr(Qo,null,e)}function br(e){return null==e||"boolean"==typeof e?gr(Qo):k(e)?gr(Zo,null,e.slice()):"object"==typeof e?_r(e):gr(Go,null,String(e))}function _r(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:mr(e)}function wr(e,t){let n=0;const{shapeFlag:o}=e;if(null==t)t=null;else if(k(t))n=16;else if("object"==typeof t){if(65&o){const n=t.default;return void(n&&(n._c&&(n._d=!1),wr(e,n()),n._c&&(n._d=!0)))}{n=32;const o=t._;o||dr in t?3===o&&xn&&(1===xn.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=xn}}else O(t)?(t={default:t,_ctx:xn},n=32):(t=String(t),64&o?(n=16,t=[vr(t)]):n=8);e.children=t,e.shapeFlag|=n}function xr(...e){const t={};for(let o=0;o<e.length;o++){const r=e[o];for(const e in r)if("class"===e)t.class!==r.class&&(t.class=a([t.class,r.class]));else if("style"===e)t.style=n([t.style,r.style]);else if(b(e)){const n=t[e],o=r[e];!o||n===o||k(n)&&n.includes(o)||(t[e]=n?[].concat(n,o):o)}else""!==e&&(t[e]=r[e])}return t}function Sr(e,t,n,o=null){Gt(e,t,7,[n,o])}const Cr=Uo();let kr=0;let Er=null;const Tr=()=>Er||xn,$r=e=>{Er=e,e.scope.on()},Or=()=>{Er&&Er.scope.off(),Er=null};function Lr(e){return 4&e.vnode.shapeFlag}let Pr=!1;function Ar(e,t,n){O(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:A(t)&&(e.setupState=Yt(t)),Nr(e,n)}function Nr(e,t,n){const o=e.type;e.render||(e.render=o.render||m),$r(e),Fe(),_o(e),Ve(),Or()}function jr(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(Yt(Ft(e.exposed)),{get:(t,n)=>n in t?t[n]:n in mo?mo[n](e):void 0,has:(e,t)=>t in e||t in mo}))}const Rr=(e,t)=>function(e,t,n=!1){let o,r;const i=O(e);return i?(o=e,r=m):(o=e.get,r=e.set),new Jt(o,r,i||!r,n)}(e,0,Pr),Ir=Symbol(""),Br=()=>Pn(Ir),Mr="3.2.47",Fr="undefined"!=typeof document?document:null,Vr=Fr&&Fr.createElement("template"),Wr={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,o)=>{const r=t?Fr.createElementNS("http://www.w3.org/2000/svg",e):Fr.createElement(e,n?{is:n}:void 0);return"select"===e&&o&&null!=o.multiple&&r.setAttribute("multiple",o.multiple),r},createText:e=>Fr.createTextNode(e),createComment:e=>Fr.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Fr.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,o,r,i){const s=n?n.previousSibling:t.lastChild;if(r&&(r===i||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),r!==i&&(r=r.nextSibling););else{Vr.innerHTML=o?`<svg>${e}</svg>`:e;const r=Vr.content;if(o){const e=r.firstChild;for(;e.firstChild;)r.appendChild(e.firstChild);r.removeChild(e)}t.insertBefore(r,n)}return[s?s.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}};const Hr=/\s*!important$/;function Ur(e,t,n){if(k(n))n.forEach((n=>Ur(e,t,n)));else if(null==n&&(n=""),n=Qr(n),t.startsWith("--"))e.setProperty(t,n);else{const o=function(e,t){const n=zr[t];if(n)return n;let o=W(t);if("filter"!==o&&o in e)return zr[t]=o;o=D(o);for(let r=0;r<Dr.length;r++){const n=Dr[r]+o;if(n in e)return zr[t]=n}return t}(e,t);Hr.test(n)?e.setProperty(U(o),n.replace(Hr,""),"important"):e[o]=n}}const Dr=["Webkit","Moz","ms"],zr={};const{unit:qr,unitRatio:Xr,unitPrecision:Yr}={unit:"rem",unitRatio:10/320,unitPrecision:5},Kr=(Jr=qr,Zr=Xr,Gr=Yr,e=>e.replace(se,((e,t)=>{if(!t)return e;if(1===Zr)return`${t}${Jr}`;const n=function(e,t){const n=Math.pow(10,t+1),o=Math.floor(e*n);return 10*Math.round(o/10)/n}(parseFloat(t)*Zr,Gr);return 0===n?"0":`${n}${Jr}`})));var Jr,Zr,Gr;const Qr=e=>L(e)?Kr(e):e,ei="http://www.w3.org/1999/xlink";function ti(e,t,n,o){e.addEventListener(t,n,o)}function ni(e,t,n,o,r=null){const i=e._vei||(e._vei={}),s=i[t];if(o&&s)s.value=o;else{const[n,a]=function(e){let t;if(oi.test(e)){let n;for(t={};n=e.match(oi);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[":"===e[2]?e.slice(3):U(e.slice(2)),t]}(t);if(o){const s=i[t]=function(e,t){const n=e=>{if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();const o=t&&t.proxy,r=o&&o.$nne,{value:i}=n;if(r&&k(i)){const n=si(e,i);for(let o=0;o<n.length;o++){const i=n[o];Gt(i,t,5,i.__wwe?[e]:r(e))}}else Gt(si(e,i),t,5,r&&!i.__wwe?r(e,i,t):[e])};return n.value=e,n.attached=(()=>ri||(ii.then((()=>ri=0)),ri=Date.now()))(),n}(o,r);ti(e,n,s,a)}else s&&(!function(e,t,n,o){e.removeEventListener(t,n,o)}(e,n,s,a),i[t]=void 0)}}const oi=/(?:Once|Passive|Capture)$/;let ri=0;const ii=Promise.resolve();function si(e,t){if(k(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map((e=>{const t=t=>!t._stopped&&e&&e(t);return t.__wwe=e.__wwe,t}))}return t}const ai=/^on[a-z]/;const li=e=>{const t=e.props["onUpdate:modelValue"]||!1;return k(t)?e=>X(t,e):t};function ci(e){e.target.composing=!0}function ui(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const di={created(e,{modifiers:{lazy:t,trim:n,number:o}},r){e._assign=li(r);const i=o||r.props&&"number"===r.props.type;ti(e,t?"change":"input",(t=>{if(t.target.composing)return;let o=e.value;n&&(o=o.trim()),i&&(o=K(o)),e._assign(o)})),n&&ti(e,"change",(()=>{e.value=e.value.trim()})),t||(ti(e,"compositionstart",ci),ti(e,"compositionend",ui),ti(e,"change",ui))},mounted(e,{value:t}){e.value=null==t?"":t},beforeUpdate(e,{value:t,modifiers:{lazy:n,trim:o,number:r}},i){if(e._assign=li(i),e.composing)return;if(document.activeElement===e&&"range"!==e.type){if(n)return;if(o&&e.value.trim()===t)return;if((r||"number"===e.type)&&K(e.value)===t)return}const s=null==t?"":t;e.value!==s&&(e.value=s)}},fi={deep:!0,created(e,t,n){e._assign=li(n),ti(e,"change",(()=>{const t=e._modelValue,n=vi(e),o=e.checked,r=e._assign;if(k(t)){const e=d(t,n),i=-1!==e;if(o&&!i)r(t.concat(n));else if(!o&&i){const n=[...t];n.splice(e,1),r(n)}}else if(T(t)){const e=new Set(t);o?e.add(n):e.delete(n),r(e)}else r(yi(e,o))}))},mounted:pi,beforeUpdate(e,t,n){e._assign=li(n),pi(e,t,n)}};function pi(e,{value:t,oldValue:n},o){e._modelValue=t,k(t)?e.checked=d(t,o.props.value)>-1:T(t)?e.checked=t.has(o.props.value):t!==n&&(e.checked=u(t,yi(e,!0)))}const hi={created(e,{value:t},n){e.checked=u(t,n.props.value),e._assign=li(n),ti(e,"change",(()=>{e._assign(vi(e))}))},beforeUpdate(e,{value:t,oldValue:n},o){e._assign=li(o),t!==n&&(e.checked=u(t,o.props.value))}},gi={deep:!0,created(e,{value:t,modifiers:{number:n}},o){const r=T(t);ti(e,"change",(()=>{const t=Array.prototype.filter.call(e.options,(e=>e.selected)).map((e=>n?K(vi(e)):vi(e)));e._assign(e.multiple?r?new Set(t):t:t[0])})),e._assign=li(o)},mounted(e,{value:t}){mi(e,t)},beforeUpdate(e,t,n){e._assign=li(n)},updated(e,{value:t}){mi(e,t)}};function mi(e,t){const n=e.multiple;if(!n||k(t)||T(t)){for(let o=0,r=e.options.length;o<r;o++){const r=e.options[o],i=vi(r);if(n)k(t)?r.selected=d(t,i)>-1:r.selected=t.has(i);else if(u(vi(r),t))return void(e.selectedIndex!==o&&(e.selectedIndex=o))}n||-1===e.selectedIndex||(e.selectedIndex=-1)}}function vi(e){return"_value"in e?e._value:e.value}function yi(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const bi={created(e,t,n){_i(e,t,n,null,"created")},mounted(e,t,n){_i(e,t,n,null,"mounted")},beforeUpdate(e,t,n,o){_i(e,t,n,o,"beforeUpdate")},updated(e,t,n,o){_i(e,t,n,o,"updated")}};function _i(e,t,n,o,r){const i=function(e,t){switch(e){case"SELECT":return gi;case"TEXTAREA":return di;default:switch(t){case"checkbox":return fi;case"radio":return hi;default:return di}}}(e.tagName,n.props&&n.props.type)[r];i&&i(e,t,n,o)}const wi={beforeMount(e,{value:t},{transition:n}){e._vod="none"===e.style.display?"":e.style.display,n&&t?n.beforeEnter(e):xi(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:o}){!t!=!n&&(o?t?(o.beforeEnter(e),xi(e,!0),o.enter(e)):o.leave(e,(()=>{xi(e,!1)})):xi(e,t))},beforeUnmount(e,{value:t}){xi(e,t)}};function xi(e,t){e.style.display=t?e._vod:"none"}const Si=w({patchProp:(e,t,n,o,r=!1,i,s,a,u)=>{if(0===t.indexOf("change:"))return function(e,t,n,o=null){if(!n||!o)return;const r=t.replace("change:",""),{attrs:i}=o,s=i[r],a=(e.__wxsProps||(e.__wxsProps={}))[r];if(a===s)return;e.__wxsProps[r]=s;const l=o.proxy;un((()=>{n(s,a,l.$gcd(l,!0),l.$gcd(l,!1))}))}(e,t,o,s);"class"===t?function(e,t,n){const{__wxsAddClass:o,__wxsRemoveClass:r}=e;r&&r.length&&(t=(t||"").split(/\s+/).filter((e=>-1===r.indexOf(e))).join(" "),r.length=0),o&&o.length&&(t=(t||"")+" "+o.join(" "));const i=e._vtc;i&&(t=(t?[t,...i]:[...i]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}(e,o,r):"style"===t?function(e,t,n){const o=e.style,r=L(n);if(n&&!r){if(t&&!L(t))for(const e in t)null==n[e]&&Ur(o,e,"");for(const e in n)Ur(o,e,n[e])}else{const i=o.display;r?t!==n&&(o.cssText=n):t&&e.removeAttribute("style"),"_vod"in e&&(o.display=i)}const{__wxsStyle:i}=e;if(i)for(const s in i)Ur(o,s,i[s])}(e,n,o):b(t)?_(t)||ni(e,t,0,o,s):("."===t[0]?(t=t.slice(1),1):"^"===t[0]?(t=t.slice(1),0):function(e,t,n,o){if(o)return"innerHTML"===t||"textContent"===t||!!(t in e&&ai.test(t)&&O(n));if("spellcheck"===t||"draggable"===t||"translate"===t)return!1;if("form"===t)return!1;if("list"===t&&"INPUT"===e.tagName)return!1;if("type"===t&&"TEXTAREA"===e.tagName)return!1;if(ai.test(t)&&L(n))return!1;return t in e}(e,t,o,r))?function(e,t,n,o,r,i,s){if("innerHTML"===t||"textContent"===t)return o&&s(o,r,i),void(e[t]=null==n?"":n);if("value"===t&&"PROGRESS"!==e.tagName&&!e.tagName.includes("-")){e._value=n;const o=null==n?"":n;return e.value===o&&"OPTION"!==e.tagName||(e.value=o),void(null==n&&e.removeAttribute(t))}let a=!1;if(""===n||null==n){const o=typeof e[t];"boolean"===o?n=c(n):null==n&&"string"===o?(n="",a=!0):"number"===o&&(n=0,a=!0)}try{e[t]=n}catch(l){}a&&e.removeAttribute(t)}(e,t,o,i,s,a,u):("true-value"===t?e._trueValue=o:"false-value"===t&&(e._falseValue=o),function(e,t,n,o,r){if(o&&t.startsWith("xlink:"))null==n?e.removeAttributeNS(ei,t.slice(6,t.length)):e.setAttributeNS(ei,t,n);else{const o=l(t);null==n||o&&!c(n)?e.removeAttribute(t):e.setAttribute(t,o?"":n)}}(e,t,o,r))},forcePatchProp:(e,t)=>0===t.indexOf("change:")||("class"===t&&e.__wxsClassChanged?(e.__wxsClassChanged=!1,!0):!("style"!==t||!e.__wxsStyleChanged)&&(e.__wxsStyleChanged=!1,!0))},Wr);let Ci;const ki=(...e)=>{const t=(Ci||(Ci=Yo(Si))).createApp(...e),{mount:n}=t;return t.mount=e=>{const o=function(e){if(L(e)){return document.querySelector(e)}return e}(e);if(!o)return;const r=t._component;O(r)||r.render||r.template||(r.template=o.innerHTML),o.innerHTML="";const i=n(o,!1,o instanceof SVGElement);return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),i},t};const Ei=["{","}"];const Ti=/^(?:\d)+/,$i=/^(?:\w)+/;const Oi=Object.prototype.hasOwnProperty,Li=(e,t)=>Oi.call(e,t),Pi=new class{constructor(){this._caches=Object.create(null)}interpolate(e,t,n=Ei){if(!t)return[e];let o=this._caches[e];return o||(o=function(e,[t,n]){const o=[];let r=0,i="";for(;r<e.length;){let s=e[r++];if(s===t){i&&o.push({type:"text",value:i}),i="";let t="";for(s=e[r++];void 0!==s&&s!==n;)t+=s,s=e[r++];const a=s===n,l=Ti.test(t)?"list":a&&$i.test(t)?"named":"unknown";o.push({value:t,type:l})}else i+=s}return i&&o.push({type:"text",value:i}),o}(e,n),this._caches[e]=o),function(e,t){const n=[];let o=0;const r=Array.isArray(t)?"list":(i=t,null!==i&&"object"==typeof i?"named":"unknown");var i;if("unknown"===r)return n;for(;o<e.length;){const i=e[o];switch(i.type){case"text":n.push(i.value);break;case"list":n.push(t[parseInt(i.value,10)]);break;case"named":"named"===r&&n.push(t[i.value])}o++}return n}(o,t)}};function Ai(e,t){if(!e)return;if(e=e.trim().replace(/_/g,"-"),t&&t[e])return e;if("chinese"===(e=e.toLowerCase()))return"zh-Hans";if(0===e.indexOf("zh"))return e.indexOf("-hans")>-1?"zh-Hans":e.indexOf("-hant")>-1?"zh-Hant":(n=e,["-tw","-hk","-mo","-cht"].find((e=>-1!==n.indexOf(e)))?"zh-Hant":"zh-Hans");var n;let o=["en","fr","es"];t&&Object.keys(t).length>0&&(o=Object.keys(t));const r=function(e,t){return t.find((t=>0===e.indexOf(t)))}(e,o);return r||void 0}class Ni{constructor({locale:e,fallbackLocale:t,messages:n,watcher:o,formater:r}){this.locale="en",this.fallbackLocale="en",this.message={},this.messages={},this.watchers=[],t&&(this.fallbackLocale=t),this.formater=r||Pi,this.messages=n||{},this.setLocale(e||"en"),o&&this.watchLocale(o)}setLocale(e){const t=this.locale;this.locale=Ai(e,this.messages)||this.fallbackLocale,this.messages[this.locale]||(this.messages[this.locale]={}),this.message=this.messages[this.locale],t!==this.locale&&this.watchers.forEach((e=>{e(this.locale,t)}))}getLocale(){return this.locale}watchLocale(e){const t=this.watchers.push(e)-1;return()=>{this.watchers.splice(t,1)}}add(e,t,n=!0){const o=this.messages[e];o?n?Object.assign(o,t):Object.keys(t).forEach((e=>{Li(o,e)||(o[e]=t[e])})):this.messages[e]=t}f(e,t,n){return this.formater.interpolate(e,t,n).join("")}t(e,t,n){let o=this.message;return"string"==typeof t?(t=Ai(t,this.messages))&&(o=this.messages[t]):n=t,Li(o,e)?this.formater.interpolate(o[e],n).join(""):(console.warn(`Cannot translate the value of keypath ${e}. Use the value of keypath as default.`),e)}}function ji(e,t={},n,o){"string"!=typeof e&&([e,t]=[t,e]),"string"!=typeof e&&(e="undefined"!=typeof uni&&ml?ml():"undefined"!=typeof global&&global.getLocale?global.getLocale():"en"),"string"!=typeof n&&(n="undefined"!=typeof __uniConfig&&__uniConfig.fallbackLocale||"en");const r=new Ni({locale:e,fallbackLocale:n,messages:t,watcher:o});let i=(e,t)=>{{let e=!1;i=function(t,n){const o=Bc().$vm;return o&&(o.$locale,e||(e=!0,function(e,t){e.$watchLocale?e.$watchLocale((e=>{t.setLocale(e)})):e.$watch((()=>e.$locale),(e=>{t.setLocale(e)}))}(o,r))),r.t(t,n)}}return i(e,t)};return{i18n:r,f:(e,t,n)=>r.f(e,t,n),t:(e,t)=>i(e,t),add:(e,t,n=!0)=>r.add(e,t,n),watch:e=>r.watchLocale(e),getLocale:()=>r.getLocale(),setLocale:e=>r.setLocale(e)}}
/*!
  * vue-router v4.1.6
  * (c) 2022 Eduardo San Martin Morote
  * @license MIT
  */var Ri,Ii,Bi,Mi;(Ii=Ri||(Ri={})).pop="pop",Ii.push="push",(Mi=Bi||(Bi={})).back="back",Mi.forward="forward",Mi.unknown="";Symbol("");var Fi,Vi;(Vi=Fi||(Fi={}))[Vi.aborted=4]="aborted",Vi[Vi.cancelled=8]="cancelled",Vi[Vi.duplicated=16]="duplicated";const Wi=ne((()=>"undefined"!=typeof __uniConfig&&__uniConfig.locales&&!!Object.keys(__uniConfig.locales).length));let Hi;function Ui(){if(!Hi){let e;if(e=navigator.cookieEnabled&&window.localStorage&&localStorage.UNI_LOCALE||__uniConfig.locale||navigator.language,Hi=ji(e),Wi()){const t=Object.keys(__uniConfig.locales||{});t.length&&t.forEach((e=>Hi.add(e,__uniConfig.locales[e]))),Hi.setLocale(e)}}return Hi}function Di(e,t,n){return t.reduce(((t,o,r)=>(t[e+o]=n[r],t)),{})}const zi=ne((()=>{const e="uni.async.",t=["error"];Ui().add("en",Di(e,t,["The connection timed out, click the screen to try again."]),!1),Ui().add("es",Di(e,t,["Se agotó el tiempo de conexión, haga clic en la pantalla para volver a intentarlo."]),!1),Ui().add("fr",Di(e,t,["La connexion a expiré, cliquez sur l'écran pour réessayer."]),!1),Ui().add("zh-Hans",Di(e,t,["连接服务器超时，点击屏幕重试"]),!1),Ui().add("zh-Hant",Di(e,t,["連接服務器超時，點擊屏幕重試"]),!1)}));function qi(e){const t=new we;return{on:(e,n)=>t.on(e,n),once:(e,n)=>t.once(e,n),off:(e,n)=>t.off(e,n),emit:(e,...n)=>t.emit(e,...n),subscribe(n,o,r=!1){t[r?"once":"on"](`${e}.${n}`,o)},unsubscribe(n,o){t.off(`${e}.${n}`,o)},subscribeHandler(n,o,r){t.emit(`${e}.${n}`,o,r)}}}let Xi=1;const Yi=Object.create(null);function Ki(e,t){return e+"."+t}function Ji({id:e,name:t,args:n},o){t=Ki(o,t);const r=t=>{e&&cu.publishHandler("invokeViewApi."+e,t)},i=Yi[t];i?i(n,r):r({})}const Zi=w(qi("service"),{invokeServiceMethod:(e,t,n)=>{const{subscribe:o,publishHandler:r}=cu,i=n?Xi++:0;n&&o("invokeServiceApi."+i,n,!0),r("invokeServiceApi",{id:i,name:e,args:t})}}),Gi=ae(!0);let Qi;function es(){Qi&&(clearTimeout(Qi),Qi=null)}let ts=0,ns=0;function os(e){if(es(),1!==e.touches.length)return;const{pageX:t,pageY:n}=e.touches[0];ts=t,ns=n,Qi=setTimeout((function(){const t=new CustomEvent("longpress",{bubbles:!0,cancelable:!0,target:e.target,currentTarget:e.currentTarget});t.touches=e.touches,t.changedTouches=e.changedTouches,e.target.dispatchEvent(t)}),350)}function rs(e){if(!Qi)return;if(1!==e.touches.length)return es();const{pageX:t,pageY:n}=e.touches[0];return Math.abs(t-ts)>10||Math.abs(n-ns)>10?es():void 0}function is(e,t){const n=Number(e);return isNaN(n)?t:n}function ss(){const e=__uniConfig.globalStyle||{},t=is(e.rpxCalcMaxDeviceWidth,960),n=is(e.rpxCalcBaseDeviceWidth,375);function o(){let e=function(){const e=/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation,t=e&&90===Math.abs(window.orientation);var n=e?Math[t?"max":"min"](screen.width,screen.height):screen.width;return Math.min(window.innerWidth,document.documentElement.clientWidth,n)||n}();e=e<=t?e:n,document.documentElement.style.fontSize=e/23.4375+"px"}o(),document.addEventListener("DOMContentLoaded",o),window.addEventListener("load",o),window.addEventListener("resize",o)}function as(){ss(),re(),window.addEventListener("touchstart",os,Gi),window.addEventListener("touchmove",rs,Gi),window.addEventListener("touchend",es,Gi),window.addEventListener("touchcancel",es,Gi)}function ls(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var cs,us,ds=["top","left","right","bottom"],fs={};function ps(){return us="CSS"in window&&"function"==typeof CSS.supports?CSS.supports("top: env(safe-area-inset-top)")?"env":CSS.supports("top: constant(safe-area-inset-top)")?"constant":"":""}function hs(){if(us="string"==typeof us?us:ps()){var e=[],t=!1;try{var n=Object.defineProperty({},"passive",{get:function(){t={passive:!0}}});window.addEventListener("test",null,n)}catch(a){}var o=document.createElement("div");r(o,{position:"absolute",left:"0",top:"0",width:"0",height:"0",zIndex:"-1",overflow:"hidden",visibility:"hidden"}),ds.forEach((function(e){s(o,e)})),document.body.appendChild(o),i(),cs=!0}else ds.forEach((function(e){fs[e]=0}));function r(e,t){var n=e.style;Object.keys(t).forEach((function(e){var o=t[e];n[e]=o}))}function i(t){t?e.push(t):e.forEach((function(e){e()}))}function s(e,n){var o=document.createElement("div"),s=document.createElement("div"),a=document.createElement("div"),l=document.createElement("div"),c={position:"absolute",width:"100px",height:"200px",boxSizing:"border-box",overflow:"hidden",paddingBottom:us+"(safe-area-inset-"+n+")"};r(o,c),r(s,c),r(a,{transition:"0s",animation:"none",width:"400px",height:"400px"}),r(l,{transition:"0s",animation:"none",width:"250%",height:"250%"}),o.appendChild(a),s.appendChild(l),e.appendChild(o),e.appendChild(s),i((function(){o.scrollTop=s.scrollTop=1e4;var e=o.scrollTop,r=s.scrollTop;function i(){this.scrollTop!==(this===o?e:r)&&(o.scrollTop=s.scrollTop=1e4,e=o.scrollTop,r=s.scrollTop,function(e){ms.length||setTimeout((function(){var e={};ms.forEach((function(t){e[t]=fs[t]})),ms.length=0,vs.forEach((function(t){t(e)}))}),0);ms.push(e)}(n))}o.addEventListener("scroll",i,t),s.addEventListener("scroll",i,t)}));var u=getComputedStyle(o);Object.defineProperty(fs,n,{configurable:!0,get:function(){return parseFloat(u.paddingBottom)}})}}function gs(e){return cs||hs(),fs[e]}var ms=[];var vs=[];const ys=ls({get support(){return 0!=("string"==typeof us?us:ps()).length},get top(){return gs("top")},get left(){return gs("left")},get right(){return gs("right")},get bottom(){return gs("bottom")},onChange:function(e){ps()&&(cs||hs(),"function"==typeof e&&vs.push(e))},offChange:function(e){var t=vs.indexOf(e);t>=0&&vs.splice(t,1)}});function bs(e,t){return parseInt((e.getPropertyValue(t).match(/\d+/)||["0"])[0])}function _s(){const e=bs(document.documentElement.style,"--window-top");return e?e+ys.top:0}function ws(e){const t=document.documentElement.style;Object.keys(e).forEach((n=>{t.setProperty(n,e[n])}))}function xs(e){return Symbol(e)}function Ss(e,t="#000",n=27){return gr("svg",{width:n,height:n,viewBox:"0 0 32 32"},[gr("path",{d:e,fill:t},null,8,["d","fill"])],8,["width","height"])}function Cs(){{const{$pageInstance:e}=Tr();return e&&e.proxy.$page.id}}function ks(){const e=_c(),t=e.length;if(t)return e[t-1]}function Es(){const e=ks();if(e)return e.$page.meta}function Ts(){const e=Es();return e?e.id:-1}function $s(){const e=ks();if(e)return e.$vm}const Os=["navigationBar","pullToRefresh"];function Ls(e,t){const n=JSON.parse(JSON.stringify(__uniConfig.globalStyle||{})),o=w({id:t},n,e);Os.forEach((t=>{o[t]=w({},n[t],e[t])}));const{navigationBar:r}=o;return r.titleText&&r.titleImage&&(r.titleText=""),o}function Ps(e,t,n){if(L(e))n=t,t=e,e=$s();else if("number"==typeof e){const t=_c().find((t=>t.$page.id===e));e=t?t.$vm:$s()}if(!e)return;const o=e.$[t];return o&&((e,t)=>{let n;for(let o=0;o<e.length;o++)n=e[o](t);return n})(o,n)}function As(e){e.preventDefault()}let Ns,js=0;function Rs({onPageScroll:e,onReachBottom:t,onReachBottomDistance:n}){let o=!1,r=!1,i=!0;const s=()=>{function s(){if((()=>{const{scrollHeight:e}=document.documentElement,t=window.innerHeight,o=window.scrollY,i=o>0&&e>t&&o+t+n>=e,s=Math.abs(e-js)>n;return!i||r&&!s?(!i&&r&&(r=!1),!1):(js=e,r=!0,!0)})())return t&&t(),i=!1,setTimeout((function(){i=!0}),350),!0}e&&e(window.pageYOffset),t&&i&&(s()||(Ns=setTimeout(s,300))),o=!1};return function(){clearTimeout(Ns),o||requestAnimationFrame(s),o=!0}}function Is(e,t=!1){return t?__uniRoutes.find((t=>t.path===e||t.alias===e)):__uniRoutes.find((t=>t.path===e))}class Bs{constructor(e){this.$bindClass=!1,this.$bindStyle=!1,this.$vm=e,this.$el=function(e,t=!1){const{vnode:n}=e;if(Q(n.el))return t?n.el?[n.el]:[]:n.el;const{subTree:o}=e;if(16&o.shapeFlag){const e=o.children.filter((e=>e.el&&Q(e.el)));if(e.length>0)return t?e.map((e=>e.el)):e[0].el}return t?n.el?[n.el]:[]:n.el}(e.$),this.$el.getAttribute&&(this.$bindClass=!!this.$el.getAttribute("class"),this.$bindStyle=!!this.$el.getAttribute("style"))}selectComponent(e){if(!this.$el||!e)return;const t=Ws(this.$el.querySelector(e));return t?Ms(t,!1):void 0}selectAllComponents(e){if(!this.$el||!e)return[];const t=[],n=this.$el.querySelectorAll(e);for(let o=0;o<n.length;o++){const e=Ws(n[o]);e&&t.push(Ms(e,!1))}return t}forceUpdate(e){"class"===e?this.$bindClass?(this.$el.__wxsClassChanged=!0,this.$vm.$forceUpdate()):this.updateWxsClass():"style"===e&&(this.$bindStyle?(this.$el.__wxsStyleChanged=!0,this.$vm.$forceUpdate()):this.updateWxsStyle())}updateWxsClass(){const{__wxsAddClass:e}=this.$el;e.length&&(this.$el.className=e.join(" "))}updateWxsStyle(){const{__wxsStyle:e}=this.$el;e&&this.$el.setAttribute("style",function(e){let t="";if(!e||L(e))return t;for(const n in e){const o=e[n],r=n.startsWith("--")?n:U(n);(L(o)||"number"==typeof o)&&(t+=`${r}:${o};`)}return t}(e))}setStyle(e){return this.$el&&e?(L(e)&&(e=s(e)),I(e)&&(this.$el.__wxsStyle=e,this.forceUpdate("style")),this):this}addClass(e){if(!this.$el||!e)return this;const t=this.$el.__wxsAddClass||(this.$el.__wxsAddClass=[]);return-1===t.indexOf(e)&&(t.push(e),this.forceUpdate("class")),this}removeClass(e){if(!this.$el||!e)return this;const{__wxsAddClass:t}=this.$el;if(t){const n=t.indexOf(e);n>-1&&t.splice(n,1)}const n=this.$el.__wxsRemoveClass||(this.$el.__wxsRemoveClass=[]);return-1===n.indexOf(e)&&(n.push(e),this.forceUpdate("class")),this}hasClass(e){return this.$el&&this.$el.classList.contains(e)}getDataset(){return this.$el&&this.$el.dataset}callMethod(e,t={}){const n=this.$vm[e];O(n)?n(JSON.parse(JSON.stringify(t))):this.$vm.ownerId&&cu.publishHandler("onWxsInvokeCallMethod",{nodeId:this.$el.__id,ownerId:this.$vm.ownerId,method:e,args:t})}requestAnimationFrame(e){return window.requestAnimationFrame(e)}getState(){return this.$el&&(this.$el.__wxsState||(this.$el.__wxsState={}))}triggerEvent(e,t={}){return this.$vm.$emit(e,t),this}getComputedStyle(e){if(this.$el){const t=window.getComputedStyle(this.$el);return e&&e.length?e.reduce(((e,n)=>(e[n]=t[n],e)),{}):t}return{}}setTimeout(e,t){return window.setTimeout(e,t)}clearTimeout(e){return window.clearTimeout(e)}getBoundingClientRect(){return this.$el.getBoundingClientRect()}}function Ms(e,t=!0){if(t&&e&&(e=G(e.$)),e&&e.$el)return e.$el.__wxsComponentDescriptor||(e.$el.__wxsComponentDescriptor=new Bs(e)),e.$el.__wxsComponentDescriptor}function Fs(e,t){return Ms(e,t)}function Vs(e,t,n,o=!0){if(t){e.__instance||(e.__instance=!0,Object.defineProperty(e,"instance",{get:()=>Fs(n.proxy,!1)}));const r=function(e,t,n=!0){if(!t)return!1;if(n&&e.length<2)return!1;const o=G(t);if(!o)return!1;const r=o.$.type;return!(!r.$wxs&&!r.$renderjs)&&o}(t,n,o);if(r)return[e,Fs(r,!1)]}}function Ws(e){if(e)return e.__vueParentComponent&&e.__vueParentComponent.proxy}function Hs(e){for(;e&&0!==e.tagName.indexOf("UNI-");)e=e.parentElement;return e}function Us(e,t=!1){const{type:n,timeStamp:o,target:r,currentTarget:i}=e,s={type:n,timeStamp:o,target:le(t?r:Hs(r)),detail:{},currentTarget:le(i)};return e._stopped&&(s._stopped=!0),e.type.startsWith("touch")&&(s.touches=e.touches,s.changedTouches=e.changedTouches),function(e,t){w(e,{preventDefault:()=>t.preventDefault(),stopPropagation:()=>t.stopPropagation()})}(s,e),s}function Ds(e,t){return{force:1,identifier:0,clientX:e.clientX,clientY:e.clientY-t,pageX:e.pageX,pageY:e.pageY-t}}function zs(e,t){const n=[];for(let o=0;o<e.length;o++){const{identifier:r,pageX:i,pageY:s,clientX:a,clientY:l,force:c}=e[o];n.push({identifier:r,pageX:i,pageY:s-t,clientX:a,clientY:l-t,force:c||0})}return n}const qs=Object.defineProperty({__proto__:null,$nne:function(e,t,n){const{currentTarget:o}=e;if(!(e instanceof Event&&o instanceof HTMLElement))return[e];const r=0!==o.tagName.indexOf("UNI-");if(r)return Vs(e,t,n,!1)||[e];const i=Us(e,r);if("click"===e.type)!function(e,t){const{x:n,y:o}=t,r=_s();e.detail={x:n,y:o-r},e.touches=e.changedTouches=[Ds(t,r)]}(i,e);else if((e=>0===e.type.indexOf("mouse")||["contextmenu"].includes(e.type))(e))!function(e,t){const n=_s();e.pageX=t.pageX,e.pageY=t.pageY-n,e.clientX=t.clientX,e.clientY=t.clientY-n,e.touches=e.changedTouches=[Ds(t,n)]}(i,e);else if((e=>"undefined"!=typeof TouchEvent&&e instanceof TouchEvent||0===e.type.indexOf("touch")||["longpress"].indexOf(e.type)>=0)(e)){const t=_s();i.touches=zs(e.touches,t),i.changedTouches=zs(e.changedTouches,t)}else if((e=>!e.type.indexOf("key")&&e instanceof KeyboardEvent)(e)){["key","code"].forEach((t=>{Object.defineProperty(i,t,{get:()=>e[t]})}))}return Vs(i,t,n)||[i]},createNativeEvent:Us},Symbol.toStringTag,{value:"Module"});function Xs(e){!function(e){const t=e.globalProperties;w(t,qs),t.$gcd=Fs}(e._context.config)}let Ys=1;function Ks(e){return(e||Ts())+".invokeViewApi"}const Js=w(qi("view"),{invokeOnCallback:(e,t)=>uu.emit("api."+e,t),invokeViewMethod:(e,t,n,o)=>{const{subscribe:r,publishHandler:i}=uu,s=o?Ys++:0;o&&r("invokeViewApi."+s,o,!0),i(Ks(n),{id:s,name:e,args:t},n)},invokeViewMethodKeepAlive:(e,t,n,o)=>{const{subscribe:r,unsubscribe:i,publishHandler:s}=uu,a=Ys++,l="invokeViewApi."+a;return r(l,n),s(Ks(o),{id:a,name:e,args:t},o),()=>{i(l)}}});function Zs(e){Ps(ks(),"onResize",e),uu.invokeOnCallback("onWindowResize",e)}function Gs(e){const t=ks();Ps(Bc(),"onShow",e),Ps(t,"onShow")}function Qs(){Ps(Bc(),"onHide"),Ps(ks(),"onHide")}const ea=["onPageScroll","onReachBottom"];function ta(){ea.forEach((e=>uu.subscribe(e,function(e){return(t,n)=>{Ps(parseInt(n),e,t)}}(e))))}function na(){!function(){const{on:e}=uu;e("onResize",Zs),e("onAppEnterForeground",Gs),e("onAppEnterBackground",Qs)}(),ta()}function oa(){if(this.$route){const e=this.$route.meta;return e.eventChannel||(e.eventChannel=new he(this.$page.id)),e.eventChannel}}function ra(e){e._context.config.globalProperties.getOpenerEventChannel=oa}function ia(){return{path:"",query:{},scene:1001,referrerInfo:{appId:"",extraData:{}}}}function sa(e){return/^-?\d+[ur]px$/i.test(e)?e.replace(/(^-?\d+)[ur]px$/i,((e,t)=>`${gl(parseFloat(t))}px`)):/^-?[\d\.]+$/.test(e)?`${e}px`:e||""}function aa(e){const t=e.animation;if(!t||!t.actions||!t.actions.length)return;let n=0;const o=t.actions,r=t.actions.length;function i(){const t=o[n],s=t.option.transition,a=function(e){const t=["matrix","matrix3d","scale","scale3d","rotate3d","skew","translate","translate3d"],n=["scaleX","scaleY","scaleZ","rotate","rotateX","rotateY","rotateZ","skewX","skewY","translateX","translateY","translateZ"],o=["opacity","background-color"],r=["width","height","left","right","top","bottom"],i=e.animates,s=e.option,a=s.transition,l={},c=[];return i.forEach((e=>{let i=e.type,s=[...e.args];if(t.concat(n).includes(i))i.startsWith("rotate")||i.startsWith("skew")?s=s.map((e=>parseFloat(e)+"deg")):i.startsWith("translate")&&(s=s.map(sa)),n.indexOf(i)>=0&&(s.length=1),c.push(`${i}(${s.join(",")})`);else if(o.concat(r).includes(s[0])){i=s[0];const e=s[1];l[i]=r.includes(i)?sa(e):e}})),l.transform=l.webkitTransform=c.join(" "),l.transition=l.webkitTransition=Object.keys(l).map((e=>`${function(e){return e.replace(/[A-Z]/g,(e=>`-${e.toLowerCase()}`)).replace("webkit","-webkit")}(e)} ${a.duration}ms ${a.timingFunction} ${a.delay}ms`)).join(","),l.transformOrigin=l.webkitTransformOrigin=s.transformOrigin,l}(t);Object.keys(a).forEach((t=>{e.$el.style[t]=a[t]})),n+=1,n<r&&setTimeout(i,s.duration+s.delay)}setTimeout((()=>{i()}),0)}const la={props:["animation"],watch:{animation:{deep:!0,handler(){aa(this)}}},mounted(){aa(this)}},ca=e=>{e.__reserved=!0;const{props:t,mixins:n}=e;return t&&t.animation||(n||(e.mixins=[])).push(la),ua(e)},ua=e=>(e.__reserved=!0,e.compatConfig={MODE:3},Mn(e)),da={hoverClass:{type:String,default:"none"},hoverStopPropagation:{type:Boolean,default:!1},hoverStartTime:{type:[Number,String],default:50},hoverStayTime:{type:[Number,String],default:400}};function fa(e){const t=zt(!1);let n,o,r=!1;function i(){requestAnimationFrame((()=>{clearTimeout(o),o=setTimeout((()=>{t.value=!1}),parseInt(e.hoverStayTime))}))}function s(o){o._hoverPropagationStopped||e.hoverClass&&"none"!==e.hoverClass&&!e.disabled&&(e.hoverStopPropagation&&(o._hoverPropagationStopped=!0),r=!0,n=setTimeout((()=>{t.value=!0,r||i()}),parseInt(e.hoverStartTime)))}function a(){r=!1,t.value&&i()}function l(){a(),window.removeEventListener("mouseup",l)}return{hovering:t,binding:{onTouchstartPassive:function(e){e.touches.length>1||s(e)},onMousedown:function(e){r||(s(e),window.addEventListener("mouseup",l))},onTouchend:function(){a()},onMouseup:function(){r&&l()},onTouchcancel:function(){r=!1,t.value=!1,clearTimeout(n)}}}}function pa(e,t){return L(t)&&(t=[t]),t.reduce(((t,n)=>(e[n]&&(t[n]=!0),t)),Object.create(null))}function ha(e){return e.__wwe=!0,e}function ga(e,t){return(n,o,r)=>{e.value&&t(n,function(e,t,n,o){const r=le(n);return{type:o.type||e,timeStamp:t.timeStamp||0,target:r,currentTarget:r,detail:o}}(n,o,e.value,r||{}))}}const ma=xs("uf"),va={for:{type:String,default:""}},ya=xs("ul");const ba=ca({name:"Label",props:va,setup(e,{slots:t}){const n=Cs(),o=function(){const e=[];return Ln(ya,{addHandler(t){e.push(t)},removeHandler(t){e.splice(e.indexOf(t),1)}}),e}(),r=Rr((()=>e.for||t.default&&t.default.length)),i=ha((t=>{const r=t.target;let i=/^uni-(checkbox|radio|switch)-/.test(r.className);i||(i=/^uni-(checkbox|radio|switch|button)$|^(svg|path)$/i.test(r.tagName)),i||(e.for?cu.emit("uni-label-click-"+n+"-"+e.for,t,!0):o.length&&o[0](t,!0))}));return()=>gr("uni-label",{class:{"uni-label-pointer":r},onClick:i},[t.default&&t.default()],10,["onClick"])}});function _a(e,t){wa(e.id,t),Nn((()=>e.id),((e,n)=>{xa(n,t,!0),wa(e,t,!0)})),Qn((()=>{xa(e.id,t)}))}function wa(e,t,n){const o=Cs();n&&!e||I(t)&&Object.keys(t).forEach((r=>{n?0!==r.indexOf("@")&&0!==r.indexOf("uni-")&&cu.on(`uni-${r}-${o}-${e}`,t[r]):0===r.indexOf("uni-")?cu.on(r,t[r]):e&&cu.on(`uni-${r}-${o}-${e}`,t[r])}))}function xa(e,t,n){const o=Cs();n&&!e||I(t)&&Object.keys(t).forEach((r=>{n?0!==r.indexOf("@")&&0!==r.indexOf("uni-")&&cu.off(`uni-${r}-${o}-${e}`,t[r]):0===r.indexOf("uni-")?cu.off(r,t[r]):e&&cu.off(`uni-${r}-${o}-${e}`,t[r])}))}const Sa=ca({name:"Button",props:{id:{type:String,default:""},hoverClass:{type:String,default:"button-hover"},hoverStartTime:{type:[Number,String],default:20},hoverStayTime:{type:[Number,String],default:70},hoverStopPropagation:{type:Boolean,default:!1},disabled:{type:[Boolean,String],default:!1},formType:{type:String,default:""},openType:{type:String,default:""},loading:{type:[Boolean,String],default:!1},plain:{type:[Boolean,String],default:!1}},setup(e,{slots:t}){const n=zt(null),o=Pn(ma,!1),{hovering:r,binding:i}=fa(e);Ui();const s=ha(((t,r)=>{if(e.disabled)return t.stopImmediatePropagation();r&&n.value.click();const i=e.formType;if(i){if(!o)return;"submit"===i?o.submit(t):"reset"===i&&o.reset(t)}else;})),a=Pn(ya,!1);return a&&(a.addHandler(s),Gn((()=>{a.removeHandler(s)}))),_a(e,{"label-click":s}),()=>{const o=e.hoverClass,a=pa(e,"disabled"),l=pa(e,"loading"),c=pa(e,"plain"),u=o&&"none"!==o;return gr("uni-button",xr({ref:n,onClick:s,class:u&&r.value?o:""},u&&i,a,l,c),[t.default&&t.default()],16,["onClick"])}}});const Ca=navigator.userAgent,ka=/android/i.test(Ca),Ea=/iphone|ipad|ipod/i.test(Ca),Ta=Ca.match(/Windows NT ([\d|\d.\d]*)/i),$a=/Macintosh|Mac/i.test(Ca),Oa=/Linux|X11/i.test(Ca),La=$a&&navigator.maxTouchPoints>0;function Pa(){return/^Apple/.test(navigator.vendor)&&"number"==typeof window.orientation}function Aa(e){return e&&90===Math.abs(window.orientation)}function Na(e,t){return e?Math[t?"max":"min"](screen.width,screen.height):screen.width}function ja(e){return Math.min(window.innerWidth,document.documentElement.clientWidth,e)||e}const Ra=["GET","OPTIONS","HEAD","POST","PUT","DELETE","TRACE","CONNECT","PATCH"];function Ia(e){return function(){try{return e.apply(e,arguments)}catch(t){console.error(t)}}}let Ba=1;const Ma={};function Fa(e,t,n){if("number"==typeof e){const o=Ma[e];if(o)return o.keepAlive||delete Ma[e],o.callback(t,n)}return t}const Va="success",Wa="fail",Ha="complete";function Ua(e,t={},{beforeAll:n,beforeSuccess:o}={}){I(t)||(t={});const{success:r,fail:i,complete:s}=function(e){const t={};for(const n in e){const o=e[n];O(o)&&(t[n]=Ia(o),delete e[n])}return t}(t),a=O(r),l=O(i),c=O(s),u=Ba++;return function(e,t,n,o=!1){Ma[e]={name:t,keepAlive:o,callback:n}}(u,e,(u=>{(u=u||{}).errMsg=function(e,t){return e&&-1!==e.indexOf(":fail")?t+e.substring(e.indexOf(":fail")):t+":ok"}(u.errMsg,e),O(n)&&n(u),u.errMsg===e+":ok"?(O(o)&&o(u,t),a&&r(u)):l&&i(u),c&&s(u)})),u}const Da="success",za="fail",qa="complete",Xa={},Ya={};function Ka(e,t){return function(n){return e(n,t)||n}}function Ja(e,t,n){let o=!1;for(let r=0;r<e.length;r++){const i=e[r];if(o)o=Promise.resolve(Ka(i,n));else{const e=i(t,n);if(N(e)&&(o=Promise.resolve(e)),!1===e)return{then(){},catch(){}}}}return o||{then:e=>e(t),catch(){}}}function Za(e,t={}){return[Da,za,qa].forEach((n=>{const o=e[n];if(!k(o))return;const r=t[n];t[n]=function(e){Ja(o,e,t).then((e=>O(r)&&r(e)||e))}})),t}function Ga(e,t){const n=[];k(Xa.returnValue)&&n.push(...Xa.returnValue);const o=Ya[e];return o&&k(o.returnValue)&&n.push(...o.returnValue),n.forEach((e=>{t=e(t)||t})),t}function Qa(e){const t=Object.create(null);Object.keys(Xa).forEach((e=>{"returnValue"!==e&&(t[e]=Xa[e].slice())}));const n=Ya[e];return n&&Object.keys(n).forEach((e=>{"returnValue"!==e&&(t[e]=(t[e]||[]).concat(n[e]))})),t}function el(e,t,n,o){const r=Qa(e);if(r&&Object.keys(r).length){if(k(r.invoke)){return Ja(r.invoke,n).then((n=>t(Za(Qa(e),n),...o)))}return t(Za(r,n),...o)}return t(n,...o)}function tl(e,t){return(n={},...o)=>function(e){return!(!I(e)||![Va,Wa,Ha].find((t=>O(e[t]))))}(n)?Ga(e,el(e,t,n,o)):Ga(e,new Promise(((r,i)=>{el(e,t,w(n,{success:r,fail:i}),o)})))}function nl(e,t,n,o){return Fa(e,w({errMsg:t+":fail"+(n?" "+n:"")},o))}function ol(e,t,n,o){if(o&&o.beforeInvoke){const e=o.beforeInvoke(t);if(L(e))return e}const r=function(e,t){const n=e[0];if(!t||!I(t.formatArgs)&&I(n))return;const o=t.formatArgs,r=Object.keys(o);for(let i=0;i<r.length;i++){const t=r[i],s=o[t];if(O(s)){const o=s(e[0][t],n);if(L(o))return o}else C(n,t)||(n[t]=s)}}(t,o);if(r)return r}function rl(e,t,n,o){return n=>{const r=Ua(e,n,o),i=ol(0,[n],0,o);return i?nl(r,e,i):t(n,{resolve:t=>function(e,t,n){return Fa(e,w(n||{},{errMsg:t+":ok"}))}(r,e,t),reject:(t,n)=>nl(r,e,function(e){return!e||L(e)?e:e.stack?(console.error(e.message+"\n"+e.stack),e.message):e}(t),n)})}}function il(e,t,n,o){return tl(e,rl(e,t,0,o))}function sl(e,t,n,o){return function(e,t,n,o){return(...e)=>{const n=ol(0,e,0,o);if(n)throw new Error(n);return t.apply(null,e)}}(0,t,0,o)}let al=!1,ll=0,cl=0,ul=960,dl=375,fl=750;function pl(){const{platform:e,pixelRatio:t,windowWidth:n}=function(){const e=Pa(),t=ja(Na(e,Aa(e)));return{platform:Ea?"ios":"other",pixelRatio:window.devicePixelRatio,windowWidth:t}}();ll=n,cl=t,al="ios"===e}function hl(e,t){const n=Number(e);return isNaN(n)?t:n}const gl=sl(0,((e,t)=>{if(0===ll&&(pl(),function(){const e=__uniConfig.globalStyle||{};ul=hl(e.rpxCalcMaxDeviceWidth,960),dl=hl(e.rpxCalcBaseDeviceWidth,375),fl=hl(e.rpxCalcBaseDeviceWidth,750)}()),0===(e=Number(e)))return 0;let n=t||ll;n=e===fl||n<=ul?n:dl;let o=e/750*n;return o<0&&(o=-o),o=Math.floor(o+1e-4),0===o&&(o=1!==cl&&al?.5:1),e<0?-o:o})),ml=sl(0,(()=>{const e=Bc();return e&&e.$vm?e.$vm.$locale:Ui().getLocale()})),vl={onUnhandledRejection:[],onPageNotFound:[],onError:[],onShow:[],onHide:[]};const yl="json",bl=["text","arraybuffer"],_l=encodeURIComponent;ArrayBuffer,Boolean;const wl={formatArgs:{method(e,t){var n,o;t.method=(n=(e||"").toUpperCase(),o=Ra,n&&-1!==o.indexOf(n)?n:o[0])},data(e,t){t.data=e||""},url(e,t){t.method===Ra[0]&&I(t.data)&&Object.keys(t.data).length&&(t.url=function(e,t){let n=e.split("#");const o=n[1]||"";n=n[0].split("?");let r=n[1]||"";e=n[0];const i=r.split("&").filter((e=>e)),s={};i.forEach((e=>{const t=e.split("=");s[t[0]]=t[1]}));for(const a in t)if(C(t,a)){let e=t[a];null==e?e="":I(e)&&(e=JSON.stringify(e)),s[_l(a)]=_l(e)}return r=Object.keys(s).map((e=>`${e}=${s[e]}`)).join("&"),e+(r?"?"+r:"")+(o?"#"+o:"")}(e,t.data))},header(e,t){const n=t.header=e||{};t.method!==Ra[0]&&(Object.keys(n).find((e=>"content-type"===e.toLowerCase()))||(n["Content-Type"]="application/json"))},dataType(e,t){t.dataType=(e||yl).toLowerCase()},responseType(e,t){t.responseType=(e||"").toLowerCase(),-1===bl.indexOf(t.responseType)&&(t.responseType="text")}}};const xl={url:{type:String,required:!0}};Sl(["slide-in-right","slide-in-left","slide-in-top","slide-in-bottom","fade-in","zoom-out","zoom-fade-out","pop-in","none"]),Sl(["slide-out-right","slide-out-left","slide-out-top","slide-out-bottom","fade-out","zoom-in","zoom-fade-in","pop-out","none"]);function Sl(e){return{animationType:{type:String,validator(t){if(t&&-1===e.indexOf(t))return"`"+t+"` is not supported for `animationType` (supported values are: `"+e.join("`|`")+"`)"}},animationDuration:{type:Number}}}const Cl=ia(),kl=ia();function El(){}const Tl={cursorSpacing:{type:[Number,String],default:0},showConfirmBar:{type:[Boolean,String],default:"auto"},adjustPosition:{type:[Boolean,String],default:!0},autoBlur:{type:[Boolean,String],default:!1}};function $l(e,t,n){function o(e){const t=Rr((()=>0===String(navigator.vendor).indexOf("Apple")));e.addEventListener("focus",(()=>{clearTimeout(undefined),document.addEventListener("click",El,!1)}));e.addEventListener("blur",(()=>{t.value&&e.blur(),document.removeEventListener("click",El,!1),t.value&&document.documentElement.scrollTo(document.documentElement.scrollLeft,document.documentElement.scrollTop)}))}Nn((()=>t.value),(e=>e&&o(e)))}const Ol=ae(!0),Ll=[];let Pl,Al=0;const Nl=e=>Ll.forEach((t=>t.userAction=e));function jl(e={userAction:!1}){if(!Pl){["touchstart","touchmove","touchend","mousedown","mouseup"].forEach((e=>{document.addEventListener(e,(function(){!Al&&Nl(!0),Al++,setTimeout((()=>{!--Al&&Nl(!1)}),0)}),Ol)})),Pl=!0}Ll.push(e)}function Rl(){const e=Pt({userAction:!1});return Kn((()=>{jl(e)})),Gn((()=>{!function(e){const t=Ll.indexOf(e);t>=0&&Ll.splice(t,1)}(e)})),{state:e}}function Il(e,t){const n=document.activeElement;if(!n)return t({});const o={};["input","textarea"].includes(n.tagName.toLowerCase())&&(o.start=n.selectionStart,o.end=n.selectionEnd),t(o)}const Bl=function(){var e,t,n;e=Ts(),n=Il,t=Ki(e,t="getSelectedTextRange"),Yi[t]||(Yi[t]=n)};function Ml(e,t){return"number"===t&&isNaN(Number(e))&&(e=""),null===e?"":String(e)}const Fl=["none","text","decimal","numeric","tel","search","email","url"],Vl=w({},{name:{type:String,default:""},modelValue:{type:[String,Number],default:""},value:{type:[String,Number],default:""},disabled:{type:[Boolean,String],default:!1},autoFocus:{type:[Boolean,String],default:!1},focus:{type:[Boolean,String],default:!1},cursor:{type:[Number,String],default:-1},selectionStart:{type:[Number,String],default:-1},selectionEnd:{type:[Number,String],default:-1},type:{type:String,default:"text"},password:{type:[Boolean,String],default:!1},placeholder:{type:String,default:""},placeholderStyle:{type:String,default:""},placeholderClass:{type:String,default:""},maxlength:{type:[Number,String],default:140},confirmType:{type:String,default:"done"},confirmHold:{type:Boolean,default:!1},ignoreCompositionEvent:{type:Boolean,default:!0},step:{type:String,default:"0.000000000000000001"},inputmode:{type:String,default:void 0,validator:e=>!!~Fl.indexOf(e)}},Tl),Wl=["input","focus","blur","update:value","update:modelValue","update:focus","compositionstart","compositionupdate","compositionend","keyboardheightchange"];function Hl(e,t,n,o){const r=pe((n=>{t.value=Ml(n,e.type)}),100,{setTimeout:setTimeout,clearTimeout:clearTimeout});Nn((()=>e.modelValue),r),Nn((()=>e.value),r);const i=function(e,t){let n,o,r=0;const i=function(...i){const s=Date.now();clearTimeout(n),o=()=>{o=null,r=s,e.apply(this,i)},s-r<t?n=setTimeout(o,t-(s-r)):o()};return i.cancel=function(){clearTimeout(n),o=null},i.flush=function(){clearTimeout(n),o&&o()},i}(((e,t)=>{r.cancel(),n("update:modelValue",t.value),n("update:value",t.value),o("input",e,t)}),100);return Yn((()=>{r.cancel(),i.cancel()})),{trigger:o,triggerInput:(e,t,n)=>{r.cancel(),i(e,t),n&&i.flush()}}}function Ul(e,t){Rl();const n=Rr((()=>e.autoFocus||e.focus));function o(){if(!n.value)return;const e=t.value;e?e.focus():setTimeout(o,100)}Nn((()=>e.focus),(e=>{e?o():function(){const e=t.value;e&&e.blur()}()})),Kn((()=>{n.value&&un(o)}))}function Dl(e,t,n,o){Bl();const{fieldRef:r,state:i,trigger:s}=function(e,t,n){const o=zt(null),r=ga(t,n),i=Rr((()=>{const t=Number(e.selectionStart);return isNaN(t)?-1:t})),s=Rr((()=>{const t=Number(e.selectionEnd);return isNaN(t)?-1:t})),a=Rr((()=>{const t=Number(e.cursor);return isNaN(t)?-1:t})),l=Rr((()=>{var t=Number(e.maxlength);return isNaN(t)?140:t})),c=Ml(e.modelValue,e.type)||Ml(e.value,e.type),u=Pt({value:c,valueOrigin:c,maxlength:l,focus:e.focus,composing:!1,selectionStart:i,selectionEnd:s,cursor:a});return Nn((()=>u.focus),(e=>n("update:focus",e))),Nn((()=>u.maxlength),(e=>u.value=u.value.slice(0,e))),{fieldRef:o,state:u,trigger:r}}(e,t,n),{triggerInput:a}=Hl(e,i,n,s);Ul(e,r),$l(0,r);const{state:l}=function(){const e=Pt({attrs:{}});return Kn((()=>{let t=Tr();for(;t;){const n=t.type.__scopeId;n&&(e.attrs[n]=""),t=t.proxy&&"page"===t.proxy.$mpType?null:t.parent}})),{state:e}}();!function(e,t){const n=Pn(ma,!1);if(!n)return;const o=Tr(),r={submit(){const n=o.proxy;return[n[e],L(t)?n[t]:t.value]},reset(){L(t)?o.proxy[t]="":t.value=""}};n.addField(r),Gn((()=>{n.removeField(r)}))}("name",i),function(e,t,n,o,r,i){function s(){const n=e.value;n&&t.focus&&t.selectionStart>-1&&t.selectionEnd>-1&&"number"!==n.type&&(n.selectionStart=t.selectionStart,n.selectionEnd=t.selectionEnd)}function a(){const n=e.value;n&&t.focus&&t.selectionStart<0&&t.selectionEnd<0&&t.cursor>-1&&"number"!==n.type&&(n.selectionEnd=n.selectionStart=t.cursor)}function l(e){return"number"===e.type?null:e.selectionEnd}Nn([()=>t.selectionStart,()=>t.selectionEnd],s),Nn((()=>t.cursor),a),Nn((()=>e.value),(function(){const c=e.value;if(!c)return;const u=function(e,o){e.stopPropagation(),O(i)&&!1===i(e,t)||(t.value=c.value,t.composing&&n.ignoreCompositionEvent||r(e,{value:c.value,cursor:l(c)},o))};function d(e){n.ignoreCompositionEvent||o(e.type,e,{value:e.data})}c.addEventListener("change",(e=>e.stopPropagation())),c.addEventListener("focus",(function(e){t.focus=!0,o("focus",e,{value:t.value}),s(),a()})),c.addEventListener("blur",(function(e){t.composing&&(t.composing=!1,u(e,!0)),t.focus=!1,o("blur",e,{value:t.value,cursor:l(e.target)})})),c.addEventListener("input",u),c.addEventListener("compositionstart",(e=>{e.stopPropagation(),t.composing=!0,d(e)})),c.addEventListener("compositionend",(e=>{e.stopPropagation(),t.composing&&(t.composing=!1,u(e)),d(e)})),c.addEventListener("compositionupdate",d)}))}(r,i,e,s,a,o);return{fieldRef:r,state:i,scopedAttrsState:l,fixDisabledColor:0===String(navigator.vendor).indexOf("Apple")&&CSS.supports("image-orientation:from-image"),trigger:s}}const zl=ca({name:"Input",props:w({},Vl,{placeholderClass:{type:String,default:"input-placeholder"},textContentType:{type:String,default:""}}),emits:["confirm",...Wl],setup(e,{emit:t}){const n=["text","number","idcard","digit","password","tel"],o=["off","one-time-code"],r=Rr((()=>{let t="";switch(e.type){case"text":"search"===e.confirmType&&(t="search");break;case"idcard":t="text";break;case"digit":t="number";break;default:t=~n.includes(e.type)?e.type:"text"}return e.password?"password":t})),i=Rr((()=>{const t=o.indexOf(e.textContentType),n=o.indexOf(U(e.textContentType));return o[-1!==t?t:-1!==n?n:0]}));let s,a=zt("");const l=zt(null),{fieldRef:c,state:u,scopedAttrsState:d,fixDisabledColor:f,trigger:p}=Dl(e,l,t,((e,t)=>{const n=e.target;if("number"===r.value){if(s&&(n.removeEventListener("blur",s),s=null),n.validity&&!n.validity.valid){if((!a.value||!n.value)&&"-"===e.data||"-"===a.value[0]&&"deleteContentBackward"===e.inputType)return a.value="-",t.value="",s=()=>{a.value=n.value=""},n.addEventListener("blur",s),!1;if(a.value)if(-1!==a.value.indexOf(".")){if("."!==e.data&&"deleteContentBackward"===e.inputType){const e=a.value.indexOf(".");return a.value=n.value=t.value=a.value.slice(0,e),!0}}else if("."===e.data)return a.value+=".",s=()=>{a.value=n.value=a.value.slice(0,-1)},n.addEventListener("blur",s),!1;return a.value=t.value=n.value="-"===a.value?"":a.value,!1}a.value=n.value;const o=t.maxlength;if(o>0&&n.value.length>o)return n.value=n.value.slice(0,o),t.value=n.value,!1}}));Nn((()=>u.value),(t=>{"number"!==e.type||"-"===a.value&&""===t||(a.value=t)}));const h=["number","digit"],g=Rr((()=>h.includes(e.type)?e.step:""));function m(t){if("Enter"!==t.key)return;const n=t.target;t.stopPropagation(),p("confirm",t,{value:n.value}),!e.confirmHold&&n.blur()}return()=>{let t=e.disabled&&f?gr("input",{key:"disabled-input",ref:c,value:u.value,tabindex:"-1",readonly:!!e.disabled,type:r.value,maxlength:u.maxlength,step:g.value,class:"uni-input-input",onFocus:e=>e.target.blur()},null,40,["value","readonly","type","maxlength","step","onFocus"]):ro(gr("input",{key:"input",ref:c,"onUpdate:modelValue":e=>u.value=e,disabled:!!e.disabled,type:r.value,maxlength:u.maxlength,step:g.value,enterkeyhint:e.confirmType,pattern:"number"===e.type?"[0-9]*":void 0,class:"uni-input-input",autocomplete:i.value,onKeyup:m,inputmode:e.inputmode},null,40,["onUpdate:modelValue","disabled","type","maxlength","step","enterkeyhint","pattern","autocomplete","onKeyup","inputmode"]),[[bi,u.value]]);return gr("uni-input",{ref:l},[gr("div",{class:"uni-input-wrapper"},[ro(gr("div",xr(d.attrs,{style:e.placeholderStyle,class:["uni-input-placeholder",e.placeholderClass]}),[e.placeholder],16),[[wi,!(u.value.length||"-"===a.value)]]),"search"===e.confirmType?gr("form",{action:"",onSubmit:e=>e.preventDefault(),class:"uni-input-form"},[t],40,["onSubmit"]):t])],512)}}}),ql=["navigate","redirect","switchTab","reLaunch","navigateBack"],Xl=["slide-in-right","slide-in-left","slide-in-top","slide-in-bottom","fade-in","zoom-out","zoom-fade-out","pop-in","none"],Yl=["slide-out-right","slide-out-left","slide-out-top","slide-out-bottom","fade-out","zoom-in","zoom-fade-in","pop-out","none"],Kl={hoverClass:{type:String,default:"navigator-hover"},url:{type:String,default:""},openType:{type:String,default:"navigate",validator:e=>Boolean(~ql.indexOf(e))},delta:{type:Number,default:1},hoverStartTime:{type:[Number,String],default:50},hoverStayTime:{type:[Number,String],default:600},exists:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!1},animationType:{type:String,default:"",validator:e=>!e||Xl.concat(Yl).includes(e)},animationDuration:{type:[String,Number],default:300}};w({},Kl,{renderLink:{type:Boolean,default:!0}});const Jl=ca({name:"Switch",props:{name:{type:String,default:""},checked:{type:[Boolean,String],default:!1},type:{type:String,default:"switch"},id:{type:String,default:""},disabled:{type:[Boolean,String],default:!1},color:{type:String,default:""}},emits:["change"],setup(e,{emit:t}){const n=zt(null),o=zt(e.checked),r=function(e,t){const n=Pn(ma,!1),o=Pn(ya,!1),r={submit:()=>{const n=["",null];return e.name&&(n[0]=e.name,n[1]=t.value),n},reset:()=>{t.value=!1}};n&&(n.addField(r),Qn((()=>{n.removeField(r)})));return o}(e,o),i=ga(n,t);Nn((()=>e.checked),(e=>{o.value=e}));const s=t=>{e.disabled||(o.value=!o.value,i("change",t,{value:o.value}))};return r&&(r.addHandler(s),Gn((()=>{r.removeHandler(s)}))),_a(e,{"label-click":s}),()=>{const{color:t,type:r}=e,i=pa(e,"disabled"),a={};return t&&o.value&&(a.backgroundColor=t,a.borderColor=t),gr("uni-switch",xr({ref:n},i,{onClick:s}),[gr("div",{class:"uni-switch-wrapper"},[ro(gr("div",{class:["uni-switch-input",[o.value?"uni-switch-input-checked":""]],style:a},null,6),[[wi,"switch"===r]]),ro(gr("div",{class:"uni-checkbox-input"},[o.value?Ss("M1.952 18.080q-0.32-0.352-0.416-0.88t0.128-0.976l0.16-0.352q0.224-0.416 0.64-0.528t0.8 0.176l6.496 4.704q0.384 0.288 0.912 0.272t0.88-0.336l17.312-14.272q0.352-0.288 0.848-0.256t0.848 0.352l-0.416-0.416q0.32 0.352 0.32 0.816t-0.32 0.816l-18.656 18.912q-0.32 0.352-0.8 0.352t-0.8-0.32l-7.936-8.064z",e.color,22):""],512),[[wi,"checkbox"===r]])])],16,["onClick"])}}});const Zl={ensp:" ",emsp:" ",nbsp:" "};function Gl(e,t){return e.replace(/\\n/g,"\n").split("\n").map((e=>function(e,{space:t,decode:n}){if(!e)return e;t&&Zl[t]&&(e=e.replace(/ /g,Zl[t]));if(!n)return e;return e.replace(/&nbsp;/g,Zl.nbsp).replace(/&ensp;/g,Zl.ensp).replace(/&emsp;/g,Zl.emsp).replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&amp;/g,"&").replace(/&quot;/g,'"').replace(/&apos;/g,"'")}(e,t)))}const Ql=ca({name:"Text",props:{selectable:{type:[Boolean,String],default:!1},space:{type:String,default:""},decode:{type:[Boolean,String],default:!1}},setup:(e,{slots:t})=>()=>{const n=[];return t.default&&t.default().forEach((t=>{if(8&t.shapeFlag&&t.type!==Qo){const o=Gl(t.children,{space:e.space,decode:e.decode}),r=o.length-1;o.forEach(((e,t)=>{(0!==t||e)&&n.push(vr(e)),t!==r&&n.push(gr("br"))}))}else n.push(t)})),gr("uni-text",{selectable:!!e.selectable||null},[gr("span",null,n)],8,["selectable"])}}),ec=ca({name:"View",props:w({},da),setup(e,{slots:t}){const{hovering:n,binding:o}=fa(e);return()=>{const r=e.hoverClass;return r&&"none"!==r?gr("uni-view",xr({class:n.value?r:""},o),[t.default&&t.default()],16):gr("uni-view",null,[t.default&&t.default()])}}});function tc(e,t,n,o){O(t)&&qn(e,t.bind(n),o)}function nc(e,t,n){var o;const r=e.mpType||n.$mpType;if(r&&"component"!==r&&(Object.keys(e).forEach((o=>{if(function(e,t,n=!0){return!(n&&!O(t))&&(ve.indexOf(e)>-1||0===e.indexOf("on"))}(o,e[o],!1)){const r=e[o];k(r)?r.forEach((e=>tc(o,e,n,t))):tc(o,r,n,t)}})),"page"===r)){t.__isVisible=!0;try{Ps(n,"onLoad",t.attrs.__pageQuery),delete t.attrs.__pageQuery,"preloadPage"!==(null==(o=n.$page)?void 0:o.openType)&&Ps(n,"onShow")}catch(i){console.error(i.message+"\n"+i.stack)}}}function oc(e,t,n){nc(e,t,n)}function rc(e,t,n){return e[t]=n}function ic(e){return function(t,n,o){if(!n)throw t;const r=e._instance;if(!r||!r.proxy)throw t;Ps(r.proxy,"onError",t)}}function sc(e,t){return e?[...new Set([].concat(e,t))]:t}function ac(e){const t=e._context.config;var n;t.errorHandler=be(e,ic),n=t.optionMergeStrategies,ve.forEach((e=>{n[e]=sc}));const o=t.globalProperties;o.$set=rc,o.$applyOptions=oc,function(e){ye.forEach((t=>t(e)))}(e)}const lc=xs("upm");function cc(){return Pn(lc)}function uc(e){const t=function(e){return Pt(JSON.parse(JSON.stringify(Ls(__uniRoutes[0].meta,e))))}(e);return Ln(lc,t),t}function dc(){const e=location.href,t=e.indexOf("?"),n=e.indexOf("#",t>-1?t:0);let o={};t>-1&&(o=fe(e.slice(t+1,n>-1?n:e.length)));const{meta:r}=__uniRoutes[0],i=te(r.route);return{meta:r,query:o,path:i,matched:[{path:i}]}}function fc(){return history.state&&history.state.__id__||1}const pc=window.CSS&&window.CSS.supports;function hc(e){return pc&&(pc(e)||pc.apply(window.CSS,e.split(":")))}const gc=hc("top:env(a)"),mc=hc("top:constant(a)"),vc=(()=>gc?"env":mc?"constant":"")();function yc(e){var t,n;ws({"--window-top":(n=0,vc?`calc(${n}px + ${vc}(safe-area-inset-top))`:`${n}px`),"--window-bottom":(t=0,vc?`calc(${t}px + ${vc}(safe-area-inset-bottom))`:`${t}px`)})}const bc=new Map;function _c(){const e=[],t=bc.values();for(const n of t)n.$.__isTabBar?n.$.__isActive&&e.push(n):e.push(n);return e}function wc(e){const t=cc();return function(e,t,n,o,r,i){const{id:s,route:a}=o,l=Se(o.navigationBar,__uniConfig.themeConfig,i).titleColor;return{id:s,path:te(a),route:a,fullPath:t,options:n,meta:o,openType:e,eventChannel:r,statusBarStyle:"#ffffff"===l?"light":"dark"}}("navigateTo",__uniRoutes[0].path,{},t)}function xc(e){e.$route;const t=wc();!function(e,t){e.route=t.route,e.$vm=e,e.$page=t,e.$mpType="page",t.meta.isTabBar&&(e.$.__isTabBar=!0,e.$.__isActive=!0)}(e,t),bc.set(Sc(t.path,t.id),e)}function Sc(e,t){return e+"$$"+t}function Cc(e,t){!function(e){const t=Ec(e),{body:n}=document;Tc&&n.removeAttribute(Tc),t&&n.setAttribute(t,""),Tc=t}(e),yc(),function(e){const t="nvue-dir-"+__uniConfig.nvue["flex-direction"];e.isNVue?(document.body.setAttribute("nvue",""),document.body.setAttribute(t,"")):(document.body.removeAttribute("nvue"),document.body.removeAttribute(t))}(t),function(e,t){document.removeEventListener("touchmove",As),$c&&document.removeEventListener("scroll",$c);if(t.disableScroll)return document.addEventListener("touchmove",As);const{onPageScroll:n,onReachBottom:o}=e,r="transparent"===t.navigationBar.type;if(!n&&!o&&!r)return;const i={},s=e.proxy.$page.id;(n||r)&&(i.onPageScroll=function(e,t,n){return o=>{t&&cu.publishHandler("onPageScroll",{scrollTop:o},e),n&&cu.emit(e+".onPageScroll",{scrollTop:o})}}(s,n,r));o&&(i.onReachBottomDistance=t.onReachBottomDistance||50,i.onReachBottom=()=>cu.publishHandler("onReachBottom",{},s));$c=Rs(i),requestAnimationFrame((()=>document.addEventListener("scroll",$c)))}(e,t)}function kc(e){const t=Ec(e);t&&function(e){const t=document.querySelector("uni-page-body");t&&t.setAttribute(e,"")}(t)}function Ec(e){return e.type.__scopeId}let Tc,$c;const Oc={install(e){ac(e),Xs(e),ra(e),e.config.warnHandler||(e.config.warnHandler=Lc)}};function Lc(e,t,n){if(t){if("PageMetaHead"===t.$.type.name)return;const e=t.$.parent;if(e&&"PageMeta"===e.type.name)return}const o=[`[Vue warn]: ${e}`];n.length&&o.push("\n",n),console.warn(...o)}const Pc={class:"uni-async-loading"},Ac=gr("i",{class:"uni-loading"},null,-1),Nc=ua({name:"AsyncLoading",render:()=>(or(),lr("div",Pc,[Ac]))});function jc(){window.location.reload()}const Rc=ua({name:"AsyncError",setup(){zi();const{t:e}=Ui();return()=>gr("div",{class:"uni-async-error",onClick:jc},[e("uni.async.error")],8,["onClick"])}});let Ic;function Bc(){return Ic}function Mc(e){Ic=e,Object.defineProperty(Ic.$.ctx,"$children",{get:()=>_c().map((e=>e.$vm))});const t=Ic.$.appContext.app;t.component(Nc.name)||t.component(Nc.name,Nc),t.component(Rc.name)||t.component(Rc.name,Rc),function(e){e.$vm=e,e.$mpType="app";const t=zt(Ui().getLocale());Object.defineProperty(e,"$locale",{get:()=>t.value,set(e){t.value=e}})}(Ic),function(e,t){const n=e.$options||{};n.globalData=w(n.globalData||{},t),Object.defineProperty(e,"globalData",{get:()=>n.globalData,set(e){n.globalData=e}})}(Ic),na(),as()}function Fc(e,{clone:t,init:n,setup:o,before:r}){t&&(e=w({},e)),r&&r(e);const i=e.setup;return e.setup=(e,t)=>{const r=Tr();n(r.proxy);const s=o(r);if(i)return i(s||e,t)},e}function Vc(e,t){return e&&(e.__esModule||"Module"===e[Symbol.toStringTag])?Fc(e.default,t):Fc(e,t)}function Wc(e){return Vc(e,{clone:!0,init:xc,setup(e){e.$pageInstance=e;const t=dc(),n=ue(t.query);e.attrs.__pageQuery=n,e.proxy.$page.options=n;const o=cc();var r,i,s;return Yn((()=>{Cc(e,o)})),Kn((()=>{kc(e);const{onReady:n}=e;n&&X(n),zc(t)})),Dn((()=>{if(!e.__isVisible){Cc(e,o),e.__isVisible=!0;const{onShow:n}=e;n&&X(n),un((()=>{zc(t)}))}}),"ba",r),function(e,t){Dn(e,"bda",t)}((()=>{if(e.__isVisible&&!e.__isUnload){e.__isVisible=!1;const{onHide:t}=e;t&&X(t)}})),i=o.id,cu.subscribe(Ki(i,"invokeViewApi"),s?s(Ji):Ji),Gn((()=>{!function(e){cu.unsubscribe(Ki(e,"invokeViewApi")),Object.keys(Yi).forEach((t=>{0===t.indexOf(e+".")&&delete Yi[t]}))}(o.id)})),n}})}function Hc(){const{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}=ou(),r=90===Math.abs(Number(window.orientation))?"landscape":"portrait";uu.emit("onResize",{deviceOrientation:r,size:{windowWidth:e,windowHeight:t,screenWidth:n,screenHeight:o}})}function Uc(e){I(e.data)&&"WEB_INVOKE_APPSERVICE"===e.data.type&&uu.emit("onWebInvokeAppService",e.data.data,e.data.pageId)}function Dc(){const{emit:e}=uu;"visible"===document.visibilityState?e("onAppEnterForeground",w({},kl)):e("onAppEnterBackground")}function zc(e){const{tabBarText:t,tabBarIndex:n,route:o}=e.meta;t&&Ps("onTabItemTap",{index:n,text:t,pagePath:o})}const qc=navigator.cookieEnabled&&(window.localStorage||window.sessionStorage)||{};let Xc;function Yc(){if(Xc=Xc||qc.__DC_STAT_UUID,!Xc){Xc=Date.now()+""+Math.floor(1e7*Math.random());try{qc.__DC_STAT_UUID=Xc}catch(e){}}return Xc}function Kc(){if(!0!==__uniConfig.darkmode)return L(__uniConfig.darkmode)?__uniConfig.darkmode:"light";try{return window.matchMedia("(prefers-color-scheme: light)").matches?"light":"dark"}catch(e){return"light"}}function Jc(){let e,t="0",n="",o="phone";const r=navigator.language;if(Ea){e="iOS";const o=Ca.match(/OS\s([\w_]+)\slike/);o&&(t=o[1].replace(/_/g,"."));const r=Ca.match(/\(([a-zA-Z]+);/);r&&(n=r[1])}else if(ka){e="Android";const o=Ca.match(/Android[\s/]([\w\.]+)[;\s]/);o&&(t=o[1]);const r=Ca.match(/\((.+?)\)/),i=r?r[1].split(";"):Ca.split(" "),s=[/\bAndroid\b/i,/\bLinux\b/i,/\bU\b/i,/^\s?[a-z][a-z]$/i,/^\s?[a-z][a-z]-[a-z][a-z]$/i,/\bwv\b/i,/\/[\d\.,]+$/,/^\s?[\d\.,]+$/,/\bBrowser\b/i,/\bMobile\b/i];for(let e=0;e<i.length;e++){const t=i[e];if(t.indexOf("Build")>0){n=t.split("Build")[0].trim();break}let o;for(let e=0;e<s.length;e++)if(s[e].test(t)){o=!0;break}if(!o){n=t.trim();break}}}else if(La)n="iPad",e="iOS",o="pad",t=O(window.BigInt)?"14.0":"13.0";else if(Ta||$a||Oa){n="PC",e="PC",o="pc",t="0";let r=Ca.match(/\((.+?)\)/)[1];if(Ta){switch(e="Windows",Ta[1]){case"5.1":t="XP";break;case"6.0":t="Vista";break;case"6.1":t="7";break;case"6.2":t="8";break;case"6.3":t="8.1";break;case"10.0":t="10"}const n=r&&r.match(/[Win|WOW]([\d]+)/);n&&(t+=` x${n[1]}`)}else if($a){e="macOS";const n=r&&r.match(/Mac OS X (.+)/)||"";t&&(t=n[1].replace(/_/g,"."),-1!==t.indexOf(";")&&(t=t.split(";")[0]))}else if(Oa){e="Linux";const n=r&&r.match(/Linux (.*)/)||"";n&&(t=n[1],-1!==t.indexOf(";")&&(t=t.split(";")[0]))}}else e="Other",t="0",o="unknown";const i=`${e} ${t}`,s=e.toLocaleLowerCase();let a="",l=String(function(){const e=navigator.userAgent,t=e.indexOf("compatible")>-1&&e.indexOf("MSIE")>-1,n=e.indexOf("Edge")>-1&&!t,o=e.indexOf("Trident")>-1&&e.indexOf("rv:11.0")>-1;if(t){new RegExp("MSIE (\\d+\\.\\d+);").test(e);const t=parseFloat(RegExp.$1);return t>6?t:6}return n?-1:o?11:-1}());if("-1"!==l)a="IE";else{const e=["Version","Firefox","Chrome","Edge{0,1}"],t=["Safari","Firefox","Chrome","Edge"];for(let n=0;n<e.length;n++){const o=e[n],r=new RegExp(`(${o})/(\\S*)\\b`);r.test(Ca)&&(a=t[n],l=Ca.match(r)[2])}}let c="portrait";const u=void 0===window.screen.orientation?window.orientation:window.screen.orientation.angle;return c=90===Math.abs(u)?"landscape":"portrait",{deviceBrand:void 0,brand:void 0,deviceModel:n,deviceOrientation:c,model:n,system:i,platform:s,browserName:a.toLocaleLowerCase(),browserVersion:l,language:r,deviceType:o,ua:Ca,osname:e,osversion:t,theme:Kc()}}const Zc=sl(0,(()=>{const e=window.devicePixelRatio,t=Pa(),n=Aa(t),o=Na(t,n),r=function(e,t){return e?Math[t?"min":"max"](screen.height,screen.width):screen.height}(t,n),i=ja(o);let s=window.innerHeight;const a=ys.top,l={left:ys.left,right:i-ys.right,top:ys.top,bottom:s-ys.bottom,width:i-ys.left-ys.right,height:s-ys.top-ys.bottom},{top:c,bottom:u}=function(){const e=document.documentElement.style,t=_s(),n=bs(e,"--window-bottom"),o=bs(e,"--window-left"),r=bs(e,"--window-right"),i=bs(e,"--top-window-height");return{top:t,bottom:n?n+ys.bottom:0,left:o?o+ys.left:0,right:r?r+ys.right:0,topWindowHeight:i||0}}();return s-=c,s-=u,{windowTop:c,windowBottom:u,windowWidth:i,windowHeight:s,pixelRatio:e,screenWidth:o,screenHeight:r,statusBarHeight:a,safeArea:l,safeAreaInsets:{top:ys.top,right:ys.right,bottom:ys.bottom,left:ys.left},screenTop:r-s}}));let Gc,Qc=!0;function eu(){Qc&&(Gc=Jc())}const tu=sl(0,(()=>{eu();const{deviceBrand:e,deviceModel:t,brand:n,model:o,platform:r,system:i,deviceOrientation:s,deviceType:a}=Gc;return{brand:n,deviceBrand:e,deviceModel:t,devicePixelRatio:window.devicePixelRatio,deviceId:Yc(),deviceOrientation:s,deviceType:a,model:o,platform:r,system:i}})),nu=sl(0,(()=>{eu();const{theme:e,language:t,browserName:n,browserVersion:o}=Gc;return{appId:__uniConfig.appId,appName:__uniConfig.appName,appVersion:__uniConfig.appVersion,appVersionCode:__uniConfig.appVersionCode,appLanguage:ml?ml():t,enableDebug:!1,hostSDKVersion:void 0,hostPackageName:void 0,hostFontSizeSetting:void 0,hostName:n,hostVersion:o,hostTheme:e,hostLanguage:t,language:t,SDKVersion:"",theme:e,version:""}})),ou=sl(0,(()=>{Qc=!0,eu(),Qc=!1;const e=Zc(),t=tu(),n=nu();Qc=!0;const{ua:o,browserName:r,browserVersion:i,osname:s,osversion:a}=Gc,l=w(e,t,n,{ua:o,browserName:r,browserVersion:i,uniPlatform:"web",uniCompileVersion:__uniConfig.compilerVersion,uniRuntimeVersion:__uniConfig.compilerVersion,fontSizeSetting:void 0,osName:s.toLocaleLowerCase(),osVersion:a,osLanguage:void 0,osTheme:void 0});return delete l.screenTop,delete l.enableDebug,__uniConfig.darkmode||delete l.theme,function(e){let t={};return I(e)&&Object.keys(e).sort().forEach((n=>{const o=n;t[o]=e[o]})),Object.keys(t)?t:e}(l)}));jl();const ru=il("request",(({url:e,data:t,header:n,method:o,dataType:r,responseType:i,withCredentials:s,timeout:a=__uniConfig.networkTimeout.request},{resolve:l,reject:c})=>{let u=null;const d=function(e){const t=Object.keys(e).find((e=>"content-type"===e.toLowerCase()));if(!t)return;const n=e[t];if(0===n.indexOf("application/json"))return"json";if(0===n.indexOf("application/x-www-form-urlencoded"))return"urlencoded";return"string"}(n);if("GET"!==o)if(L(t)||t instanceof ArrayBuffer)u=t;else if("json"===d)try{u=JSON.stringify(t)}catch(g){u=t.toString()}else if("urlencoded"===d){const e=[];for(const n in t)C(t,n)&&e.push(encodeURIComponent(n)+"="+encodeURIComponent(t[n]));u=e.join("&")}else u=t.toString();const f=new XMLHttpRequest,p=new iu(f);f.open(o,e);for(const m in n)C(n,m)&&f.setRequestHeader(m,n[m]);const h=setTimeout((function(){f.onload=f.onabort=f.onerror=null,p.abort(),c("timeout")}),a);return f.responseType=i,f.onload=function(){clearTimeout(h);const e=f.status;let t="text"===i?f.responseText:f.response;if("text"===i&&"json"===r)try{t=JSON.parse(t)}catch(g){}l({data:t,statusCode:e,header:su(f.getAllResponseHeaders()),cookies:[]})},f.onabort=function(){clearTimeout(h),c("abort")},f.onerror=function(){clearTimeout(h),c()},f.withCredentials=s,f.send(u),p}),0,wl);class iu{constructor(e){this._xhr=e}abort(){this._xhr&&(this._xhr.abort(),delete this._xhr)}onHeadersReceived(e){throw new Error("Method not implemented.")}offHeadersReceived(e){throw new Error("Method not implemented.")}}function su(e){const t={};return e.split("\n").forEach((e=>{const n=e.match(/(\S+\s*):\s*(.*)/);n&&3===n.length&&(t[n[1]]=n[2])})),t}function au(e){function t(){var t;t=e.navigationBar.titleText,document.title=t,uu.emit("onNavigationBarChange",{titleText:t})}var n;jn(t,null,n),Hn(t)}const lu=ua({name:"Layout",setup(e,{emit:t}){const n=zt(null);ws({"--status-bar-height":"0px","--top-window-height":"0px","--window-left":"0px","--window-right":"0px","--window-margin":"0px","--tab-bar-height":"0px"});const{layoutState:o,windowState:r}=function(){dc();{const e=Pt({marginWidth:0,leftWindowWidth:0,rightWindowWidth:0});return Nn((()=>e.marginWidth),(e=>ws({"--window-margin":e+"px"}))),Nn((()=>e.leftWindowWidth+e.marginWidth),(e=>{ws({"--window-left":e+"px"})})),Nn((()=>e.rightWindowWidth+e.marginWidth),(e=>{ws({"--window-right":e+"px"})})),{layoutState:e,windowState:Rr((()=>({})))}}}();!function(e,t){const n=dc();function o(){const o=document.body.clientWidth,r=_c();let i={};if(r.length>0){i=r[r.length-1].$page.meta}else{const e=Is(n.path,!0);e&&(i=e.meta)}const s=parseInt(String((C(i,"maxWidth")?i.maxWidth:__uniConfig.globalStyle.maxWidth)||Number.MAX_SAFE_INTEGER));let a=!1;a=o>s,a&&s?(e.marginWidth=(o-s)/2,un((()=>{const e=t.value;e&&e.setAttribute("style","max-width:"+s+"px;margin:0 auto;")}))):(e.marginWidth=0,un((()=>{const e=t.value;e&&e.removeAttribute("style")})))}Nn([()=>n.path],o),Kn((()=>{o(),window.addEventListener("resize",o)}))}(o,n);const i=function(e){const t=zt(!1);return Rr((()=>({"uni-app--showtabbar":e&&e.value,"uni-app--maxwidth":t.value})))}(!1);return()=>{const e=gr(__uniRoutes[0].component);return gr("uni-app",{ref:n,class:i.value},[e,!1],2)}}});const cu=w(Zi,{publishHandler(e,t,n){uu.subscribeHandler(e,t,n)}}),uu=w(Js,{publishHandler(e,t,n){cu.subscribeHandler(e,t,n)}}),du=ua({name:"PageBody",setup:(e,t)=>()=>gr(Zo,null,[!1,gr("uni-page-wrapper",null,[gr("uni-page-body",null,[po(t.slots,"default")])],16)])}),fu=ua({name:"Page",setup(e,t){const n=uc(fc());return n.navigationBar,au(n),()=>gr("uni-page",{"data-page":n.route},[pu(t)])}});function pu(e){return or(),lr(du,{key:0},{default:kn((()=>[po(e.slots,"page")])),_:3})}const hu={loading:"AsyncLoading",error:"AsyncError",delay:200,timeout:6e4,suspensible:!0};window.uni={},window.wx={},window.rpx2px=gl;const gu=Object.assign({}),mu=Object.assign;window.__uniConfig=mu({globalStyle:{backgroundColor:"#F8F8F8",titleNView:!1,navigationBar:{backgroundColor:"#F8F8F8",titleText:"uni-app",type:"default",style:"custom",titleColor:"#000000"},isNVue:!1},uniIdRouter:{},compilerVersion:"3.96"},{appId:"__UNI__23FA02F",appName:"Print",appVersion:"1.0.0",appVersionCode:"100",async:hu,debug:!1,networkTimeout:{request:6e4,connectSocket:6e4,uploadFile:6e4,downloadFile:6e4},sdkConfigs:{},qqMapKey:void 0,googleMapKey:void 0,aMapKey:void 0,aMapSecurityJsCode:void 0,aMapServiceHost:void 0,nvue:{"flex-direction":"column"},locale:"",fallbackLocale:"",locales:Object.keys(gu).reduce(((e,t)=>{const n=t.replace(/\.\/locale\/(uni-app.)?(.*).json/,"$2");return mu(e[n]||(e[n]={}),gu[t].default),e}),{}),router:{mode:"hash",base:"/",assets:"assets",routerBase:"/"},darkmode:!1,themeConfig:{}}),window.__uniLayout=window.__uniLayout||{};const vu={delay:hu.delay,timeout:hu.timeout,suspensible:hu.suspensible};hu.loading&&(vu.loadingComponent={name:"SystemAsyncLoading",render:()=>gr(so(hu.loading))}),hu.error&&(vu.errorComponent={name:"SystemAsyncError",render:()=>gr(so(hu.error))});const yu=()=>function(t,n,o){if(!n||0===n.length)return t();const r=document.getElementsByTagName("link");return Promise.all(n.map((t=>{if((t=function(e){return"/"+e}(t))in e)return;e[t]=!0;const n=t.endsWith(".css"),i=n?'[rel="stylesheet"]':"";if(o)for(let e=r.length-1;e>=0;e--){const o=r[e];if(o.href===t&&(!n||"stylesheet"===o.rel))return}else if(document.querySelector(`link[href="${t}"]${i}`))return;const s=document.createElement("link");return s.rel=n?"stylesheet":"modulepreload",n||(s.as="script",s.crossOrigin=""),s.href=t,document.head.appendChild(s),n?new Promise(((e,n)=>{s.addEventListener("load",e),s.addEventListener("error",(()=>n(new Error(`Unable to preload CSS for ${t}`))))})):void 0}))).then((()=>t()))}((()=>import("./pages-index-index.a78ada74.js")),["assets/pages-index-index.a78ada74.js","assets/index-e1707285.css"]).then((e=>Wc(e.default||e))),bu=function(e){O(e)&&(e={loader:e});const{loader:t,loadingComponent:n,errorComponent:o,delay:r=200,timeout:i,suspensible:s=!0,onError:a}=e;let l,c=null,u=0;const d=()=>{let e;return c||(e=c=t().catch((e=>{if(e=e instanceof Error?e:new Error(String(e)),a)return new Promise(((t,n)=>{a(e,(()=>t((u++,c=null,d()))),(()=>n(e)),u+1)}));throw e})).then((t=>e!==c&&c?c:(t&&(t.__esModule||"Module"===t[Symbol.toStringTag])&&(t=t.default),l=t,t))))};return Mn({name:"AsyncComponentWrapper",__asyncLoader:d,get __asyncResolved(){return l},setup(){const e=Er;if(l)return()=>Vn(l,e);const t=t=>{c=null,Qt(t,e,13,!o)};if(s&&e.suspense||Pr)return d().then((t=>()=>Vn(t,e))).catch((e=>(t(e),()=>o?gr(o,{error:e}):null)));const a=zt(!1),u=zt(),f=zt(!!r);return r&&setTimeout((()=>{f.value=!1}),r),null!=i&&setTimeout((()=>{if(!a.value&&!u.value){const e=new Error(`Async component timed out after ${i}ms.`);t(e),u.value=e}}),i),d().then((()=>{a.value=!0,e.parent&&Wn(e.parent.vnode)&&dn(e.parent.update)})).catch((e=>{t(e),u.value=e})),()=>a.value&&l?Vn(l,e):u.value&&o?gr(o,{error:u.value}):n&&!f.value?gr(n):void 0}})}(mu({loader:yu},vu));window.__uniRoutes=[{path:"/",alias:"/pages/index/index",component:{setup(){const e=Bc(),t=e&&e.$route&&e.$route.query||{};return()=>{return e=bu,n=t,or(),lr(fu,null,{page:kn((()=>[gr(e,mu({},n,{ref:"page"}),null,512)])),_:1});var e,n}}},loader:yu,meta:{isQuit:!0,isEntry:!0,navigationBar:{titleText:"报表打印",type:"default"},isNVue:!1}}].map((e=>(e.meta.route=(e.alias||e.path).slice(1),e)));const _u={onLaunch:function(){console.log("App Launch")},onShow:function(){console.log("App Show")},onHide:function(){console.log("App Hide")}};Vc(_u,{init:Mc,setup(e){const t=dc();return Yn((()=>{var n;n=e,Object.keys(vl).forEach((e=>{vl[e].forEach((t=>{qn(e,t,n)}))}));const{onLaunch:o,onShow:r,onPageNotFound:i,onError:s}=e,a=function({path:e,query:t}){return w(Cl,{path:e,query:t}),w(kl,Cl),w({},Cl)}({path:t.path.slice(1)||__uniRoutes[0].meta.route,query:ue(t.query)});o&&X(o,a),r&&X(r,a),s&&(e.appContext.config.errorHandler=e=>{X(s,e)})})),Kn((()=>{window.addEventListener("resize",pe(Hc,50,{setTimeout:setTimeout,clearTimeout:clearTimeout})),window.addEventListener("message",Uc),document.addEventListener("visibilitychange",Dc),function(){let e=null;try{e=window.matchMedia("(prefers-color-scheme: dark)")}catch(t){}if(e){let t=e=>{uu.emit("onThemeChange",{theme:e.matches?"dark":"light"})};e.addEventListener?e.addEventListener("change",t):e.addListener(t)}}()})),t.query},before(e){e.mpType="app";const{setup:t}=e,n=()=>(or(),lr(lu));e.setup=(e,o)=>{const r=t&&t(e,o);return O(r)?n:r},e.render=n}}),ki(_u).use(Oc).mount("#app");export{Zo as F,zl as I,a,L as b,lr as c,Kn as d,ar as e,gr as f,kn as g,yr as h,Ql as i,ec as j,vr as k,hr as l,fo as m,n,or as o,ru as p,Jl as q,zt as r,ba as s,f as t,Sa as u,lo as v,Nn as w};
