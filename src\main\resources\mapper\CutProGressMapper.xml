<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zqn.modeldata2.mapper.CutProGressMapper">

    <select id="query" resultType="com.zqn.modeldata2.entity.CutProGress">
        <!---->
        SELECT shp_date , brand_no, dev_type, ord_no, item_no, model_pic, dutyer, model_no, last_no, wo_date,
        tot_qty, c2_qty, c1_qty, c3_qty, c4_qty, c5_qty, c6_qty,c7_qty, c_f_status, u_f_status, cin_flag, cin_date,
        cin_area,pb_desc
            FROM vw_mk2_c_pschedule
        <where>
            <if test="brand != null and brand != ''">
                AND UPPER(brand_no) = UPPER(#{brand})
            </if>
            <if test="devType != null and devType != '' and devType != 'select'">
                AND dev_type = #{devType}
            </if>
            <if test="cinStartTime != null">
                and cin_date between #{cinStartTime} and #{cinEndTime}
            </if>
            <if test="externalStatus != null and externalStatus == 0">
                and c7_qty = 0
            </if>
            <if test="externalStatus != null and externalStatus == 1">
                and c7_qty > 0
            </if>
        </where>
        order by shp_date
    </select>

    <select id="queryTotalQty" resultType="com.zqn.modeldata2.entity.CutProGress">
        <!---->
        SELECT
        SUM(TOT_QTY) AS total_order_quantity1,
        SUM(CASE WHEN C1_QTY != '/' THEN TO_NUMBER(C1_QTY) ELSE 0 END) AS total_order_quantity2,
        SUM(CASE WHEN C2_QTY != '/' THEN TO_NUMBER(C2_QTY) ELSE 0 END) AS total_order_quantity3,
        SUM(CASE WHEN C3_QTY != '/' THEN TO_NUMBER(C3_QTY) ELSE 0 END) AS total_order_quantity4,
        SUM(CASE WHEN C4_QTY != '/' THEN TO_NUMBER(C4_QTY) ELSE 0 END) AS total_order_quantity5,
        SUM(CASE WHEN C5_QTY != '/' THEN TO_NUMBER(C5_QTY) ELSE 0 END) AS total_order_quantity6,
        SUM(CASE WHEN C6_QTY != '/' THEN TO_NUMBER(C6_QTY) ELSE 0 END) AS total_order_quantity7
        FROM vw_mk2_c_pschedule
        <where>
            <if test="brand != null and brand != ''">
                AND UPPER(brand_no) = UPPER(#{brand})
            </if>
            <if test="devType != null and devType != '' and devType != 'select'">
                AND dev_type = #{devType}
            </if>
            <if test="cinStartTime != null and cinEndTime != null">
                and cin_date between #{cinStartTime} and #{cinEndTime}
            </if>
        </where>
        order by cin_date desc
    </select>

    <select id="queryAllDevType" resultType="java.lang.String">
        select dev_type
        FROM vw_mk2_c_pschedule
        group by dev_type
    </select>


    <update id="update">
        UPDATE gc_sorders
        SET cin_flag = null,
            cin_date = null,
            cin_area = '' --此处用户说先不用赋值，后续会用
        WHERE ord_no = #{ordNo}
          and item_no = #{itemNo}
    </update>
</mapper>