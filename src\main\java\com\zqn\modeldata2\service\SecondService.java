package com.zqn.modeldata2.service;

import com.zqn.modeldata2.entity.CkSmodelp;
import com.zqn.modeldata2.entity.CkSmodelpPlus;

import java.util.List;

public interface SecondService {
    List<CkSmodelp> getSpecification(CkSmodelp ckSmodelp);

    List<CkSmodelpPlus> getSpecificationPlus(CkSmodelp ckSmodelp);

    int addSpecification(CkSmodelp ckSmodelp);

    int updateSpecification(CkSmodelp ckSmodelp);

    int deleteSpecification(CkSmodelp ckSmodelp);

    int batchDeleteSpecification(List<CkSmodelp> ckSmodelpList);

    Integer updateStartSize(CkSmodelp ckSmodelp);

    Integer updateEndSize(CkSmodelp ckSmodelp);
}
