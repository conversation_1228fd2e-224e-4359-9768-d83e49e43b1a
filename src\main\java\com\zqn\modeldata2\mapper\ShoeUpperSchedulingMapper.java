package com.zqn.modeldata2.mapper;

import com.zqn.modeldata2.entity.ShoeUpperScheduling;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ShoeUpperSchedulingMapper {


    List<ShoeUpperScheduling> list(  @Param("empName")  String empName, @Param("beginShpDate")  String beginShpDate, @Param("endShpDate")  String endShpDate, @Param("prdDate")  String prdDate);


    String detail(@Param("madeDept") String madeDept, @Param("prdDate") String prdDate);


}