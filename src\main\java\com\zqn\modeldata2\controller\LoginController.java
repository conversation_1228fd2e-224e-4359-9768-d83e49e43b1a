package com.zqn.modeldata2.controller;

import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.entity.Login;
import com.zqn.modeldata2.service.LoginService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/login")
public class LoginController {
    @Autowired
    private LoginService loginService;

    @PostMapping("/login")
    public R<Login> login(@RequestBody Map<String, String> params) {
        String userId = params.get("userId") != null ? params.get("userId") : "";
        String password = params.get("password") != null ? params.get("password") : "";
        Login result = loginService.login(userId, password);
        return R.success(result);
    }
}