package com.zqn.newmodel.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import com.zqn.newmodel.entity.*;
import com.zqn.newmodel.mapper.NewModelMapper;
import com.zqn.newmodel.service.NewModelService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/8/6 8:10
 */
@Service
public class NewModelServiceImpl implements NewModelService {

    @Resource
    private NewModelMapper newModelMapper;

    @Override
    public NewModel query(String ordNo) {
        NewModel newModel = new NewModel();
        queryHd(ordNo, newModel);
        querySize(ordNo, newModel);
        queryDt(ordNo, newModel);
        return newModel;
    }

    private void queryHd(String ordNo, NewModel newModel) {
        NewModelTitle newModelTitle = newModelMapper.queryHd(ordNo);
        //处理标题
        String devType = newModelTitle.getDev_type();
        String chnSim = newModelTitle.getChn_sim();
        if (devType != null && devType.indexOf("開發試做") > -1) {
            newModelTitle.setTitle(chnSim + "新型體開發指令單(" + devType + ")");
        } else {
            newModelTitle.setTitle(chnSim + "樣品型體明細表(" + devType + ")");
        }
        BufferedImage qrCodeImage = QrCodeUtil.generate(newModelTitle.getOrd_no(), 300, 300);
        // 将 BufferedImage 转换为字节数组
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try {
            ImageIO.write(qrCodeImage, "png", baos);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        byte[] imageBytes = baos.toByteArray();

        // 将字节数组编码为 Base64 字符串
        String base64Image = Base64.getEncoder().encodeToString(imageBytes);
        newModelTitle.setOrd_qr(imageBytes);
        newModel.setTitle(newModelTitle);
    }

    /**
     * @description: 查询尺码
     * @param: ordNo
     * @return: void
     * <AUTHOR> Yang
     * @date: 2024/8/6 10:13
     */
    private void querySize(String ordNo, NewModel newModel) {
        //只执行这个才能查到尺码信息
        Map<String, Object> params = new HashMap<>();
        params.put("i_ord_no", ordNo);
        params.put("i_ord_type", newModel.getTitle().getOrd_type());
        params.put("o_return", "S");
        newModelMapper.pdGcSordereq(params);
        Object o_return = params.get("o_return");
        System.out.println(o_return);
        NewModelSize newModelSize = new NewModelSize();
        List<String> headers = new ArrayList<>();
        List<List<String>> rows = new ArrayList<>();
        headers.add("项次");
        headers.add("日期");
        headers.add("肥度");
        headers.add("左右");

        // 假设这些方法是从你的Mapper类中调用的
        List<NewModelTable2> sizeCaptions = newModelMapper.querySizeCaption(ordNo, newModel.getTitle().getSiz_type());
        List<NewModelTable2> newModelTable2List = newModelMapper.querySize(ordNo);

        // 用于存储动态添加的表头
        Set<String> dynamicHeaders = new HashSet<>();
        HashMap<String, Integer> sizeHeaders = new HashMap<>();

        // 先遍历所有数据，收集有值的size并添加到headers中
        for (NewModelTable2 newModelTable2 : sizeCaptions) {
            for (int j = 1; j <= 30; j++) { // 假设最多有30个尺码
                String methodName = "getSize" + String.format("%02d", j);
                try {
                    Method method = newModelTable2.getClass().getMethod(methodName);
                    Object value = method.invoke(newModelTable2);
                    if (value != null) {
                        if (value instanceof Number) {
                            Number numberValue = (Number) value;
                            if (numberValue.doubleValue() != 0) {
                                String header = String.valueOf(numberValue.intValue());
                                dynamicHeaders.add(header);
                                sizeHeaders.put(header, j); // 存储尺码对应的列索引
                            }
                        }
                    }
                } catch (Exception e) {
                    // 处理异常，例如方法不存在或访问权限问题
                    e.printStackTrace();
                }
            }
        }

        // 将动态表头按数字顺序添加到headers中
        List<String> sortedDynamicHeaders = new ArrayList<>(dynamicHeaders);
        sortedDynamicHeaders.sort(Comparator.comparingInt(Integer::parseInt));
        headers.addAll(headers.indexOf("左右") + 1, sortedDynamicHeaders);

        // 确保第25列是“合计”
        while (headers.size() < 24) {
            headers.add("");
        }
        headers.add(24, "合计");

        int i = 0;
        BigDecimal sizeCount = new BigDecimal("0.0");
        for (NewModelTable2 newModelTable2 : newModelTable2List) {
            i++;
            List<String> row = new ArrayList<>();
            // 先放表头
            row.add(String.valueOf(i));
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("MM/dd");
            String shpDate = simpleDateFormat.format(newModelTable2.getShp_date());
            row.add(shpDate);
            row.add(newModelTable2.getWidth());
            row.add(newModelTable2.getLr_mark());

            // 根据列数获取对应的headers，并利用反射获取size的值
            for (String header : headers.subList(4, headers.size())) {
                if (header.equals("合计")) {
                    BigDecimal count = new BigDecimal("0.0");
                    for (int k = 4; k < row.size(); k++) {
                        String num = row.get(k);
                        if (StrUtil.isNotBlank(num)) {
                            BigDecimal bigDecimal = new BigDecimal(num);
                            count = count.add(bigDecimal);
                            sizeCount = sizeCount.add(bigDecimal);
                        }
                    }
                    row.add(String.valueOf(count)); // 这里可以根据实际需求计算合计值
                } else {
                    if (header.equals("")) {
                        row.add("");
                    } else {
                        Integer columnIndex = sizeHeaders.get(header);
                        String methodName = "getSize" + String.format("%02d", columnIndex);
                        try {
                            Method method = newModelTable2.getClass().getMethod(methodName);
                            Object value = method.invoke(newModelTable2);
                            row.add(value != null ? String.valueOf(value) : "");
                        } catch (Exception e) {
                            // 处理异常，
                            // 例如方法不存在或访问权限问题
                            e.printStackTrace();
                            row.add("");
                        }
                    }
                }
            }
            rows.add(row);
        }
        //以双为单位，/2
//        sizeCount = sizeCount.divide(new BigDecimal("2.0"));
        newModelSize.setSizeCount(sizeCount);
        newModelSize.setHeaders(headers);
        newModelSize.setRows(rows);
        newModel.setSize(newModelSize);
    }

    /**
     * @description: 查询明细
     * @param: ordNo
     * @return: void
     * <AUTHOR> Yang
     * @date: 2024/8/6 10:13
     */
    private void queryDt(String ordNo, NewModel newModel) {
        NewModelTitle title = newModel.getTitle();
        BigDecimal ordQty = title.getOrd_qty();
        String brandNo = title.getBrand_no();
        List<NewModelTable3> newModelTable3List = newModelMapper.queryDt(ordNo, ordQty, brandNo);
        newModel.setTable3(newModelTable3List);
    }
}
