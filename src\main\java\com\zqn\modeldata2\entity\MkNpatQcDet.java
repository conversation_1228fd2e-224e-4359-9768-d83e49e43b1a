package com.zqn.modeldata2.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class MkNpatQcDet {
    // 鞋图
    private byte[] model_pic;

    // 出貨日
    @JsonFormat(
            pattern = "YYYY-MM-dd"
    )
    private Date shp_date;

    // 型体编号
    private String model_no;

    // 样品单号
    private String ord_no;

    // 样品类型
    private String dev_type;

    // 楦头编号
    private String last_no;

    // 双数
    private Double tot_qty;

    // 鞋面
    private Double u_qty;

    // 中底
    private Double b_qty1;

    // 中底皮
    private Double b_qty2;

    // 粘包
    private Double b_qty3;

    // 大底
    private Double b_qty6;

    // 开单号码及数量
    private String sz_data;

    // 楦头状态
    private String las_c_flag;

    //单号
    private String mating_no;

    // 投入時間
    @JsonFormat(
            pattern = "MM-dd HH:mm:ss"
    )
    private Date bar_date;

    // 投入時數
    private String bar_hour;

    // 異常原因
    private String pb_desc;

    private String pat_qty;

    private String ypat_qty;

    private String las_qty;

    private String siz_type;

    // 生产线栏位
    private String pd_line;

    private String p1_qty;

    private String p2_qty;
}
