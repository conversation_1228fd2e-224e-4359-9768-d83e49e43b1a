package com.zqn.studentrecord.controller;

import com.zqn.modeldata2.common.R;
import com.zqn.studentrecord.entity.StudentRecord;
import com.zqn.studentrecord.service.StudentRecordService;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

@Controller
@RequestMapping("/student-record/export")
public class StudentRecordExportController {
    
    @Autowired
    private StudentRecordService studentRecordService;
    
    @Value("${uploadUrl}")
    private String TARGET_FOLDER;
    
    /**
     * 压缩图片文件
     */
    public static byte[] compressImage(String inputImagePath) {
        try {
            File file = new File(inputImagePath);
            if (!file.exists()) {
                return null;
            }
            
            BufferedImage bufferImg = ImageIO.read(file);
            if (bufferImg == null) {
                return null;
            }
            
            String formatName = inputImagePath.substring(inputImagePath.lastIndexOf(".") + 1).toLowerCase();
            if (formatName.equals("jfif")) {
                formatName = "jpeg";
            }
            
            Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName(formatName);
            if (!writers.hasNext()) {
                return null;
            }
            ImageWriter writer = writers.next();
            
            ByteArrayOutputStream byteArrayOut = new ByteArrayOutputStream();
            ImageOutputStream ios = ImageIO.createImageOutputStream(byteArrayOut);
            writer.setOutput(ios);
            
            ImageWriteParam param = writer.getDefaultWriteParam();
            if (param.canWriteCompressed()) {
                param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
                param.setCompressionQuality(0.8f);
            }
            
            writer.write(null, new IIOImage(bufferImg, null, null), param);
            ios.close();
            writer.dispose();
            
            return byteArrayOut.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 压缩字节数组图片
     */
    public static byte[] compressImageBytes(byte[] imageBytes) {
        try {
            if (imageBytes == null || imageBytes.length == 0) {
                return null;
            }
            
            BufferedImage bufferImg = ImageIO.read(new ByteArrayInputStream(imageBytes));
            if (bufferImg == null) {
                return imageBytes;
            }
            
            Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName("png");
            if (!writers.hasNext()) {
                return imageBytes;
            }
            ImageWriter writer = writers.next();
            
            ByteArrayOutputStream byteArrayOut = new ByteArrayOutputStream();
            ImageOutputStream ios = ImageIO.createImageOutputStream(byteArrayOut);
            writer.setOutput(ios);
            
            ImageWriteParam param = writer.getDefaultWriteParam();
            if (param.canWriteCompressed()) {
                param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
                param.setCompressionQuality(0.8f);
            }
            
            writer.write(null, new IIOImage(bufferImg, null, null), param);
            ios.close();
            writer.dispose();
            
            return byteArrayOut.toByteArray();
        } catch (Exception e) {
            e.printStackTrace();
            return imageBytes;
        }
    }
    
    @GetMapping("/excel")
    public void exportStudentRecords(
            @RequestParam(required = false) String orderNo,
            @RequestParam(required = false) String student,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            HttpServletResponse response
    ) throws IOException {
        
        try {
            // 获取学员记录数据
            R<StudentRecordService.PageResult<StudentRecord>> result = 
                studentRecordService.getRecordList(1, 1000, orderNo, student, startDate, endDate);
            
            List<StudentRecord> recordList = new ArrayList<>();
            if (result.getCode() == 1 && result.getData() != null) {
                recordList = result.getData().getRecords();
            }
            
            // 创建工作簿
            XSSFWorkbook workbook = new XSSFWorkbook();
            XSSFSheet sheet = workbook.createSheet("制鞋学员一览表");
            
            // 创建样式
            CellStyle titleStyle = createTitleStyle(workbook);
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle dataStyle = createDataStyle(workbook);
            
            // 设置列宽
            setColumnWidths(sheet);
            
            // 创建标题行
            createTitleRow(sheet, titleStyle);
            
            // 创建表头行
            createHeaderRow(sheet, headerStyle);
            
            // 创建数据行
            createDataRows(sheet, dataStyle, recordList);
            
            // 插入图片
            insertImages(workbook, sheet, recordList);
            
            // 设置响应头
            String fileName = "制鞋学员一览表_" + new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date()) + ".xlsx";
            response.reset();
            response.setContentType("application/octet-stream;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            response.setHeader("Access-Control-Allow-Origin", "*");
            response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
            response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With");
            
            workbook.write(response.getOutputStream());
            workbook.close();
            
        } catch (Exception e) {
            e.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.getWriter().write("导出失败: " + e.getMessage());
        }
    }
    
    /**
     * 设置列宽
     */
    private void setColumnWidths(XSSFSheet sheet) {
        sheet.setColumnWidth(0, 8 * 256);   // 序号
        sheet.setColumnWidth(1, 12 * 256);  // 日期
        sheet.setColumnWidth(2, 15 * 256);  // 鞋图
        sheet.setColumnWidth(3, 15 * 256);  // 样品单号
        sheet.setColumnWidth(4, 12 * 256);  // 订单数量
        sheet.setColumnWidth(5, 12 * 256);  // 鞋型
        sheet.setColumnWidth(6, 12 * 256);  // 学员
        sheet.setColumnWidth(7, 20 * 256);  // 实物动作
        sheet.setColumnWidth(8, 20 * 256);  // 改善动作
        sheet.setColumnWidth(9, 20 * 256);  // 成品
        sheet.setColumnWidth(10, 25 * 256); // 教官/学员感想
        sheet.setColumnWidth(11, 20 * 256); // 沟通协作
        sheet.setColumnWidth(12, 12 * 256); // 动作学会
    }
    
    /**
     * 创建标题样式
     */
    private CellStyle createTitleStyle(XSSFWorkbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 16);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        return style;
    }
    
    /**
     * 创建表头样式
     */
    private CellStyle createHeaderStyle(XSSFWorkbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setFontHeightInPoints((short) 12);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setWrapText(true);
        return style;
    }
    
    /**
     * 创建数据样式
     */
    private CellStyle createDataStyle(XSSFWorkbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontHeightInPoints((short) 10);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.LEFT);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setWrapText(true);
        return style;
    }
    
    /**
     * 创建标题行
     */
    private void createTitleRow(XSSFSheet sheet, CellStyle titleStyle) {
        Row titleRow = sheet.createRow(0);
        titleRow.setHeightInPoints(30);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("制鞋学员一览表");
        titleCell.setCellStyle(titleStyle);
        
        // 合并单元格
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 12));
    }
    
    /**
     * 创建表头行
     */
    private void createHeaderRow(XSSFSheet sheet, CellStyle headerStyle) {
        Row headerRow = sheet.createRow(1);
        headerRow.setHeightInPoints(25);
        
        String[] headers = {
            "序号", "日期", "鞋图", "样品单号", "订单数量", "鞋型", "学员",
            "实物动作", "改善动作", "成品", "学员感想", "教官评语", "动作学会"
        };
        
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
    }
    
    /**
     * 创建数据行
     */
    private void createDataRows(XSSFSheet sheet, CellStyle dataStyle, List<StudentRecord> recordList) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        
        for (int i = 0; i < recordList.size(); i++) {
            StudentRecord record = recordList.get(i);
            Row dataRow = sheet.createRow(i + 2);
            
            // 根据图片数量动态设置行高
            int maxImages = Math.max(
                Math.max(
                    record.getActionPics() != null ? record.getActionPics().size() : 0,
                    record.getImprovePics() != null ? record.getImprovePics().size() : 0
                ),
                record.getProductPics() != null ? record.getProductPics().size() : 0
            );
            
            // 计算需要的行数（每行最多3张图片）
            int imageRows = maxImages > 0 ? (int) Math.ceil((double) maxImages / 3.0) : 1;
            // 设置行高，每行图片需要80像素，最少80像素
            float rowHeight = Math.max(80, imageRows * 80);
            dataRow.setHeightInPoints(rowHeight);
            
            // 序号
            Cell cell0 = dataRow.createCell(0);
            cell0.setCellValue(i + 1);
            cell0.setCellStyle(dataStyle);
            
            // 日期
            Cell cell1 = dataRow.createCell(1);
            cell1.setCellValue(record.getRecordDate() != null ? dateFormat.format(record.getRecordDate()) : "");
            cell1.setCellStyle(dataStyle);
            
            // 鞋图（第3列留空，图片会插入）
            Cell cell2 = dataRow.createCell(2);
            cell2.setCellValue("");
            cell2.setCellStyle(dataStyle);
            
            // 样品单号
            Cell cell3 = dataRow.createCell(3);
            cell3.setCellValue(record.getOrderNo() != null ? record.getOrderNo() : "");
            cell3.setCellStyle(dataStyle);
            
            // 订单数量
            Cell cell4 = dataRow.createCell(4);
            cell4.setCellValue(record.getOrdQty() != null ? record.getOrdQty().toString() : "");
            cell4.setCellStyle(dataStyle);
            
            // 鞋型
            Cell cell5 = dataRow.createCell(5);
            cell5.setCellValue(record.getModelNo() != null ? record.getModelNo() : "");
            cell5.setCellStyle(dataStyle);
            
            // 学员
            Cell cell6 = dataRow.createCell(6);
            cell6.setCellValue(record.getStudent() != null ? record.getStudent() : "");
            cell6.setCellStyle(dataStyle);
            
            // 实物动作（第8列留空，图片会插入）
            Cell cell7 = dataRow.createCell(7);
            cell7.setCellValue("");
            cell7.setCellStyle(dataStyle);
            
            // 改善动作（第9列留空，图片会插入）
            Cell cell8 = dataRow.createCell(8);
            cell8.setCellValue("");
            cell8.setCellStyle(dataStyle);
            
            // 成品（第10列留空，图片会插入）
            Cell cell9 = dataRow.createCell(9);
            cell9.setCellValue("");
            cell9.setCellStyle(dataStyle);
            
            // 教官/学员感想
            Cell cell10 = dataRow.createCell(10);
            cell10.setCellValue(record.getStudentThoughts() != null ? record.getStudentThoughts() : "");
            cell10.setCellStyle(dataStyle);
            
            // 沟通协作
            Cell cell11 = dataRow.createCell(11);
            cell11.setCellValue(record.getCommunicationIssues() != null ? record.getCommunicationIssues() : "");
            cell11.setCellStyle(dataStyle);
            
            // 动作学会
            Cell cell12 = dataRow.createCell(12);
            cell12.setCellValue(record.getLearned() != null ? (record.getLearned() ? "是" : "否") : "否");
            cell12.setCellStyle(dataStyle);
        }
    }
    
    /**
     * 插入图片到Excel
     */
    private void insertImages(XSSFWorkbook workbook, XSSFSheet sheet, List<StudentRecord> recordList) {
        XSSFDrawing patriarch = sheet.createDrawingPatriarch();
        
        for (int i = 0; i < recordList.size(); i++) {
            StudentRecord record = recordList.get(i);
            int rowIndex = i + 2; // 从第3行开始（0-based index）
            
            // 插入鞋图（第3列，索引2）
            if (record.getShoePic() != null) {
                byte[] compressedShoeImg = compressImageBytes(record.getShoePic());
                if (compressedShoeImg != null) {
                    insertImageToCell(patriarch, workbook, compressedShoeImg, rowIndex, 2);
                }
            }
            
            // 插入实物动作图片（第8列，索引7）- 合并多张图片
            if (record.getActionPics() != null && !record.getActionPics().isEmpty()) {
                byte[] mergedActionImage = mergeImages(record.getActionPics());
                if (mergedActionImage != null) {
                    insertImageToCell(patriarch, workbook, mergedActionImage, rowIndex, 7);
                }
            }
            
            // 插入改善动作图片（第9列，索引8）- 合并多张图片
            if (record.getImprovePics() != null && !record.getImprovePics().isEmpty()) {
                byte[] mergedImproveImage = mergeImages(record.getImprovePics());
                if (mergedImproveImage != null) {
                    insertImageToCell(patriarch, workbook, mergedImproveImage, rowIndex, 8);
                }
            }
            
            // 插入成品图片（第10列，索引9）- 合并多张图片
            if (record.getProductPics() != null && !record.getProductPics().isEmpty()) {
                byte[] mergedProductImage = mergeImages(record.getProductPics());
                if (mergedProductImage != null) {
                    insertImageToCell(patriarch, workbook, mergedProductImage, rowIndex, 9);
                }
            }
        }
    }
    
    /**
     * 将多张图片合并成一张图片
     */
    private byte[] mergeImages(List<String> imagePaths) {
        try {
            if (imagePaths == null || imagePaths.isEmpty()) {
                return null;
            }
            
            // 过滤有效的图片路径
            List<BufferedImage> images = new ArrayList<>();
            for (String imagePath : imagePaths) {
                if (imagePath != null && !imagePath.isEmpty()) {
                    String fullPath = TARGET_FOLDER + imagePath;
                    File file = new File(fullPath);
                    if (file.exists()) {
                        BufferedImage img = ImageIO.read(file);
                        if (img != null) {
                            images.add(img);
                        }
                    }
                }
            }
            
            if (images.isEmpty()) {
                return null;
            }
            
            // 如果只有一张图片，直接压缩返回
            if (images.size() == 1) {
                String fullPath = TARGET_FOLDER + imagePaths.get(0);
                return compressImage(fullPath);
            }
            
            // 计算合并后图片的尺寸
            int imageWidth = 200; // 每张小图的宽度 - 从120增加到200
            int imageHeight = 200; // 每张小图的高度 - 从120增加到200
            int imagesPerRow = 3; // 每行图片数量
            int padding = 10; // 图片间距 - 从5增加到10
            
            int rows = (int) Math.ceil((double) images.size() / imagesPerRow);
            int totalWidth = imagesPerRow * imageWidth + (imagesPerRow - 1) * padding;
            int totalHeight = rows * imageHeight + (rows - 1) * padding;
            
            // 创建合并后的图片
            BufferedImage mergedImage = new BufferedImage(totalWidth, totalHeight, BufferedImage.TYPE_INT_RGB);
            java.awt.Graphics2D g2d = mergedImage.createGraphics();
            
            // 设置背景色为白色
            g2d.setColor(java.awt.Color.WHITE);
            g2d.fillRect(0, 0, totalWidth, totalHeight);
            
            // 设置图片质量
            g2d.setRenderingHint(java.awt.RenderingHints.KEY_INTERPOLATION, java.awt.RenderingHints.VALUE_INTERPOLATION_BICUBIC);
            g2d.setRenderingHint(java.awt.RenderingHints.KEY_RENDERING, java.awt.RenderingHints.VALUE_RENDER_QUALITY);
            g2d.setRenderingHint(java.awt.RenderingHints.KEY_ANTIALIASING, java.awt.RenderingHints.VALUE_ANTIALIAS_ON);
            g2d.setRenderingHint(java.awt.RenderingHints.KEY_ALPHA_INTERPOLATION, java.awt.RenderingHints.VALUE_ALPHA_INTERPOLATION_QUALITY);
            g2d.setRenderingHint(java.awt.RenderingHints.KEY_COLOR_RENDERING, java.awt.RenderingHints.VALUE_COLOR_RENDER_QUALITY);
            
            // 绘制每张图片
            for (int i = 0; i < images.size(); i++) {
                BufferedImage img = images.get(i);
                
                // 计算位置
                int row = i / imagesPerRow;
                int col = i % imagesPerRow;
                int x = col * (imageWidth + padding);
                int y = row * (imageHeight + padding);
                
                // 直接使用Graphics2D绘制，获得更好的质量
                g2d.drawImage(img, x, y, imageWidth, imageHeight, null);
            }
            
            g2d.dispose();
            
            // 将合并后的图片转换为字节数组
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(mergedImage, "PNG", baos);
            
            // 压缩图片
            return compressImageBytes(baos.toByteArray());
            
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 在指定单元格插入单张图片
     */
    private void insertImageToCell(XSSFDrawing patriarch, XSSFWorkbook workbook, byte[] imageBytes, 
                                  int row, int col) {
        try {
            XSSFClientAnchor anchor = new XSSFClientAnchor(
                50, 50, 950, 950, // dx1, dy1, dx2, dy2 (图片在单元格内的位置)
                (short) col, row, (short) (col + 1), row + 1);
            patriarch.createPicture(anchor, workbook.addPicture(imageBytes, XSSFWorkbook.PICTURE_TYPE_PNG));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
} 