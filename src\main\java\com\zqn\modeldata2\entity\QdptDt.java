package com.zqn.modeldata2.entity;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 前段配套查询明细数据
 * @date 2024/6/11 15:06
 */
@Data
public class QdptDt {

    /**
     * 样品单号
     */
    private String ord_no;

    /**
     * 序号
     */
    private String remark;

    /**
     * 条码日期
     */
    private Date bar_date;

    /**
     * 制程部位
     */
    private String semi_su;

    /**
     * 必扫标记 KEY_FLAG=‘Y’就代表样品单有勾选必须扫描
     */
    private String key_flag;

    /**
     * 制作组别
     */
    private String made_dept;

    /**
     * 制作人员
     */
    private String emp_name;

    /**
     * 条码双数
     */
    private String bar_qty;

    /**
     * 建立人
     */
    private String ins_user;

    /**
     * 建立日期
     */
    private Date ins_date;

}
