package com.zqn.modeldata2.service.impl;

import com.zqn.modeldata2.mapper.FirstMapper;
import com.zqn.modeldata2.service.FirstService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class FirstServiceImpl implements FirstService {
    @Autowired
    private FirstMapper firstMapper;

    @Override
    public List<Object> getBrands() {
        return firstMapper.getBrands();
    }

    @Override
    public List<Object> getBrandsPlus() {
        List<Object> list = firstMapper.getBrands();
        List<Object> result = new ArrayList<>();
        String[] arr = new String[]{
                "ALL",
                "A", "B", "C", "D", "E", "F", "G",
                "H", "I", "J", "K", "L", "M", "N",
                "O", "P", "Q",
                "R", "S", "T",
                "U", "V", "W", "X", "Y", "Z"
        };
        for (int i = 0; i < arr.length; i++) {
            Map<String, Object> map = new HashMap<>();
            map.put("letter", arr[i]);
            List<String> brands = new ArrayList<>();
            for (Object item : list) {
                if (((Map<String, String>) item).get("brand_no").startsWith(arr[i])) {
                    brands.add(((Map<String, String>) item).get("brand_no"));
                }
            }
            map.put("data", brands);
            if (i == 0) {
                List<String> temp = new ArrayList<>();
                temp.add("所有品牌");
                map.put("data", temp);
            }
            result.add(map);
        }
        return result;
    }

    @Override
    public Map<String, Object> getModel(String model_no) {
        return firstMapper.getModel(model_no);
    }

    @Override
    public List<Object> getModels(String brand_no, String model_no) {
        return firstMapper.getModels(brand_no, model_no);
    }

    @Override
    public List<Object> getModelsByShoeLast(String brand_no, String shoe_last) {
        return firstMapper.getModelsByShoeLast(brand_no, shoe_last);
    }

    @Override
    public Map<String, Object> getModelPicture(String model_no) {
        return firstMapper.getModelPicture(model_no);
    }

    @Override
    public List<String> getSizeTypeList(String brand_no) {
        return firstMapper.getSizeTypeList(brand_no);
    }

    @Override
    public List<String> getSizeOption(String siz_type) {
        return firstMapper.getSizeOption(siz_type);
    }

    @Override
    public Integer updateSizeType(String model_no, String siz_type, String upd_user) {
        Integer result = -1;
        try {
            result = firstMapper.updateSizeType(model_no, siz_type, upd_user);
        } catch (Exception e) {
            return result;
        }
        return result;
    }

    @Override
    public Integer updateBaseSize(String model_no, String bas_size, String upd_user) {
        Integer result = -1;
        try {
            result = firstMapper.updateBaseSize(model_no, bas_size, upd_user);
        } catch (Exception e) {
            return result;
        }
        return result;
    }
}
