package com.zqn.prodctrl.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 生控排程(待排程 ）
 * @date 2024/8/27 10:36
 */
@Data
public class SchedProd {


    //项次
    private String item_no;

    //上线日期
    @JsonFormat(pattern = "YY/MM/dd")
    private Date online_date;

    //样单号
    private String ord_no;

    //样品类型
    private String dev_type;

    //鞋图
    private byte[] model_pic;

    //面师/底师
    private String upper_ser;

    private String sole_ser;

    //业务
    private String dutyer;

    //型体
    private String model_no;

    //sku
    private String sku_no;

    //楦头编号
    private String last_no;

    //楦头数量
    private String app_qty;

    //派工日
    @JsonFormat(pattern = "YY/MM/dd")
    private Date wo_date;

    //出货日
    @JsonFormat(pattern = "YY/MM/dd")
    private Date shp_date;

    //样双数
    private String tot_qty;

    //型体确认标记
    private String dev_types;

    //模具新旧
    private String mod_old;

    //模具确认标记
    private String mold_cfm_flag;

    //库存标记
    private String kc_flag;

    //面料交期
    private String pur_u_flag;

    //底料交期
    private String pur_s_flag;

    //品牌
    private String brand_no;

    //品牌描述
    private String brand_desc;

    //縂雙數
    private Integer tot_qty_sum;

    private String c_flag;
    private String b_flag;
    private String u_flag;
    private String p_flag;

    private String pd_line;

    private String phase;


}
