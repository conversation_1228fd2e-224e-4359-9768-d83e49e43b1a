package com.zqn.sop.service;

import com.github.pagehelper.PageInfo;
import com.zqn.sop.entity.PccMeProjectPlanHd;
import com.zqn.sop.vo.PccMeProjectPlanCopyVo;
import com.zqn.sop.vo.PccMeProjectPlanHdVo;

import java.util.List;

public interface PccMeProjectPlanHdService {

    PageInfo<PccMeProjectPlanHd> query(int pageNo, int pageSize, String model, String dept, String brand, String loginUser,String userFactory,String selectFactory);

    int create(PccMeProjectPlanHdVo vo) throws Exception;

    int update(PccMeProjectPlanHdVo vo) throws Exception;

    List<PccMeProjectPlanHd> queryModel(String model);

    List<String> queryShoeLastByModelNo(String model, String brand);

    Integer selectMax(String model, String dept,String factory);

    Integer delete(Integer parentId) throws Exception;

    int copy(PccMeProjectPlanCopyVo vo) throws Exception;

    List<String> findAllPlanFactory();

    Integer audit(PccMeProjectPlanHdVo vo);

    Integer revAudit(PccMeProjectPlanHdVo vo);
}