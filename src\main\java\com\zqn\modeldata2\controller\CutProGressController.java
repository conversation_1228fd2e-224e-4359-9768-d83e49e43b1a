package com.zqn.modeldata2.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.entity.CutProGress;
import com.zqn.modeldata2.entity.Qdpt;
import com.zqn.modeldata2.service.CutProGressService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:裁断生产进度表
 * @date 2024/6/4 8:45
 */
@RestController
@RequestMapping("/cutProGress")
@Validated
public class CutProGressController {

    @Resource
    private CutProGressService custProGressService;

    @GetMapping("/query")
    public R<PageInfo<CutProGress>> query(@RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                          @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
                                          @RequestParam(value = "startTime", required = false) long startTimeTamp,
                                          @RequestParam(value = "endTime", required = false) long endTimeTamp,
                                          @RequestParam(value = "cinStartTime", required = false) Long cinStartTimeTamp,
                                          @RequestParam(value = "cinEndTime", required = false) Long cinEndTimeTamp,
                                          @RequestParam(value = "brand", required = false) String brand,
                                          @RequestParam(value = "devType", required = false) String devType,
                                          @RequestParam(value = "externalStatus", required = false) Integer externalStatus
                                          ) {
        Date startTime = new Date(startTimeTamp); // 将时间戳转换为Date对象
        Date endTime = new Date(endTimeTamp); // 将时间戳转换为Date对象
        //当天0点
        Date cinStartTime = null;
        if(cinStartTimeTamp != null) {
            cinStartTime = new Date(cinStartTimeTamp);
            cinStartTime.setHours(0);
            cinStartTime.setMinutes(0);
            cinStartTime.setSeconds(0);
        }
        //当天23点59分59秒
        Date cinEndTime = null;
        if(cinEndTimeTamp != null) {
            cinEndTime = new Date(cinEndTimeTamp);
            cinEndTime.setHours(23);
            cinEndTime.setMinutes(59);
            cinEndTime.setSeconds(59);
        }
        return custProGressService.query(pageNo, pageSize, startTime, endTime, cinStartTime, cinEndTime, brand, devType, externalStatus);
    }


    @GetMapping("/queryAllDevType")
    public R<List<JSONObject>> queryAllDevType() {
        List<JSONObject> result = custProGressService.queryAllDevType();
        return R.success(result);
    }

    /**
     * @description: 取消投入
     * @param: qdpt
     * @return: com.zqn.modeldata2.common.R<java.lang.String>
     * <AUTHOR> Yang
     * @date: 2024/9/20 9:38
     */
    @PostMapping("/update")
    public R<String> update(@RequestBody CutProGress cutProGress) throws Exception {
        Integer result = custProGressService.update(cutProGress);
        if (result == 0) {
            return R.error("取消投入异常！");
        }
        return R.success("取消投入成功");
    }

}
