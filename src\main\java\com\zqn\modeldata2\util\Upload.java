package com.zqn.modeldata2.util;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.sql.Blob;
import java.util.Base64;

public class Upload {
    public static String blobToBase64String(Blob blob) throws IOException {
        String base64 = "";
        InputStream inputStream = null;
        ByteArrayOutputStream outputStream = null;
        if (blob != null) {
            try {
                inputStream = blob.getBinaryStream();
                outputStream = new ByteArrayOutputStream();
                byte[] buffer = new byte[1024];
                int offset = 0;
                while ((offset = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, offset);
                }
                base64 = Base64.getEncoder().encodeToString(outputStream.toByteArray());
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                if (outputStream != null) {
                    outputStream.close();
                }
            }
            return "data:image/jepg;base64," + base64;
        } else {
            return null;
        }
    }
}
