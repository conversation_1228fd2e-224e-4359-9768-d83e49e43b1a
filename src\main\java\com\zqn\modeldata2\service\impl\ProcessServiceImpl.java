package com.zqn.modeldata2.service.impl;

import com.github.houbb.opencc4j.util.ZhConverterUtil;
import com.zqn.modeldata2.entity.CopyProcess;
import com.zqn.modeldata2.entity.ProcessInfo;
import com.zqn.modeldata2.entity.ProcessStep;
import com.zqn.modeldata2.entity.ProcessTemplate;
import com.zqn.modeldata2.mapper.ProcessMapper;
import com.zqn.modeldata2.service.ProcessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class ProcessServiceImpl implements ProcessService {
    @Autowired
    private ProcessMapper processMapper;

    @Override
    public List<Integer> getOperation() {
        return processMapper.getOperation();
    }

    @Override
    public List<Object> getBrand() {
        List<String> list = processMapper.getBrand();
        List<Object> result = new ArrayList<>();
        String[] arr = new String[]{
                "A", "B", "C", "D", "E", "F", "G",
                "H", "I", "J", "K", "L", "M", "N",
                "O", "P", "Q",
                "R", "S", "T",
                "U", "V", "W", "X", "Y", "Z"
        };
        for (String initial : arr) {
            Map<String, Object> map = new HashMap<>();
            map.put("initial", initial);
            List<String> brandList = new ArrayList<>();
            for (String brand : list) {
                if (brand.startsWith(initial)) {
                    brandList.add(brand);
                }
            }
            map.put("data", brandList);
            result.add(map);
        }
        return result;
    }

    @Override
    public List<String> getModel(String brand_no) {
        return processMapper.getModel(brand_no);
    }

    @Override
    public Map<String, Object> getLast(String model_no) {
        return processMapper.getLast(model_no);
    }

    @Override
    public String getCode(String model_no, String operation) {
        return processMapper.getCode(model_no, operation);
    }

    @Override
    public Map<String, Object> getPicture(String model_no) {
        return processMapper.getPicture(model_no);
    }

    @Override
    public List<String> getType(String operation) {
        return processMapper.getType(operation);
    }

    @Override
    public List<String> getMaterial() {
        return processMapper.getMaterial();
    }

    @Override
    public List<ProcessInfo> getProcessInfo(ProcessInfo processInfo) {
        return processMapper.getProcessInfo(processInfo);
    }

    @Override
    public Integer addProcessInfo(ProcessInfo processInfo) {
        Integer result = -1;
        try {
            if (processInfo.getMaterial() != null && processInfo.getMaterial().length() > 0) {
                processInfo.setMaterial(ZhConverterUtil.toTraditional(processInfo.getMaterial()));
            }
            result = processMapper.addProcessInfo(processInfo);
        } catch (Exception e) {
            return result;
        }
        return result;
    }

    @Override
    public Integer updateProcessInfo(ProcessInfo processInfo) {
        Integer result = -1;
        try {
            if (processInfo.getMaterial() != null && processInfo.getMaterial().length() > 0) {
                processInfo.setMaterial(ZhConverterUtil.toTraditional(processInfo.getMaterial()));
            }
            result = processMapper.updateProcessInfo(processInfo);
        } catch (Exception e) {
            return result;
        }
        return result;
    }

    @Override
    public Integer deleteProcessInfo(ProcessInfo processInfo) {
        Integer result = -1;
        try {
            result = processMapper.deleteProcessInfo(processInfo);
        } catch (Exception e) {
            return result;
        }
        return result;
    }

    @Override
    @Transactional
    public Integer batchDeleteProcessInfo(List<ProcessInfo> processInfoList) {
        Integer result = -1;
        try {
            result = processMapper.batchDeleteProcessInfo(processInfoList);
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return result;
        }
        return result;
    }

    @Override
    public String getNo(ProcessStep processStep) {
        return processMapper.getNo(processStep);
    }

    @Override
    public String getKey(ProcessStep processStep) {
        return processMapper.getKey(processStep);
    }

    @Override
    public List<ProcessTemplate> getTemplate(String operation) {
        return processMapper.getTemplate(operation);
    }

    @Override
    @Transactional
    public Integer templateImport(ProcessTemplate processTemplate) {
        Integer result = -1;
        try {
            List<ProcessStep> processStepList = processMapper.getTemplateStep(processTemplate.getDoc_no());
            for (ProcessStep processStep : processStepList) {
                processStep.setModel_no(processTemplate.getModel_no());
                processStep.setOperation(processTemplate.getOperation());
                processStep.setRtg_code(processTemplate.getRtg_code());
                processStep.setIns_user(processTemplate.getIns_user());
                processStep.setUpd_user(processTemplate.getUpd_user());
            }
            result = processMapper.templateImport(processStepList);
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return result;
        }
        return result;
    }

    @Override
    public List<ProcessStep> getProcessStep(ProcessStep processStep) {
        return processMapper.getProcessStep(processStep);
    }

    @Override
    public Integer addProcessStep(ProcessStep processStep) {
        Integer result = -1;
        try {
            if (processStep.getSeq_name() != null && processStep.getSeq_name().length() > 0) {
                processStep.setSeq_name(ZhConverterUtil.toTraditional(processStep.getSeq_name()));
            }
            if (processStep.getRemark() != null && processStep.getRemark().length() > 0) {
                processStep.setRemark(ZhConverterUtil.toTraditional(processStep.getRemark()));
            }
            result = processMapper.addProcessStep(processStep);
        } catch (Exception e) {
            return result;
        }
        return result;
    }

    @Override
    public Integer updateProcessStep(ProcessStep processStep) {
        Integer result = -1;
        try {
            if (processStep.getSeq_name() != null && processStep.getSeq_name().length() > 0) {
                processStep.setSeq_name(ZhConverterUtil.toTraditional(processStep.getSeq_name()));
            }
            if (processStep.getRemark() != null && processStep.getRemark().length() > 0) {
                processStep.setRemark(ZhConverterUtil.toTraditional(processStep.getRemark()));
            }
            result = processMapper.updateProcessStep(processStep);
        } catch (Exception e) {
            return result;
        }
        return result;
    }

    @Override
    public Integer deleteProcessStep(ProcessStep processStep) {
        Integer result = -1;
        try {
            result = processMapper.deleteProcessStep(processStep);
        } catch (Exception e) {
            return result;
        }
        return result;
    }

    @Override
    @Transactional
    public Integer batchDeleteProcessStep(List<ProcessStep> processStepList) {
        Integer result = -1;
        try {
            result = processMapper.batchDeleteProcessStep(processStepList);
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return result;
        }
        return result;
    }

    @Override
    @Transactional
    public Integer editProcessStep(List<ProcessStep> processStepList) {
        Integer result = -1;
        try {
            for (ProcessStep processStep : processStepList) {
                if (processStep.getSeq_name() != null && processStep.getSeq_name().length() > 0) {
                    processStep.setSeq_name(ZhConverterUtil.toTraditional(processStep.getSeq_name()));
                }
                if (processStep.getRemark() != null && processStep.getRemark().length() > 0) {
                    processStep.setRemark(ZhConverterUtil.toTraditional(processStep.getRemark()));
                }
                processMapper.updateProcessStep(processStep);
            }
            result = 1;
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return result;
        }
        return result;
    }

    @Override
    public List<String> getModelList() {
        return processMapper.getModelList();
    }

    @Override
    @Transactional
    public Integer copyProcess(String originalModel, String targetModel, List<String> operationList, String user) {
        Integer result = -1;
        try {
            for (String operation : operationList) {
                CopyProcess copyProcess = new CopyProcess();
                copyProcess.setOriginalModel(originalModel);
                copyProcess.setTargetModel(targetModel);
                copyProcess.setOperation(operation);
                copyProcess.setInsUser(user);
                copyProcess.setUpdUser(user);
                processMapper.copyProcessInfo(copyProcess);
                processMapper.copyProcessStep(copyProcess);
            }
            result = 1;
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return result;
        }
        return result;
    }
}