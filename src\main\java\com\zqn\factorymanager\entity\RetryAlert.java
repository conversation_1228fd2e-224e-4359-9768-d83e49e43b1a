package com.zqn.factorymanager.entity;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/11/15 10:32
 */
@Data
public class RetryAlert {
    private String brand_no;

    private String model_no;

    private String ord_no;

    private byte[] model_pic;

    private String dev_type;

    private String upp_desc;

    private String upper_der;

    private String sole_der;

    private String dutyer;

    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date shp_date;
}
