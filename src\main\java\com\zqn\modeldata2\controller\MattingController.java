package com.zqn.modeldata2.controller;

import cn.afterturn.easypoi.cache.ExcelCache;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageInfo;
import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.entity.*;
import com.zqn.modeldata2.service.MattingService;
import com.zqn.sop.util.ExcelExportUtil;
import org.apache.poi.ss.usermodel.Drawing;
import org.apache.poi.ss.usermodel.Picture;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;


@RestController
@RequestMapping("/api/matting")
public class MattingController {

    @Autowired
    private MattingService mattingService;

    @GetMapping("/list")
    public R list(@RequestParam("date") String date) {
        List<Matting> result = mattingService.callMatting(date);
        return R.success(result);

    }

    @GetMapping("/bottomOrderList")
    public R bottomOrderList( @RequestParam(value = "beginTime", required = false) String beginTime,
                              @RequestParam(value = "endTime", required = false) String endTime) {
        List<BottomOrder> result = mattingService.bottomList(beginTime, endTime );
        return R.success(result);

    }

    @GetMapping("/bottomOrderItem")
    public R bottomOrderItem( @RequestParam(value = "modelNo") String modelNo,
                              @RequestParam(value = "mline") String mline) {
        List<BottomOrderItem> result = mattingService.getBottomItem(modelNo, mline );
        return R.success(result);

    }


    @GetMapping("/query")
    public R query(@RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                   @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
                   @RequestParam(value = "factory", required = false) String factory,
                   @RequestParam(value = "brandNo", required = false) String brandNo,
                   @RequestParam(value = "startTime", required = false) String startTime,
                   @RequestParam(value = "endTime", required = false) String endTime,
                   @RequestParam(value = "cfmFlag", required = false) String cfmFlag,
                   @RequestParam(value = "clFlag", required = false) String clFlag) {

        List<Fit> result = mattingService.query(factory, brandNo, startTime, endTime, cfmFlag, clFlag);
        return R.success(result);
    }



    @GetMapping("/queryDayuanList")
    public R dayuan(
                    @RequestParam(value = "brandNo", required = false) String brandNo,
                    @RequestParam(value = "startTime", required = false) String startTime,
                    @RequestParam(value = "endTime", required = false) String endTime) {
        List<DaYuan> result = mattingService.queryDayuanList(brandNo, startTime, endTime);
        return R.success(result);
    }

    @GetMapping("/queryDayuanBrands")
    public R dayuanBrands() {
        List<Brand> result = mattingService.queryDayuanBrands();
        return R.success(result);
    }



    @GetMapping("/export")
    public void export(HttpServletResponse response,
                       @RequestParam(value = "factory", required = false) String factory,
                       @RequestParam(value = "brandNo", required = false) String brandNo,
                       @RequestParam(value = "startTime", required = false) String startTime,
                       @RequestParam(value = "endTime", required = false) String endTime,
                       @RequestParam(value = "cfmFlag", required = false) String cfmFlag,
                       @RequestParam(value = "clFlag", required = false) String clFlag) throws IOException {

        List<Fit> fits = mattingService.queryList(factory, brandNo, startTime, endTime, cfmFlag, clFlag);
        // 模板路径
        String filePath = "static/excel/fit.xlsx";
        Workbook tempWorkbook = ExcelCache.getWorkbook(filePath, new Integer[]{0}, false);

        Map<Integer, List<Map<String, Object>>> resultMap = new TreeMap<>();
        List<Map<String, Object>> mapList1 = new ArrayList<>();
        Map<String, Object> map = new TreeMap<>();
        map.put("title", "FIT一览表");
        List<Map<String, Object>> list = new ArrayList<>();
        for (Fit fit1 : fits) {
            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("rownum", fit1.getRownum());
            hashMap.put("tran_date", fit1.getTran_date());
            hashMap.put("get_date", fit1.getGet_date());
            hashMap.put("factory", fit1.getFactory());
            hashMap.put("ord_qty",  StrUtil.isEmpty(fit1.getOrd_qty()) ? "\n" +  fit1.getTot_qty() : fit1.getOrd_qty()  + "\n" + fit1.getTot_qty());
            hashMap.put("tot_qty", fit1.getTot_qty());
            hashMap.put("dutyer", fit1.getDutyer());
            hashMap.put("online_date", fit1.getOnline_date());
            hashMap.put("ord_no", fit1.getOrd_no());
            hashMap.put("model_no", fit1.getModel_no());
            hashMap.put("model_name", fit1.getModel_name());
            hashMap.put("last_no", fit1.getLast_no());
            hashMap.put("t1_flag", fit1.getT1_flag());
            hashMap.put("t2_flag", fit1.getT2_flag());
            hashMap.put("t2_date", fit1.getT2_date());
            hashMap.put("t3_flag", fit1.getT3_flag());
            hashMap.put("y_flag", fit1.getY_flag());
            hashMap.put("u_date", fit1.getU_date());
            hashMap.put("b_date", fit1.getB_date());
            hashMap.put("shp_date", fit1.getShp_date());
            hashMap.put("dev_type", fit1.getDev_type());
            hashMap.put("upp_desc", fit1.getUpp_desc());
            map.put("empty", "");
            list.add(hashMap);
        }
        map.put("list", list);
        mapList1.add(map);
        resultMap.put(0, mapList1);

        TemplateExportParams params = new TemplateExportParams(filePath, true);
        params.setTemplateWb(tempWorkbook);
        XSSFWorkbook workbook = (XSSFWorkbook) ExcelExportUtil.exportExcelClone(resultMap, params);

        Sheet sheet = workbook.getSheetAt(0); // 获取第一个工作表
        Drawing drawing = sheet.createDrawingPatriarch();

        int pictureIndex = 0; // 图片索引
        int pictureColumnIndex = 8; // 假设图片要插入在第11列
        int pictureStartRow = 2; // 图片开始的行号（标题行通常在第一行）

        for (Fit fit : fits) {
            byte[] imageBytes = fit.getModel_pic();
            if (imageBytes != null) {
                // 添加图片到工作簿
                pictureIndex = workbook.addPicture(imageBytes, Workbook.PICTURE_TYPE_JPEG);

                // 创建锚点，定义图片的位置
                XSSFClientAnchor anchor = new XSSFClientAnchor();
                anchor.setCol1(pictureColumnIndex); // 图片开始的列
                anchor.setRow1(pictureStartRow); // 图片开始的行
                anchor.setCol2(pictureColumnIndex + 1); // 图片结束的列
                anchor.setRow2(pictureStartRow + 1); // 图片结束的行
                anchor.setDx1(50000); // 设置图片与单元格左侧的距离
                anchor.setDy1(50000); // 设置图片与单元格上侧的距离
                // 创建图片
                Picture picture = drawing.createPicture(anchor, pictureIndex);
                // 或者自定义缩放
                double scale = 0.85; // 缩放比例
                picture.resize(scale);

                // 更新图片开始的行号
                pictureStartRow++;
            }else{
                pictureStartRow++;
            }
        }


        String fileName = "FIT一览表.xlsx";
        response.reset();
        response.setContentType("application/octet-stream;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
        response.setHeader("Access-Control-Allow-Origin", "*"); // 或者指定具体的域名
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With");
        try {
            workbook.write(response.getOutputStream());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                workbook.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }




    @GetMapping("/exportNew")
    public void exportNew(HttpServletResponse response,
                       @RequestParam(value = "factory", required = false) String factory,
                       @RequestParam(value = "brandNo", required = false) String brandNo,
                       @RequestParam(value = "startTime", required = false) String startTime,
                       @RequestParam(value = "endTime", required = false) String endTime,
                       @RequestParam(value = "cfmFlag", required = false) String cfmFlag,
                       @RequestParam(value = "clFlag", required = false) String clFlag) throws IOException {

        List<Fit> fits = mattingService.queryList(factory, brandNo, startTime, endTime, cfmFlag, clFlag);
        // 模板路径
        String filePath = "static/excel/fitNew.xlsx";
        Workbook tempWorkbook = ExcelCache.getWorkbook(filePath, new Integer[]{0}, false);

        Map<Integer, List<Map<String, Object>>> resultMap = new TreeMap<>();
        List<Map<String, Object>> mapList1 = new ArrayList<>();
        Map<String, Object> map = new TreeMap<>();
        map.put("title", "FIT一览表(面部)");
        List<Map<String, Object>> list = new ArrayList<>();
        for (Fit fit1 : fits) {
            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("rownum", fit1.getRownum());
            hashMap.put("tran_date", fit1.getTran_date());
            hashMap.put("get_date", fit1.getGet_date());
            hashMap.put("factory", fit1.getFactory());
            hashMap.put("ord_qty",  StrUtil.isEmpty(fit1.getOrd_qty()) ? "\n" +  fit1.getTot_qty() : fit1.getOrd_qty()  + "\n" + fit1.getTot_qty());
            hashMap.put("tot_qty", fit1.getTot_qty());
            hashMap.put("dutyer", fit1.getDutyer());
            hashMap.put("online_date", fit1.getOnline_date());
            hashMap.put("ord_no", fit1.getOrd_no());
            hashMap.put("model_no", fit1.getModel_no());
            hashMap.put("model_name", fit1.getModel_name());
            hashMap.put("last_no", fit1.getLast_no());
            hashMap.put("t1_flag", fit1.getT1_flag());
            hashMap.put("t2_flag", fit1.getT2_flag());
            hashMap.put("t2_date", fit1.getT2_date());
            hashMap.put("t3_flag", fit1.getT3_flag());
            hashMap.put("y_flag", fit1.getY_flag());
            hashMap.put("u_date", fit1.getU_date());
            hashMap.put("b_date", fit1.getB_date());
            hashMap.put("shp_date", fit1.getShp_date());
            hashMap.put("dev_type", fit1.getDev_type());
            hashMap.put("upp_desc", fit1.getUpp_desc());

            hashMap.put("u1_qty", fit1.getU1_qty());
            hashMap.put("t5_qty", fit1.getT5_qty());
            hashMap.put("b2_qty", fit1.getB2_qty());
            hashMap.put("b1_qty", fit1.getB1_qty());
            hashMap.put("b4_qty", fit1.getB4_qty());
            hashMap.put("b3_qty", fit1.getB3_qty());
            hashMap.put("p1_qty", fit1.getP1_qty());

            map.put("empty", "");
            list.add(hashMap);
        }
        map.put("list", list);
        mapList1.add(map);
        resultMap.put(0, mapList1);

        TemplateExportParams params = new TemplateExportParams(filePath, true);
        params.setTemplateWb(tempWorkbook);
        XSSFWorkbook workbook = (XSSFWorkbook) ExcelExportUtil.exportExcelClone(resultMap, params);

        Sheet sheet = workbook.getSheetAt(0); // 获取第一个工作表
        Drawing drawing = sheet.createDrawingPatriarch();

        int pictureIndex = 0; // 图片索引
        int pictureColumnIndex = 8; // 假设图片要插入在第11列
        int pictureStartRow = 2; // 图片开始的行号（标题行通常在第一行）

        for (Fit fit : fits) {
            byte[] imageBytes = fit.getModel_pic();
            if (imageBytes != null) {
                // 添加图片到工作簿
                pictureIndex = workbook.addPicture(imageBytes, Workbook.PICTURE_TYPE_JPEG);

                // 创建锚点，定义图片的位置
                XSSFClientAnchor anchor = new XSSFClientAnchor();
                anchor.setCol1(pictureColumnIndex); // 图片开始的列
                anchor.setRow1(pictureStartRow); // 图片开始的行
                anchor.setCol2(pictureColumnIndex + 1); // 图片结束的列
                anchor.setRow2(pictureStartRow + 1); // 图片结束的行
                anchor.setDx1(50000); // 设置图片与单元格左侧的距离
                anchor.setDy1(50000); // 设置图片与单元格上侧的距离
                // 创建图片
                Picture picture = drawing.createPicture(anchor, pictureIndex);
                // 或者自定义缩放
                double scale = 0.85; // 缩放比例
                picture.resize(scale);

                // 更新图片开始的行号
                pictureStartRow++;
            }else{
                pictureStartRow++;
            }
        }


        String fileName = "FIT一览表.xlsx(面部)";
        response.reset();
        response.setContentType("application/octet-stream;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
        response.setHeader("Access-Control-Allow-Origin", "*"); // 或者指定具体的域名
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With");
        try {
            workbook.write(response.getOutputStream());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                workbook.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }




    @GetMapping("/bottomOrderExport")
    public void bottomOrderExport(HttpServletResponse response,
                                  @RequestParam(value = "beginTime", required = false) String beginTime,
                                  @RequestParam(value = "endTime", required = false) String endTime) throws IOException {

        List<BottomOrder> bottomOrderList = mattingService.bottomList(beginTime, endTime );
        // 模板路径
        String filePath = "static/excel/bottomOrder.xlsx";
        Workbook tempWorkbook = ExcelCache.getWorkbook(filePath, new Integer[]{0}, false);

        Map<Integer, List<Map<String, Object>>> resultMap = new TreeMap<>();
        List<Map<String, Object>> mapList1 = new ArrayList<>();
        Map<String, Object> map = new TreeMap<>();
        map.put("title", "皮底訂單生產狀況表");
        List<Map<String, Object>> list = new ArrayList<>();
        for (BottomOrder item : bottomOrderList) {
            HashMap<String, Object> hashMap = new HashMap<>();
            hashMap.put("get_date", item.getGET_DATE().split(" ")[0]);
            hashMap.put("brand_no", item.getBRAND_NO());
            hashMap.put("parentsmatno", item.getPARENTSMATNO());
            hashMap.put("sorder_no", item.getSORDER_NO());
            hashMap.put("out_cdate", StrUtil.isNotEmpty(item.getOUT_CDATE()) ? item.getOUT_CDATE().split(" ")[0] : "");
            hashMap.put("ord_qty", item.getORD_QTY());


            hashMap.put("model_name", item.getMODEL_NAME());
            hashMap.put("sole", item.getSOLE());
            hashMap.put("chn_color", item.getCHN_COLOR());
            hashMap.put("pl_status", item.getPL_STATUS());
            hashMap.put("fl_status", item.getFL_STATUS());

            hashMap.put("t_5d_flag", item.getT_5D_FLAG().equals("Y") ? (item.getT_5D_CPT_QTY() == 0 ? "OK" : item.getT_5D_CPT_QTY()) : "" );
            hashMap.put("t_5p_flag", item.getT_5P_FLAG().equals("Y") ? (item.getT_5P_CPT_QTY() == 0 ? "OK" : item.getT_5P_CPT_QTY()) : "" );
            hashMap.put("t_5t_flag", item.getT_5T_FLAG().equals("Y") ? (item.getT_5T_CPT_QTY() == 0 ? "OK" : item.getT_5T_CPT_QTY()) : "" );
            hashMap.put("t_5q_flag", item.getT_5Q_FLAG().equals("Y") ? (item.getT_5Q_CPT_QTY() == 0 ? "OK" : item.getT_5Q_CPT_QTY()) : "" );
            hashMap.put("t_5r_flag", item.getT_5R_FLAG().equals("Y") ? (item.getT_5R_CPT_QTY() == 0 ? "OK" : item.getT_5R_CPT_QTY()) : "" );
            hashMap.put("t_5u_flag", item.getT_5U_FLAG().equals("Y") ? (item.getT_5U_CPT_QTY() == 0 ? "OK" : item.getT_5U_CPT_QTY()) : "" );
            hashMap.put("t_5s_flag", item.getT_5S_FLAG().equals("Y") ? (item.getT_5S_CPT_QTY() == 0 ? "OK" : item.getT_5S_CPT_QTY()) : "" );




            map.put("empty", "");
            list.add(hashMap);
        }
        map.put("list", list);
        mapList1.add(map);
        resultMap.put(0, mapList1);

        TemplateExportParams params = new TemplateExportParams(filePath, true);
        params.setTemplateWb(tempWorkbook);
        XSSFWorkbook workbook = (XSSFWorkbook) ExcelExportUtil.exportExcelClone(resultMap, params);

        Sheet sheet = workbook.getSheetAt(0); // 获取第一个工作表
        Drawing drawing = sheet.createDrawingPatriarch();

        int pictureIndex = 0; // 图片索引
        int pictureColumnIndex = 5; // 假设图片要插入在第11列
        int pictureStartRow = 2; // 图片开始的行号（标题行通常在第一行）

        for (BottomOrder item : bottomOrderList) {
            byte[] imageBytes = item.getMODEL_PIC();
            if (imageBytes != null) {
                // 添加图片到工作簿
                pictureIndex = workbook.addPicture(imageBytes, Workbook.PICTURE_TYPE_JPEG);

                // 创建锚点，定义图片的位置
                XSSFClientAnchor anchor = new XSSFClientAnchor();
                anchor.setCol1(pictureColumnIndex); // 图片开始的列
                anchor.setRow1(pictureStartRow); // 图片开始的行
                anchor.setCol2(pictureColumnIndex + 1); // 图片结束的列
                anchor.setRow2(pictureStartRow + 1); // 图片结束的行
                anchor.setDx1(50000); // 设置图片与单元格左侧的距离
                anchor.setDy1(50000); // 设置图片与单元格上侧的距离
                // 创建图片
                Picture picture = drawing.createPicture(anchor, pictureIndex);
                // 或者自定义缩放
                double scale = 0.85; // 缩放比例
                picture.resize(scale);

                // 更新图片开始的行号
                pictureStartRow++;
            }else{
                pictureStartRow++;
            }
        }


        String fileName = "皮底訂單生產狀況表.xlsx";
        response.reset();
        response.setContentType("application/octet-stream;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
        response.setHeader("Access-Control-Allow-Origin", "*"); // 或者指定具体的域名
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With");
        try {
            workbook.write(response.getOutputStream());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                workbook.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


}