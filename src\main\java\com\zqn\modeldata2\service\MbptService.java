package com.zqn.modeldata2.service;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.entity.MbptDtDto;
import com.zqn.modeldata2.entity.MbptDto;
import com.zqn.modeldata2.entity.MkSorderbarDto;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/7/25 15:30
 */
public interface MbptService {
    R<PageInfo<MbptDto>> query(int pageNo, int pageSize, Date startTime, Date endTime, String brand, String devType, Integer fileType, Integer cutComplType, String searchOrdNo, String pdLine, String t2Flag, String bDate,String groNo) throws ParseException;

    List<JSONObject> queryAllDevType();

    List<MbptDtDto> queryDetailTableData(String userNo, String grpNo);

    Integer addMadeDept(MkSorderbarDto mbptDto);

    Integer editMadeDept(MkSorderbarDto mkSorderbarDto);

    List<MkSorderbarDto> queryMkSorderbarDtoByOrdNo(String ordNo);

    String fwtr(MkSorderbarDto mkSorderbarDto);

    String fwcc(MkSorderbarDto mkSorderbarDto);

    String cancel(MkSorderbarDto mkSorderbarDto);
}
