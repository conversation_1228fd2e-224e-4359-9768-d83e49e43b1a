package com.zqn.modeldata2.controller;

import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.service.FirstService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/first")
public class FirstController {
    @Autowired
    private FirstService firstService;

    @PostMapping("/getBrands")
    public R<List<Object>> getBrands() {
        List<Object> result = firstService.getBrands();
        return R.success(result);
    }

    @PostMapping("/getBrandsPlus")
    public R<List<Object>> getBrandsPlus() {
        List<Object> result = firstService.getBrandsPlus();
        return R.success(result);
    }

    @PostMapping("/getModel")
    public R<Map<String, Object>> getModel(@RequestBody Map<String, Object> params) {
        String model_no = params.get("model_no").toString();
        Map<String, Object> result = firstService.getModel(model_no);
        return R.success(result);
    }

    @PostMapping("/getList")
    public R<List<Object>> getList(@RequestBody Map<String, Object> params) {
        String brand_no = params.get("brand_no").toString();
        String model_no = params.get("model_no") != null ? params.get("model_no").toString() : "";
        List<Object> result = firstService.getModels(brand_no, model_no);
        return R.success(result);
    }

    @PostMapping("/getListByShoeLast")
    public R<List<Object>> getListByShoeLast(@RequestBody Map<String, Object> params) {
        String brand_no = params.get("brand_no").toString();
        String shoe_last = params.get("shoe_last") != null ? params.get("shoe_last").toString() : "";
        List<Object> result = firstService.getModelsByShoeLast(brand_no, shoe_last);
        return R.success(result);
    }

    @PostMapping("/getModelPicture")
    public R<Map<String, Object>> getModelPicture(@RequestBody Map<String, Object> params) {
        String model_no = params.get("model_no").toString();
        Map<String, Object> result = firstService.getModelPicture(model_no);
        return R.success(result);
    }

    @PostMapping("/getSizeTypeList")
    public R<List<String>> getSizeTypeList(@RequestBody Map<String, Object> param) {
        String brand_no = param.get("brand_no") != null ? param.get("brand_no").toString() : "";
        List<String> result = firstService.getSizeTypeList(brand_no);
        return R.success(result);
    }

    @PostMapping("/getSizeOption")
    public R<List<String>> getSizeOption(@RequestBody Map<String, Object> param) {
        String siz_type = param.get("siz_type") != null ? param.get("siz_type").toString() : "";
        List<String> result = firstService.getSizeOption(siz_type);
        return R.success(result);
    }

    @PostMapping("/updateSizeType")
    public R<String> updateSizeType(@RequestBody Map<String, Object> param) {
        String model_no = param.get("model_no") != null ? param.get("model_no").toString() : "";
        String siz_type = param.get("siz_type") != null ? param.get("siz_type").toString() : "";
        String upd_user = param.get("upd_user") != null ? param.get("upd_user").toString() : "";
        Integer result = firstService.updateSizeType(model_no, siz_type, upd_user);
        if (result < 0) {
            return R.error("码别修改失败！");
        }
        return R.success("码别修改成功！");
    }

    @PostMapping("/updateBaseSize")
    public R<String> updateBaseSize(@RequestBody Map<String, Object> param) {
        String model_no = param.get("model_no") != null ? param.get("model_no").toString() : "";
        String bas_size = param.get("bas_size") != null ? param.get("bas_size").toString() : "";
        String upd_user = param.get("upd_user") != null ? param.get("upd_user").toString() : "";
        Integer result = firstService.updateBaseSize(model_no, bas_size, upd_user);
        if (result < 0) {
            return R.error("基本码修改失败！");
        }
        return R.success("基本码修改成功！");
    }
}
