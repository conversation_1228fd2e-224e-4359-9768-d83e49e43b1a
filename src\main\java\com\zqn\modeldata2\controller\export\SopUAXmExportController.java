package com.zqn.modeldata2.controller.export;

import cn.afterturn.easypoi.cache.ExcelCache;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import com.zqn.modeldata2.entity.sop.SopFlowPicture;
import com.zqn.modeldata2.entity.sop.SopPreview;
import com.zqn.modeldata2.service.SopService;
import com.zqn.sop.util.ExcelExportUtil;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;

@Controller
@RequestMapping("/sop/ua/xmexport")
public class SopUAXmExportController {
    @Autowired
    private SopService sopService;

    @Value("${uploadUrl}")
    private String TARGET_FOLDER;

    public static byte[] compressImage(String inputImagePath) {
        try {
            // 读取图像
            BufferedImage bufferImg = ImageIO.read(new File(inputImagePath));

            // 获取图片格式
            String formatName = inputImagePath.substring(inputImagePath.lastIndexOf(".") + 1);
            if (formatName.equals("jfif")) {
                formatName = "jpeg";
            }

            // 获取图片写入器
            Iterator<ImageWriter> writers = ImageIO.getImageWritersByFormatName(formatName);
            if (!writers.hasNext()) throw new IllegalStateException("No writers found");
            ImageWriter writer = writers.next();

            // 设置输出流
            ByteArrayOutputStream byteArrayOut = new ByteArrayOutputStream();
            ImageOutputStream ios = ImageIO.createImageOutputStream(byteArrayOut);
            writer.setOutput(ios);

            // 设置压缩参数
            ImageWriteParam param = writer.getDefaultWriteParam();
            if (param.canWriteCompressed()) {
                param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
                param.setCompressionQuality(0.2f); // 这里设置压缩质量
            }

            // 写入图像
            writer.write(null, new IIOImage(bufferImg, null, null), param);

            // 关闭流
            ios.close();
            writer.dispose();

            return byteArrayOut.toByteArray();

        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    private void insertImagesInGrid(XSSFDrawing patriarch, XSSFWorkbook workbook, List<byte[]> imageBytesList, int startRow, int startCol, int endRow, int endCol, int rowHeight, int colWidth) {
        int numRows = 3; // 网格行数
        int numCols = 8; // 网格列数
        for (int i = 0; i < numRows; i++) {
            for (int j = 0; j < numCols; j++) {
                int index = i * numCols + j;
                if (index >= imageBytesList.size()) {
                    return; // 如果图片数量少于网格单元格数量，提前返回
                }
                byte[] imageBytes = imageBytesList.get(index);
                int row1 = startRow + i * rowHeight;
                int col1 = startCol + j * colWidth;
                int row2 = row1 + rowHeight;
                int col2 = col1 + colWidth;

                // 创建锚点
                XSSFClientAnchor anchor = new XSSFClientAnchor(0, 0, 0, 0, (short) col1, row1, (short) col2, row2);
                patriarch.createPicture(anchor, workbook.addPicture(imageBytes, XSSFWorkbook.PICTURE_TYPE_PNG));
            }
        }
    }

    // Copy print setup settings from source sheet to target sheet
    private void settingPrintSetup(Sheet targetSheet, short scale) {
        PrintSetup targetPrintSetup = targetSheet.getPrintSetup();
        targetPrintSetup.setPaperSize((short) 9);
        targetPrintSetup.setScale(scale);
        targetPrintSetup.setLandscape(true);
    }

    /**
     * 一对多，设置行高
     */
    private static void setRowHeight(Workbook workbook) {
        Sheet sheet = workbook.getSheetAt(0);
        //设置第4列的列宽为60（下标从0开始），TestExportSub2Vo 不知道为什么设置了列宽但是不起作用，只能在这里单独设置
//        sheet.setColumnWidth(3,60*256);
        for (int i = 0; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            int j = i / 9;
            if (i == 3 + (j * 9) || i == 5 + (j * 9)) {
                //设置其他行的行高根据内容自适应
                row.setHeightInPoints(70);
            } else if (i == 5 + (j * 9) || i == 7 + (j * 9)) {
                //设置其他行的行高根据内容自适应
                row.setHeightInPoints(70);
            } else {
                //设置第二行的行高（表格表头）
                row.setHeightInPoints(50);
            }
        }
    }

    private static void setRowHeight(Row row) {
        //根据内容长度设置行高
        int enterCnt = 0;
        for (int j = 0; j < row.getPhysicalNumberOfCells(); j++) {
            if (j < 7) {
                continue;
            }
            int rwsTemp = row.getCell(j).toString().length();
            //这里取每一行中的每一列字符长度最大的那一列的字符
            if (rwsTemp > enterCnt) {
                enterCnt = rwsTemp;
            }
        }
        //设置默认行高为50
        row.setHeightInPoints(50);
        //如果字符长度大于20，判断大了多少倍，根据倍数来设置相应的行高
        if (enterCnt > 15) {
            float d = (float) enterCnt / 15;
            if (d > 1.0f) {
                d = d * 1.0f;
            }
            float f = 50 * d;
            /*if (d>2 && d<4){
                f = 35*2;
            }else if(d>=4 && d<6){
                f = 35*3;
            }else if (d>=6 && d<8){
                f = 35*4;
            }*/
            row.setHeightInPoints(f);
        }
    }

    public static void copyRow(Row sourceRow, Row targetRow, XSSFWorkbook workbook) {
        // 设置行高
        targetRow.setHeight(sourceRow.getHeight());

        // 设置单元格内容和样式
        for (int i = 0; i < sourceRow.getLastCellNum(); i++) {
            Cell sourceCell = sourceRow.getCell(i);
            Cell targetCell = targetRow.createCell(i);

            if (sourceCell != null) {
                // 复制单元格样式
                targetCell.setCellStyle(sourceCell.getCellStyle());

                // 复制单元格值
                switch (sourceCell.getCellType()) {
                    case STRING:
                        targetCell.setCellValue(sourceCell.getStringCellValue());
                        break;
                    case NUMERIC:
                        targetCell.setCellValue(sourceCell.getNumericCellValue());
                        break;
                    case BOOLEAN:
                        targetCell.setCellValue(sourceCell.getBooleanCellValue());
                        break;
                    case FORMULA:
                        targetCell.setCellFormula(sourceCell.getCellFormula());
                        break;
                    case BLANK:
                        targetCell.setBlank();
                        break;
                    case ERROR:
                        targetCell.setCellErrorValue(sourceCell.getErrorCellValue());
                        break;
                    default:
                        break;
                }
            }
        }
    }

    private void copyMergedRegions(XSSFSheet sourceSheet, XSSFSheet targetSheet, int startRowIndex) {
        for (int i = 0; i < sourceSheet.getNumMergedRegions(); i++) {
            CellRangeAddress mergedRegion = sourceSheet.getMergedRegion(i);
            CellRangeAddress newMergedRegion = new CellRangeAddress(
                    mergedRegion.getFirstRow() + startRowIndex,
                    mergedRegion.getLastRow() + startRowIndex,
                    mergedRegion.getFirstColumn(),
                    mergedRegion.getLastColumn()
            );
            targetSheet.addMergedRegion(newMergedRegion);
        }
    }

    /**
     * 复制原始 sheet 中的所有图片到目标 sheet，并根据行号偏移锚点位置
     */
    public static void copyPictures(XSSFSheet sourceSheet, XSSFSheet targetSheet, int rowOffset) {
        // 获取源 sheet 的绘图容器
        XSSFDrawing sourceDrawing = (XSSFDrawing) sourceSheet.getDrawingPatriarch();
        if (sourceDrawing == null) return;

        // 创建或获取目标 sheet 的绘图容器
        XSSFDrawing targetDrawing = (XSSFDrawing) targetSheet.createDrawingPatriarch();

        // 遍历所有图形对象
        for (XSSFShape shape : sourceDrawing.getShapes()) {
            if (shape instanceof XSSFPicture) {
                XSSFPicture picture = (XSSFPicture) shape;
                XSSFClientAnchor sourceAnchor = (XSSFClientAnchor) picture.getAnchor();

                // 构建新的锚点并偏移行号
                XSSFClientAnchor newAnchor = new XSSFClientAnchor();
                newAnchor.setCol1(sourceAnchor.getCol1());
                newAnchor.setCol2(sourceAnchor.getCol2());
                newAnchor.setRow1(sourceAnchor.getRow1() + rowOffset);
                newAnchor.setRow2(sourceAnchor.getRow2() + rowOffset);

                // 获取图片数据和类型
                byte[] imageData = getPictureData(picture);
                int pictureType = picture.getPictureData().getPictureType();

                // 添加图片到目标工作簿
                XSSFWorkbook workbook = (XSSFWorkbook) targetSheet.getWorkbook();
                int pictureIdx = workbook.addPicture(imageData, pictureType);

                // 插入图片到目标 sheet
                targetDrawing.createPicture(newAnchor, pictureIdx);
            }
        }
    }

    /**
     * 提取图片字节流
     */
    private static byte[] getPictureData(XSSFPicture picture) {
        return picture.getPictureData().getData();
    }

    @GetMapping("/excel")
    public void exportOperationProcess(
            @RequestParam(value = "model") String model,
            @RequestParam(value = "operation") String operation,
            @RequestParam(value = "rtgCode") String rtgCode,
            HttpServletResponse response
    ) throws IOException {
        Map<String, String> operationMap = new HashMap<>();
        operationMap.put("1", "加工");
        operationMap.put("4", "鞋面");
        operationMap.put("5", "半成品");
        operationMap.put("6", "成型");

        // 模板路径
        String filePath = "static/excel/ua/xm.xlsx";
        Workbook tempWorkbook = ExcelCache.getWorkbook(filePath, new Integer[]{0, 1, 2, 3}, false);
        List<SopPreview> previewList = sopService.getPreviewList(model, operation, rtgCode);

        Map<Integer, List<Map<String, Object>>> resultMap = new TreeMap<>();
        List<Map<String, Object>> mapList1 = new ArrayList<>();
        List<Map<String, Object>> mapList2 = new ArrayList<>();
        List<Map<String, Object>> mapList3 = new ArrayList<>();
        List<Map<String, Object>> mapList4 = new ArrayList<>();

        int pageSize = 12;
        int deleteCount = (previewList.size() + pageSize - 1) / pageSize;
        for (int sheetIndex = previewList.size() - 1; sheetIndex >= 0; sheetIndex--) {
            SopPreview sopPreview = previewList.get(sheetIndex);

            Map<String, Object> map = new TreeMap<>();
            map.put("item", sopPreview.getSkey());
            map.put("model_no", model);
            map.put("actions", sopPreview.getActions());
            map.put("op_std", sopPreview.getStandard());
            map.put("tools", sopPreview.getTools());
            map.put("chemical_substance", sopPreview.getChemical());
            map.put("machine", sopPreview.getMachine());
            map.put("temp", sopPreview.getTemp());
            map.put("pressure", sopPreview.getPressure());
            map.put("time", sopPreview.getTime());
            map.put("self_check_points", sopPreview.getCheckPoint());
            map.put("glue", sopPreview.getGlue());
            map.put("car_line", sopPreview.getCarLine());
            map.put("margin", sopPreview.getMargin());
            map.put("spacing", sopPreview.getSpacing());
            map.put("needle_spacing", sopPreview.getNeedleSpacing());
            map.put("needle", sopPreview.getNeedle());
            map.put("createBy", sopPreview.getTab());
            map.put("empty", "");
            map.put("count", deleteCount);

            List<Map<String, Object>> list = new ArrayList<>();
            if (mapList3.size() < deleteCount) {
                for (int i = 0; i < previewList.size(); i++) {
                    if (i >= sheetIndex * pageSize && i <= (sheetIndex + 1) * pageSize - 1) {
                        SopPreview sopPreview1 = previewList.get(i);
                        HashMap<String, Object> hashMap = new HashMap<>();
                        hashMap.put("actions", sopPreview1.getActions());
                        hashMap.put("op_std", sopPreview1.getStandard());
                        hashMap.put("tools", sopPreview1.getTools());
                        hashMap.put("glue", sopPreview1.getGlue());
                        hashMap.put("machine", sopPreview1.getMachine());
                        hashMap.put("car_line", sopPreview1.getCarLine());
                        hashMap.put("margin", sopPreview1.getMargin());
                        hashMap.put("spacing", sopPreview1.getSpacing());
                        hashMap.put("needle_spacing", sopPreview1.getNeedleSpacing());
                        hashMap.put("needle", sopPreview1.getNeedle());
                        hashMap.put("item", sopPreview1.getSkey());
                        list.add(hashMap);
                    }
                }
            }
            map.put("list", list);
            //sheet1、sheet2只需要复制一个模板就行
            if (sheetIndex == 0) {
                mapList1.add(map);
                mapList2.add(map);
            }
            //sheet3需要根据分页来决定复制几次
            if (mapList3.size() < deleteCount && list.size() > 0) {
                mapList3.add(map);
            }

            if (sheetIndex % 4 == 0) {
                map.put("page", sheetIndex / 4 + 1);
                for (int i = sheetIndex; i < (sheetIndex + 4) && i < previewList.size(); i++) {
                    SopPreview sopPreview1 = previewList.get(i);
                    switch (i - sheetIndex) {
                        case 0:
                            map.put("item1", sopPreview1.getSkey());
                            map.put("actions1", sopPreview1.getActions());
                            map.put("machine1", sopPreview1.getMachine());
                            map.put("needle1", sopPreview1.getNeedle());
                            map.put("car_line1", sopPreview1.getCarLine());
                            map.put("margin1", sopPreview1.getMargin());
                            map.put("needle_spacing1", sopPreview1.getNeedleSpacing());
                            map.put("op_std1", sopPreview1.getStandard());
                            break;
                        case 1:
                            map.put("item2", sopPreview1.getSkey());
                            map.put("actions2", sopPreview1.getActions());
                            map.put("machine2", sopPreview1.getMachine());
                            map.put("needle2", sopPreview1.getNeedle());
                            map.put("car_line2", sopPreview1.getCarLine());
                            map.put("margin2", sopPreview1.getMargin());
                            map.put("needle_spacing2", sopPreview1.getNeedleSpacing());
                            map.put("op_std2", sopPreview1.getStandard());
                            break;
                        case 2:
                            map.put("item3", sopPreview1.getSkey());
                            map.put("actions3", sopPreview1.getActions());
                            map.put("machine3", sopPreview1.getMachine());
                            map.put("needle3", sopPreview1.getNeedle());
                            map.put("car_line3", sopPreview1.getCarLine());
                            map.put("margin3", sopPreview1.getMargin());
                            map.put("needle_spacing3", sopPreview1.getNeedleSpacing());
                            map.put("op_std3", sopPreview1.getStandard());
                            break;
                        case 3:
                            map.put("item4", sopPreview1.getSkey());
                            map.put("actions4", sopPreview1.getActions());
                            map.put("machine4", sopPreview1.getMachine());
                            map.put("needle4", sopPreview1.getNeedle());
                            map.put("car_line4", sopPreview1.getCarLine());
                            map.put("margin4", sopPreview1.getMargin());
                            map.put("needle_spacing4", sopPreview1.getNeedleSpacing());
                            map.put("op_std4", sopPreview1.getStandard());
                            break;
                        default:
                            break;
                    }
                }
                mapList4.add(map);
            }
        }

        // 将 mapList 加入到 resultMap 中
        resultMap.put(0, mapList1); // sheet2
        resultMap.put(1, mapList2); // sheet2
        resultMap.put(2, mapList3); // sheet2
        resultMap.put(3, mapList4); // sheet2

        TemplateExportParams params = new TemplateExportParams(filePath, true);
        params.setTemplateWb(tempWorkbook);
        XSSFWorkbook workbook = (XSSFWorkbook) ExcelExportUtil.exportExcelClone(resultMap, params);

        // 重新设置 sheet 名称，确保名称唯一
        int sheet3Index = 0;
        int sheet4Index = 0;
        int sheet3StartIndex = 2; // sheet3 的起始索引
        int sheet4StartIndex = sheet3StartIndex + deleteCount; // sheet4 的起始索引
        for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
            if (i == 0) {
                settingPrintSetup(workbook.getSheetAt(i), (short) 70);
                workbook.setSheetName(i, "裁斷（斬刀）全覽圖");
            } else if (i == 1) {
                settingPrintSetup(workbook.getSheetAt(i), (short) 34);
                workbook.setSheetName(i, "重點提示");
            } else if (i >= sheet3StartIndex && i < sheet4StartIndex) {
                sheet3Index++;
                settingPrintSetup(workbook.getSheetAt(i), (short) 56);
                workbook.setSheetName(i, "面部作業流程" + sheet3Index);
            } else if (i >= sheet4StartIndex) {
                sheet4Index++;
                settingPrintSetup(workbook.getSheetAt(i), (short) 60);
                workbook.setSheetName(i, "針車流程說明 (" + sheet4Index + ")");
            }
        }

        // 插入图片
        for (int imageIndex = 0; imageIndex < previewList.size(); imageIndex++) {
            SopPreview sopPreview = previewList.get(imageIndex);
            List<SopFlowPicture> assetsPicture = sopPreview.getImgList();
            if (!CollectionUtils.isEmpty(assetsPicture)) {
                List<byte[]> type1Images = new ArrayList<>();

                for (SopFlowPicture sopFlowPicture : assetsPicture) {
                    String picturePath = TARGET_FOLDER + sopFlowPicture.getImgUrl();
                    byte[] compressedImage = compressImage(picturePath);
                    type1Images.add(compressedImage);
                }

                // 插入图片到红框
                XSSFSheet sheet = (XSSFSheet) workbook.getSheet("針車流程說明 (" + (imageIndex / 4 + 1) + ")");
                XSSFDrawing patriarch = sheet.createDrawingPatriarch();
                switch (imageIndex % 4) {
                    case 0:
                        //根据图片数量判读单元格坐标
                        insertImagesInGrid(patriarch, workbook, type1Images, 12, 2, 0, 0, 3, 3);
                        break;
                    case 1:
                        insertImagesInGrid(patriarch, workbook, type1Images, 12, 32, 0, 0, 3, 3);
                        break;
                    case 2:
                        insertImagesInGrid(patriarch, workbook, type1Images, 34, 2, 0, 0, 3, 3);
                        break;
                    case 3:
                        insertImagesInGrid(patriarch, workbook, type1Images, 34, 32, 0, 0, 3, 3);
                        break;
                    default:
                        break;
                }
            }
        }

        // 先记录原始 sheet 数量
        int originalSheetCount = workbook.getNumberOfSheets();

        // 创建合并工作表
        XSSFSheet mergedSheet = workbook.createSheet("針車流程");

        int currentRowNum = 0;

        // 只复制原始 sheet 中 >= sheet4StartIndex 的内容
        for (int i = sheet4StartIndex; i < originalSheetCount; i++) {
            XSSFSheet sourceSheet = workbook.getSheetAt(i);

            // 设置列宽（仅第一次）
            if (currentRowNum == 0) {
                for (int colIdx = 0; colIdx < sourceSheet.getRow(0).getLastCellNum(); colIdx++) {
                    mergedSheet.setColumnWidth(colIdx, sourceSheet.getColumnWidth(colIdx));
                }
            }

            // 缓存行数据以避免并发修改
            List<Row> rows = new ArrayList<>();
            for (Row row : sourceSheet) {
                rows.add(row);
            }

            // 复制行
            for (Row sourceRow : rows) {
                Row newRow = mergedSheet.createRow(currentRowNum++);
                copyRow(sourceRow, newRow, workbook);
            }

            int startRow = currentRowNum - rows.size();

            // 复制合并区域
            copyMergedRegions(sourceSheet, mergedSheet, startRow);

            // 复制图片
            copyPictures(sourceSheet, mergedSheet, startRow);

            // 插入分页符（当前行结束后插入分页）
            mergedSheet.setRowBreak(currentRowNum - 1);
        }

        // 删除原始 sheet（从后往前删）
        for (int i = originalSheetCount - 1; i >= sheet4StartIndex; i--) {
            workbook.removeSheetAt(i);
        }

        String fileName = model + operationMap.get(operation) + rtgCode + ".xlsx";
        response.reset();
        response.setContentType("application/octet-stream;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
        response.setHeader("Access-Control-Allow-Origin", "*"); // 或者指定具体的域名
        response.setHeader("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
        response.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Requested-With");
        try {
            workbook.write(response.getOutputStream());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                workbook.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }
}