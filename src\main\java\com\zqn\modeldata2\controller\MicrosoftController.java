package com.zqn.modeldata2.controller;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.service.MicrosoftService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class MicrosoftController {

    @Autowired
    private MicrosoftService microsoftService;

    @PostMapping("/translate")
    public R<JSONArray> translateText(@RequestBody JSONObject json) throws JSONException {
        JSONArray translatedText = microsoftService.callMicrosoftAPITranslate(json);
        return R.success(translatedText);
    }
}
