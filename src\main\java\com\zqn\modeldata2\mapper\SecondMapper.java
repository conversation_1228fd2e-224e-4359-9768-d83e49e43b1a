package com.zqn.modeldata2.mapper;

import com.zqn.modeldata2.entity.CkSmodelp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SecondMapper {
    List<CkSmodelp> getSpecification(@Param("ckSmodelp") CkSmodelp ckSmodelp);

    int addSpecification(@Param("ckSmodelp") CkSmodelp ckSmodelp);

    int updateSpecification(@Param("ckSmodelp") CkSmodelp ckSmodelp);

    int deleteSpecification(@Param("ckSmodelp") CkSmodelp ckSmodelp);

    int batchDeleteSpecification(@Param("ckSmodelpList") List<CkSmodelp> ckSmodelpList);

    Integer updateStartSize(@Param("ckSmodelp") CkSmodelp ckSmodelp);

    Integer updateEndSize(@Param("ckSmodelp") CkSmodelp ckSmodelp);
}
