package com.zqn.sop.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.github.houbb.heaven.util.lang.BeanUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zqn.modeldata2.service.MenuService;
import com.zqn.sop.entity.PccMeProjectPlanHd;
import com.zqn.sop.mapper.PccMeProjectPlanDtMapper;
import com.zqn.sop.mapper.PccMeProjectPlanHdMapper;
import com.zqn.sop.mapper.PccSopContMapper;
import com.zqn.sop.service.PccMeProjectPlanDtService;
import com.zqn.sop.service.PccMeProjectPlanHdService;
import com.zqn.sop.service.PccSopContService;
import com.zqn.sop.vo.PccMeProjectPlanCopyVo;
import com.zqn.sop.vo.PccMeProjectPlanDtVo;
import com.zqn.sop.vo.PccMeProjectPlanHdVo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class PccMeProjectPlanHdServiceImpl implements PccMeProjectPlanHdService {

    @Resource
    private PccMeProjectPlanHdMapper hdMapper;

    @Resource
    private PccMeProjectPlanDtService pccMeProjectPlanDtService;

    @Resource
    private PccSopContService pccSopContService;

    @Resource
    private PccMeProjectPlanDtMapper dtMapper;

    @Resource
    private PccSopContMapper pccSopContMapper;

    @Resource
    private MenuService menuService;

    @Override
    @DS("app")
    public PageInfo<PccMeProjectPlanHd> query(int pageNo, int pageSize, String model, String dept, String brand, String loginUser,String userFactory,String selectFactory) {
        PageHelper.startPage(pageNo, pageSize);
        Boolean hasAudit = menuService.buttonQuery("/pccmeprjplanhd/audit", loginUser);
        List<PccMeProjectPlanHd> list = hdMapper.query(model, dept, brand, loginUser,userFactory,selectFactory, hasAudit);
        for (PccMeProjectPlanHd pccMeProjectPlanHd : list) {
            if (pccMeProjectPlanHd.getDept() != null) {
                pccMeProjectPlanHd.setDept_name(pccMeProjectPlanHd.getDept());
            }
            Integer maxItem = pccMeProjectPlanDtService.selectMax(pccMeProjectPlanHd.getModel_no(),pccMeProjectPlanHd.getDept(),pccMeProjectPlanHd.getFactory());
            pccMeProjectPlanHd.setItem_num(maxItem);
            Integer minItem = pccMeProjectPlanDtService.selectMin(pccMeProjectPlanHd.getModel_no(), pccMeProjectPlanHd.getDept(), pccMeProjectPlanHd.getFactory());
            pccMeProjectPlanHd.setMin_item(minItem);
        }
        PageInfo<PccMeProjectPlanHd> pageInfo = new PageInfo<>(list);
        return pageInfo;
    }



    @Override
    @DS("app")
    public List<PccMeProjectPlanHd> queryModel(String model) {
        return hdMapper.queryModel(model);
    }

    @Override
    public List<String> queryShoeLastByModelNo(String model, String brand) {
        return hdMapper.queryShoeLastByModelNo(model, brand);
    }

    @Override
    public Integer selectMax(String model, String dept,String factory) {
        return pccMeProjectPlanDtService.selectMax( model,  dept, factory);
    }

    @DS("app")
    @Transactional
    @Override
    public int create(PccMeProjectPlanHdVo vo) throws Exception {
        if (vo.getNew_item() != null) {
            if (vo.getPccMeProjectPlanDts().getItem_no() == vo.getNew_item()) {
                throw new Exception("新项次不能与旧项次相同");
            }
            vo.getPccMeProjectPlanDts().setItem_no(vo.getNew_item());
        }
        int count = 0;
        //保存主表
        count = add(vo);
        //保存明细表
        int dtCount = pccMeProjectPlanDtService.add(vo.getPccMeProjectPlanDts(), "add");
        //保存内容表
        int contCount = pccSopContService.add(vo);

        count = count + dtCount + contCount;
        return count;
    }

    /**
     * @description: 主表保存
     * @param: vo
     * @return: void
     * <AUTHOR> Yang
     * @date: 2024/5/8 15:01
     */
    private int add(PccMeProjectPlanHdVo vo) throws Exception {
        List<PccMeProjectPlanHd> exist = hdMapper.query(vo.getModel_no(), vo.getDept(), null, null,vo.getFactory(),null, true);
        if (!CollectionUtils.isEmpty(exist)) {
            for (PccMeProjectPlanHd record : exist) {
                if (record.getAudit_flag() == 1) {
                    throw new Exception("编辑失败，数据已审核！");
                }
                if (vo.getFactory().equals(record.getFactory())) {
                    //设置父ID
                    vo.getPccMeProjectPlanDts().setParent_id(record.getId());
                    return 0;
                }
            }
        }

        PccMeProjectPlanHd pccMeProjectPlanHd = new PccMeProjectPlanHd();
        BeanUtil.copyProperties(vo, pccMeProjectPlanHd);
        Date date = new Date();
        pccMeProjectPlanHd.setLink_date(date);
        pccMeProjectPlanHd.setCreate_date(date);
        pccMeProjectPlanHd.setUpdate_date(date);
        //存储工厂信息
        pccMeProjectPlanHd.setFactory(vo.getFactory());
        pccMeProjectPlanHd.setRemark("");
        int count = hdMapper.save(pccMeProjectPlanHd);
        if (count != 1) {
            throw new Exception("保存失败！");
        }
        vo.getPccMeProjectPlanDts().setParent_id(pccMeProjectPlanHd.getId());
        vo.getPccMeProjectPlanDts().setCreate_by(vo.getCreate_by());
        vo.getPccMeProjectPlanDts().setUpdate_by(vo.getUpdate_by());
        return count;
    }

    /**
     * @description: 编辑直接新增一个新的版本，原本的不做更改
     * @param: vo
     * @return: int
     * <AUTHOR> Yang
     * @date: 2024/5/24 8:35
     */
    @DS("app")
    @Transactional
    @Override
    public int update(PccMeProjectPlanHdVo vo) throws Exception {
        String deleteModel = null;
        String deleteDept = null;
        Integer deleteItem = null;
        if (vo.getNew_item() != null) {
            deleteModel = vo.getModel_no();
            deleteDept = vo.getDept();
            deleteItem = vo.getPccMeProjectPlanDts().getItem_no();
            vo.getPccMeProjectPlanDts().setItem_no(vo.getNew_item());
        }
        int count = 0;
        //保存主表
        count = edit(vo);
        //保存明细表
        int dtCount = pccMeProjectPlanDtService.add(vo.getPccMeProjectPlanDts(), "edit");
        //保存内容表
        int contCount = pccSopContService.add(vo);

        //修改后项次与当前项次不一样才需要删除
        if (vo.getNew_item() != null) {
            if (vo.getNew_item() != deleteItem) {
                // 验证输入参数是否为空
                if (StrUtil.isEmpty(deleteModel) || StrUtil.isEmpty(deleteDept) || deleteItem == null) {
                    throw new Exception("删除失败！");
                }
                // 调用DAO删除指定的项目计划条目，并返回删除的条目数量
                dtMapper.deleteItem(vo.getId(),deleteItem);
                dtMapper.deleteContent(vo.getId(),deleteItem);
                dtMapper.deleteImg(vo.getId(), deleteItem);
            }
        }
        count = count + dtCount + contCount;
        return count;
    }

    public int edit(PccMeProjectPlanHdVo vo) throws Exception {
        List<PccMeProjectPlanHd> exist = hdMapper.query(vo.getModel_no(), vo.getDept(), null, null,vo.getFactory(),null, true);
        int count = 0;
        if (!CollectionUtils.isEmpty(exist)) {
            //
            for (PccMeProjectPlanHd pccMeProjectPlanHd : exist) {
                if (pccMeProjectPlanHd.getAudit_flag() == 1) {
                    throw new Exception("编辑失败，数据已审核！");
                }
            }
        } else {
            throw new Exception("编辑失败！");
        }
        return count;
    }

    @Override
    @Transactional
    @DS("app")
    public Integer delete(Integer parentId) throws Exception {
        PccMeProjectPlanHdVo pccMeProjectPlanHdVo = hdMapper.queryById(parentId);
        if (pccMeProjectPlanHdVo != null && pccMeProjectPlanHdVo.getAudit_flag() == 1) {
            throw new Exception("删除失败，数据已审核！");
        }
        //删除子表
        //1.图片
        hdMapper.deleteImgs(parentId);
        hdMapper.deleteContent(parentId);
        //2.明细表
        hdMapper.deleteDt(parentId);
        hdMapper.deleteHd(parentId);
        return 1;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @DS("app")
    public int copy(PccMeProjectPlanCopyVo vo) throws Exception {
        //先看是否已存在目标型体

        //查出原型体的主表、明细表、内容表信息，并复制
        List<PccMeProjectPlanHd> oldHds = hdMapper.query(vo.getSourceModel(), vo.getSourceDept(), null, null,null,null, false);
        PccMeProjectPlanHd oldHd = oldHds.stream().filter(item->item.getFactory().equals(vo.getFactory())).findFirst().orElse(null);
        if (CollectionUtils.isEmpty(oldHds)) {
            throw new Exception("已存在目标型体，请重新检查！");
        }
      /*  PccMeProjectPlanHd oldHd = oldHds.get(0);*/
        PccMeProjectPlanHdVo pccMeProjectPlanHdVo = new PccMeProjectPlanHdVo();
        BeanUtil.copyProperties(oldHd, pccMeProjectPlanHdVo);
        pccMeProjectPlanHdVo.setModel_no(vo.getModel());
        pccMeProjectPlanHdVo.setId(null);
        pccMeProjectPlanHdVo.setBrand(vo.getBrand());
        pccMeProjectPlanHdVo.setCreate_date(new Date());
        pccMeProjectPlanHdVo.setFactory(vo.getUserFactory());
        pccMeProjectPlanHdVo.setShoe_last(vo.getShoeLost());
        PccMeProjectPlanDtVo pccMeProjectPlanDtVo = new PccMeProjectPlanDtVo();
        pccMeProjectPlanHdVo.setPccMeProjectPlanDts(pccMeProjectPlanDtVo);
        pccMeProjectPlanHdVo.setCreate_by(vo.getCreateBy());

        //保存主表
        this.add(pccMeProjectPlanHdVo);

        //复制子表
        hdMapper.copyDt(pccMeProjectPlanHdVo.getPccMeProjectPlanDts().getParent_id(), oldHd.getId());

        //把刚新增的子表查出来，获取id
        int count = 0;
        List<PccMeProjectPlanDtVo> pccMeProjectPlanDtVos = hdMapper.queryDt(pccMeProjectPlanHdVo.getPccMeProjectPlanDts().getParent_id());
        for (PccMeProjectPlanDtVo meProjectPlanDtVo : pccMeProjectPlanDtVos) {
            Integer id = meProjectPlanDtVo.getId();
            Integer itemNo = meProjectPlanDtVo.getItem_no();
            Integer version = meProjectPlanDtVo.getVersion();

            //通过item_no与version找出原数据
            int j = hdMapper.insertCont(vo.getSourceModel(), itemNo, version, vo.getSourceDept(),id,vo.getFactory());
            int k = hdMapper.insertImg(vo.getSourceModel(), itemNo, version, vo.getSourceDept(), id,vo.getFactory());
            count += j;
            count += k;
        }
        return count;
    }

    @DS("app")
    @Override
    public List<String> findAllPlanFactory() {
        return hdMapper.queryAllPlanFactory();
    }

    @DS("app")
    @Override
    public Integer audit(PccMeProjectPlanHdVo vo) {
        return hdMapper.audit(vo.getId());
    }

    @DS("app")
    @Override
    public Integer revAudit(PccMeProjectPlanHdVo vo) {
        return hdMapper.revAudit(vo.getId());
    }


}
