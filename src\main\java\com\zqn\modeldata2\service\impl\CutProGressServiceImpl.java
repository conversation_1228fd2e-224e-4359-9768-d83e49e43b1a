package com.zqn.modeldata2.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.entity.CutProGress;
import com.zqn.modeldata2.entity.Qdpt;
import com.zqn.modeldata2.mapper.CutProGressMapper;
import com.zqn.modeldata2.service.CutProGressService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class CutProGressServiceImpl implements CutProGressService {

    @Resource
    private CutProGressMapper cutProGressMapper;

    @Override
    public R<PageInfo<CutProGress>> query(int pageNo, int pageSize, Date startTime, Date endTime,
                                          Date cinStartTime, Date cinEndTime,
                                          String brand, String devType, Integer externalStatus) {
        CutProGress totalQty = cutProGressMapper.queryTotalQty(cinStartTime, cinEndTime, brand, devType);
        PageHelper.startPage(pageNo, pageSize);
        List<CutProGress> list = cutProGressMapper.query(startTime, endTime, cinStartTime, cinEndTime, brand, devType, externalStatus);
        if (list.size() > 0) {
            CutProGress cutProGress = list.get(0);
            cutProGress.setTotal_order_quantity1(totalQty.getTotal_order_quantity1());
            cutProGress.setTotal_order_quantity2(totalQty.getTotal_order_quantity2());
            cutProGress.setTotal_order_quantity3(totalQty.getTotal_order_quantity3());
            cutProGress.setTotal_order_quantity4(totalQty.getTotal_order_quantity4());
            cutProGress.setTotal_order_quantity5(totalQty.getTotal_order_quantity5());
            cutProGress.setTotal_order_quantity6(totalQty.getTotal_order_quantity6());
            cutProGress.setTotal_order_quantity7(totalQty.getTotal_order_quantity7());
        }
        PageInfo<CutProGress> pageInfo = new PageInfo<>(list);
        return R.success(pageInfo);
    }

    @Override
    public List<JSONObject> queryAllDevType() {
        List<JSONObject> result = new ArrayList<JSONObject>();
        List<String> devTypes = cutProGressMapper.queryAllDevType();
        if (devTypes.size() > 0) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("value", "");
            jsonObject.put("text", "請選擇");
            result.add(jsonObject);
        }
        for (String devType : devTypes) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("value", devType);
            jsonObject.put("text", devType);
            result.add(jsonObject);
        }
        return result;
    }

    @Override
    public Integer update(CutProGress cutProGress) throws Exception {
        if (StrUtil.isEmpty(cutProGress.getOrd_no()) || StrUtil.isEmpty(cutProGress.getItem_no())) {
            throw new Exception("未选中需更新数据！");
        }
        return cutProGressMapper.update(cutProGress.getOrd_no(), cutProGress.getItem_no());
    }
}
