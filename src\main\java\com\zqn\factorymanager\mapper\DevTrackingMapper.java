package com.zqn.factorymanager.mapper;

import com.zqn.factorymanager.entity.DevTracking;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface DevTrackingMapper {

    List<DevTracking> queryTop(@Param("startTime") Date startTime,
                               @Param("endTime") Date endTime);

    List<DevTracking> queryBottom(@Param("startTime") Date startTime,
                                  @Param("endTime") Date endTime);

    List<DevTracking> queryWarnTop(@Param("startTime") Date startTime,
                                   @Param("endTime") Date endTime);

    List<DevTracking> queryWarnBottom(@Param("startTime") Date startTime,
                                      @Param("endTime") Date endTime);
                           
    int updateBrandFn(DevTracking devTracking);
}