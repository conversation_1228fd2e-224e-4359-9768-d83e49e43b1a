package com.zqn.sop.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:ME工程编制主表
 * @date 2024/5/8 8:36
 */
@Data
public class PccMeProjectPlanHd {

    private Integer id;

    //型体
    private String model_no;

    //楦头编号
    private String shoe_last;

    //客户
    private String brand;

    //日期
    @JsonFormat(pattern = "YYYY/MM/dd")
    private Date link_date;

    private String remark;

    private String create_by;

    @JsonFormat(pattern = "YYYY-MM-dd HH:mm:ss")
    private Date create_date;

    private String update_by;

    @JsonFormat(pattern = "YYYY-MM-dd HH:mm:ss")
    private Date update_date;

    private String dept;

    private String dept_name;

    private Integer item_num;

    private Integer min_item;

    //制鞋排头
    private String shoe_make_head;

    //版师
    private String printmaker;

    //高级技师
    private String senior_technician;


    private String factory;

    private Integer audit_flag;

}
