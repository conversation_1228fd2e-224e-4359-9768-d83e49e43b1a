package com.zqn.sop.controller;

import com.github.pagehelper.PageInfo;
import com.zqn.modeldata2.common.R;
import com.zqn.sop.entity.PccProgressRoute;
import com.zqn.sop.service.PccProgressRouteService;
import com.zqn.sop.vo.PccProgressRouteVo;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;


@RestController
@RequestMapping("/pccprogressroute")
//@Validated
public class PccProgressRouteController {

    @Resource
    private PccProgressRouteService pccProgressRouteService;


    @GetMapping("/query")
    public R<PageInfo<PccProgressRoute>> query(@RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                                 @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
                                                 @RequestParam(value = "dept", required = false) String dept,
                                                 @RequestParam(value = "factory", required = false) String factory) {

        PageInfo<PccProgressRoute> result = pccProgressRouteService.query(pageNo, pageSize, dept, factory);
        return R.success(result);
    }




    @PostMapping("/create")
    public R<Integer> create(@Valid @RequestBody PccProgressRouteVo vo) throws Exception {
        Date date = new Date();
        vo.setCreate_date(date);
        vo.setUpdate_date(date);
        int result = pccProgressRouteService.create(vo);
        return R.success(result);
    }

    @PostMapping("update")
    public R<Integer> update(@RequestBody PccProgressRouteVo vo) throws Exception {
        Date date = new Date();
        vo.setUpdate_date(date);
        int result = pccProgressRouteService.update(vo);
        return R.success(result);
    }

    @GetMapping("getById")
    public R<PccProgressRoute> getById(Integer id) throws Exception {
        PccProgressRoute progressRoute = pccProgressRouteService.getById(id);
        return R.success(progressRoute);
    }

    @PostMapping("getByFactoryAndDept")
    public R<PccProgressRoute> getByFactoryAndDept(@RequestBody PccProgressRouteVo vo) throws Exception {
        PccProgressRoute progressRoute = pccProgressRouteService.getByFactoryAndDept(vo);
        return R.success(progressRoute);
    }

    @DeleteMapping("delete")
    public R<Integer> delete(@RequestBody String ids) throws Exception {
        List<Integer> idList = Arrays.stream(ids.split(","))
                .map(Integer::parseInt)
                .collect(Collectors.toList());
        Integer result = 0;
        for (Integer id : idList) {
            result = pccProgressRouteService.delete(id);
        }
        return R.success(result);
    }

}
