<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zqn.modeldata2.mapper.LoginMapper">
    <!--    <resultMap id="ResultMap" type="java.util.HashMap">-->
    <!--        <result property="" column="" jdbcType=""/>-->
    <!--    </resultMap>-->

    <!-- 登录 -->
    <select id="login" resultType="com.zqn.modeldata2.entity.Login">
        select a.user_no,
               a.user_desc,
               a.user_id,
               a.factory,
               a.all_brand,
               a.user_mats,
               a.user_stores,
               a.user_menus,
               a.inv_flag,
               b.app_tag
        from sy_user a,
             bc_depart b
        where a.dept_no = b.dept_no
          and a.user_id = #{userId}
          and trim(a.password) = trim(encrypt(#{password}))
    </select>

    <!-- 更新登录时间 -->
    <update id="updateLoginTime">
        update sy_user
        set pass_date = sysdate + 90,
            logon_date = sysdate
        where user_no = #{userNo}
    </update>
</mapper>