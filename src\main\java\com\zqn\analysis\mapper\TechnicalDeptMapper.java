package com.zqn.analysis.mapper;

import com.zqn.analysis.entity.TechnicalDept;
import com.zqn.analysis.entity.TechnicalDeptFactoryQty;
import com.zqn.analysis.entity.TechnicalDeptModelList;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface TechnicalDeptMapper {

    TechnicalDept query();

    TechnicalDept queryModelNoQty();

    TechnicalDept queryBrandQty();

    List<TechnicalDeptFactoryQty> queryQtyByFactory();

    List<TechnicalDeptModelList> queryModelList();

    List<Map<String, Object>> queryPeopleDetail(String factory);
}