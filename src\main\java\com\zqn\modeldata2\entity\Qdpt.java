package com.zqn.modeldata2.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 前段配套生产，翻译不出来该叫什么
 * @date 2024/6/11 15:06
 */
@Data
public class Qdpt {

    private String ord_seq;

    //出貨日
    @JsonFormat(pattern = "YY/MM/dd")
    private Date shp_date;

    //品牌
    private String brand_no;

    //样品类型
    private String dev_type;

    //樣品單號
    private String ord_no;

    //項次
    private String item_no;

    //鞋圖
    private byte[] model_pic;

    //業務
    private String dutyer;

    //型體
    private String model_no;

    //楦頭編號
    private String last_no;

    //派工日
    @JsonFormat(pattern = "YY/MM/dd")
    private Date wo_date;

    //訂單量
    private String tot_qty;

    //面板
    private String upp_flag;

    //底板
    private String sole_flag;

    //面料狀況
    private String t1_flag;

    //底料狀況
    private String t2_flag;

    //副料配套
    private String t3_flag;

    //裁断发外
    private String c_f_status;

    //鞋面發外
    private String u_f_status;

    //排入生产
    private String cin_flag;

    //排入時間
    @JsonFormat(pattern = "YY/MM/dd")
    private Date cin_date;

    //排入區域
    private String cin_area;

    private BigDecimal total_order_quantity;

    //出貨日
    @JsonFormat(pattern = "YYYY-MM-dd HH:mm:ss")
    private Date created_date;
}
