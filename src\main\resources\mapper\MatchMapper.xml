<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zqn.modeldata2.mapper.MatchMapper">

    <resultMap id="ResultMap" type="java.util.HashMap">
        <!--        <result property="" column="" jdbcType=""/>-->
        <result property="typesOf" column="TYPESOF" jdbcType="VARCHAR"/>
        <result property="lr_mark" column="LR_MARK" jdbcType="VARCHAR"/>
        <result property="width" column="WIDTH" jdbcType="VARCHAR"/>
        <result property="t_name" column="T_NAME" jdbcType="VARCHAR"/>
        <result property="cm" column="CM" jdbcType="VARCHAR"/>
        <result property="yp" column="YP" jdbcType="VARCHAR"/>

    </resultMap>

    <!-- 已配套待配送 -->
    <select id="getWaitSend" resultType="com.zqn.modeldata2.entity.MkMating">
        select *
        from (select rownum rn, t.*
              from (select mating_no,
                           to_char(shp_date, 'yyyy-mm-dd')   shp_date,
                           model_no,
                           ord_no,
                           dev_type,
                           last_no,
                           tot_qty,
                           ushelf_no,
                           bshelf_no6,
                           bshelf_no3,
                           bshelf_no2,
                           bshelf_no1,
                           mat_date,
                           pat_date,
                           pat_qty
                            ,
                           nvl(ypat_qty, 0)                  ypat_qty,
                           b1_date,
                           b2_date,
                           b3_date,
                           b6_date,
                           u_date,
                           case
                               when b1_flag >= tot_qty then 'Y'
                               else 'N'
                               end                           b1_flag,
                           case
                               when b2_flag >= tot_qty then 'Y'
                               else 'N'
                               end                           b2_flag,
                           case
                               when b3_flag >= tot_qty then 'Y'
                               else 'N'
                               end                           b3_flag,
                           case
                               when b6_flag >= tot_qty then 'Y'
                               else 'N'
                               end                           b6_flag,
                           case
                               when u_flag >= tot_qty then 'Y'
                               else 'N'
                               end                           u_flag,
                           model_pic,
                           decode(las_c_flag, 'Y', 'OK', '') las_c_flag
                    from vw_mk_mating1
                    order by shp_date) t)
        where rn &gt; (#{page_no} - 1) * #{page_size}
          and rn &lt;= #{page_no} * #{page_size}
    </select>

    <!-- 待配送数量 -->
    <select id="getSendCount" resultType="integer">
        select count(*)
        from vw_mk_mating1 a
    </select>

    <!-- 修改配送状态-->
    <update id="updateState">
        update mk_mating
        set pat_flag = 'Y',
            PD_LINE  = #{pdLine}
        where mating_no = #{mating_no}
    </update>

    <!-- 修改配送状态-->
    <update id="updateState2">
        update gc_sorder
        set PD_LINE = #{pdLine}
        where ord_no = #{ord_no}
    </update>

    <!-- 已配套待投入 -->
    <select id="getWaitInput" resultType="com.zqn.modeldata2.entity.MkMating">
        select *
        from (
        select rownum rn, t.*
        from (
        select to_char(shp_date,'yyyy-mm-dd') shp_date,
        A.model_no, ord_no, dev_type, last_no, tot_qty,
        status,dp1_qty,p1_qty,
        mat_date, pat_date, pat_qty,PD_LINE,
        B.model_pic
        from vw_mk_mating2 A,(select MODEL_NO,MODEL_PIC from ck_smodel) B
        where A.MODEL_NO=B.MODEL_NO
        <if test="status == 1">
            and pd_line='A線DRESS'
        </if>
        <if test="status == 2">
            and pd_line='B線SPORTS'
        </if>
        order by shp_date
        ) t
        )
        where rn &gt; (#{page_no} - 1) * #{page_size}
        and rn &lt;= #{page_no} * #{page_size}
    </select>

    <!-- 待投入数量 -->
    <select id="getInputCount" resultType="integer">
        select count(*)
        from vw_mk_mating2
        <where>
            <if test="status == 1">
                and pd_line='A線DRESS'
            </if>
            <if test="status == 2">
                and pd_line='B線SPORTS'
            </if>
        </where>
    </select>

    <!-- 未配套库存查询 -->
    <select id="getNoMatch" resultType="com.zqn.modeldata2.entity.MkNpatQcTot">
        select row_number() over(order by npat_cou desc) pm, brand_no,
        npat_cou, tot_qty, u_qty, b_qty1, b_qty2, b_qty3, b_qty6
        from
        <if test="status == 0">
            vw_mk_npat_qc_tot
        </if>
        <if test="status == 1">
            vw_mk_npat_qc_tot1
        </if>
        order by u_qty desc
    </select>

    <!-- 未配套库存明细 -->
    <select id="getNoMatchDetail" resultType="com.zqn.modeldata2.entity.MkNpatQcDet">
        select model_pic, shp_date, model_no, ord_no, dev_type, last_no,
        tot_qty, u_qty, b_qty1, b_qty2, b_qty3, b_qty6
        from
        <if test="status == 0">
            vw_mk_npat_qc_det
        </if>
        <if test="status == 1">
            vw_mk_npat_qc_det1
        </if>
        where brand_no = #{brand_no} order by dev_type,shp_date desc
    </select>

    <!-- 已配套楦头确认 -->
    <select id="getMatchConfirm" resultType="com.zqn.modeldata2.entity.MkNpatQcDet">
        SELECT shp_date,model_No,ord_No,dev_type,last_no,model_pic,tot_qty,pat_qty,ypat_qty,las_qty,sz_data,
        decode(las_c_flag, 'Y', 'OK', '') las_c_flag,mating_no,pd_line,siz_type
        FROM vw_mk_mating3 where las_c_flag='N'
        <if test="status == 1">
            and pd_line='A線DRESS'
        </if>
        <if test="status == 2">
            and pd_line='B線SPORTS'
        </if>
        OFFSET (#{page_no}-1) * #{page_size} ROWS
        FETCH NEXT #{page_size} ROWS ONLY
    </select>

    <!-- 已配套楦头确认  ——明细 -->
    <select id="getMatchConfirmDetail" resultMap="ResultMap">
        SELECT typesOf,
               width,
               lr_mark,
               LISTAGG(t_name, ',') within group (order by t_name) t_name,
               LISTAGG(cm, ',') within group (order by t_name)     cm,
               LISTAGG(yp, ',') within group (order by t_name)     yp
        from (select '样品数量' typesOf, width, lr_mark, t_name, yp
              FROM gc_sorders
                  UNPIVOT (yp FOR t_name IN (size01 AS '01',size02 AS '02',size03 AS '03',size04 AS '04',size05 AS '05',size06 AS '06',
                      size07 AS '07',size08 AS '08',size09 AS '09',size10 AS '10',size11 AS '11',size12 AS '12',size13 AS '13',size14 AS '14',
                      size15 AS '15',size16 AS '16',size17 AS '17',size18 AS '18',size19 AS '19',size20 AS '20',size21 AS '21',size22 AS '22',
                      size23 AS '23',size24 AS '24',size25 AS '25',size26 AS '26',size27 AS '27',size28 AS '28',size29 AS '29',size30 AS '30',
                      size31 AS '31',size32 AS '32',size33 AS '33',size34 AS '34',size35 AS '35',size36 AS '36',size37 AS '37',size38 AS '38',
                      size39 AS '39',size40 AS '40',size41 AS '41',size42 AS '42',size43 AS '43',size44 AS '44',size45 AS '45'))
              WHERE ord_No = #{ord_No}
                AND (yp > 0 or t_name IN (SELECT t_name
                                          FROM vw_mk_lastsg
                                              UNPIVOT (sl FOR t_name IN (size01 AS '01',size02 AS '02',size03 AS '03',size04 AS '04',size05 AS '05',size06 AS '06',
                                                  size07 AS '07',size08 AS '08',size09 AS '09',size10 AS '10',size11 AS '11',size12 AS '12',size13 AS '13',size14 AS '14',
                                                  size15 AS '15',size16 AS '16',size17 AS '17',size18 AS '18',size19 AS '19',size20 AS '20',size21 AS '21',size22 AS '22',
                                                  size23 AS '23',size24 AS '24',size25 AS '25',size26 AS '26',size27 AS '27',size28 AS '28',size29 AS '29',size30 AS '30',
                                                  size31 AS '31',size32 AS '32',size33 AS '33',size34 AS '34',size35 AS '35',size36 AS '36',size37 AS '37',size38 AS '38',
                                                  size39 AS '39',size40 AS '40',size41 AS '41',size42 AS '42',size43 AS '43',size44 AS '44',size45 AS '45'))
                                          WHERE last_no = #{last_no}
                                            AND lr_mark IN (select lr_mark FROM gc_sorders where ord_No = #{ord_No})
                                            AND sl > 0))
              UNION all
              SELECT '申购数量' typesOf, width, lr_mark, t_name, sl
              FROM vw_mk_lastsg
                  UNPIVOT (sl FOR t_name IN (size01 AS '01',size02 AS '02',size03 AS '03',size04 AS '04',size05 AS '05',size06 AS '06',
                      size07 AS '07',size08 AS '08',size09 AS '09',size10 AS '10',size11 AS '11',size12 AS '12',size13 AS '13',size14 AS '14',
                      size15 AS '15',size16 AS '16',size17 AS '17',size18 AS '18',size19 AS '19',size20 AS '20',size21 AS '21',size22 AS '22',
                      size23 AS '23',size24 AS '24',size25 AS '25',size26 AS '26',size27 AS '27',size28 AS '28',size29 AS '29',size30 AS '30',
                      size31 AS '31',size32 AS '32',size33 AS '33',size34 AS '34',size35 AS '35',size36 AS '36',size37 AS '37',size38 AS '38',
                      size39 AS '39',size40 AS '40',size41 AS '41',size42 AS '42',size43 AS '43',size44 AS '44',size45 AS '45'))
              WHERE last_no = #{last_no}
                AND lr_mark IN (select lr_mark FROM gc_sorders where ord_No = #{ord_No})
                AND (sl > 0 OR t_name IN (select t_name
                                          FROM gc_sorders
                                              UNPIVOT (yp FOR t_name IN (size01 AS '01',size02 AS '02',size03 AS '03',size04 AS '04',size05 AS '05',size06 AS '06',
                                                  size07 AS '07',size08 AS '08',size09 AS '09',size10 AS '10',size11 AS '11',size12 AS '12',size13 AS '13',size14 AS '14',
                                                  size15 AS '15',size16 AS '16',size17 AS '17',size18 AS '18',size19 AS '19',size20 AS '20',size21 AS '21',size22 AS '22',
                                                  size23 AS '23',size24 AS '24',size25 AS '25',size26 AS '26',size27 AS '27',size28 AS '28',size29 AS '29',size30 AS '30',
                                                  size31 AS '31',size32 AS '32',size33 AS '33',size34 AS '34',size35 AS '35',size36 AS '36',size37 AS '37',size38 AS '38',
                                                  size39 AS '39',size40 AS '40',size41 AS '41',size42 AS '42',size43 AS '43',size44 AS '44',size45 AS '45'))
                                          WHERE ord_No = #{ord_No}
                                            AND yp > 0)))
        GROUP BY typesOf, lr_mark, width
    </select>

    <!-- 已配套楦头确认 操作 -->
    <select id="getMatchConfirmMXXX" resultMap="ResultMap">
        SELECT typesOf,
               width,
               lr_mark,
               LISTAGG(t_name, ',') within group (order by t_name) t_name,
               LISTAGG(cm, ',') within group (order by t_name)     cm,
               LISTAGG(yp, ',') within group (order by t_name)     yp
        from (SELECT A.*, B.CM
              FROM (select '样品数量' typesOf, width, lr_mark, t_name, regexp_replace(yp, '^\.', '0.') yp
                    FROM gc_sorders
                        UNPIVOT (yp FOR t_name IN (size01 AS '01',size02 AS '02',size03 AS '03',size04 AS '04',size05 AS '05',size06 AS '06',
                            size07 AS '07',size08 AS '08',size09 AS '09',size10 AS '10',size11 AS '11',size12 AS '12',size13 AS '13',size14 AS '14',
                            size15 AS '15',size16 AS '16',size17 AS '17',size18 AS '18',size19 AS '19',size20 AS '20',size21 AS '21',size22 AS '22',
                            size23 AS '23',size24 AS '24',size25 AS '25',size26 AS '26',size27 AS '27',size28 AS '28',size29 AS '29',size30 AS '30',
                            size31 AS '31',size32 AS '32',size33 AS '33',size34 AS '34',size35 AS '35',size36 AS '36',size37 AS '37',size38 AS '38',
                            size39 AS '39',size40 AS '40',size41 AS '41',size42 AS '42',size43 AS '43',size44 AS '44',size45 AS '45'))
                    WHERE ord_No = #{ord_No}
                      AND yp > 0
                    UNION all
                    SELECT '申购数量' typesOf, width, lr_mark, t_name, regexp_replace(sl, '^\.', '0.') yp
                    FROM vw_mk_lastsg
                        UNPIVOT (sl FOR t_name IN (size01 AS '01',size02 AS '02',size03 AS '03',size04 AS '04',size05 AS '05',size06 AS '06',
                            size07 AS '07',size08 AS '08',size09 AS '09',size10 AS '10',size11 AS '11',size12 AS '12',size13 AS '13',size14 AS '14',
                            size15 AS '15',size16 AS '16',size17 AS '17',size18 AS '18',size19 AS '19',size20 AS '20',size21 AS '21',size22 AS '22',
                            size23 AS '23',size24 AS '24',size25 AS '25',size26 AS '26',size27 AS '27',size28 AS '28',size29 AS '29',size30 AS '30',
                            size31 AS '31',size32 AS '32',size33 AS '33',size34 AS '34',size35 AS '35',size36 AS '36',size37 AS '37',size38 AS '38',
                            size39 AS '39',size40 AS '40',size41 AS '41',size42 AS '42',size43 AS '43',size44 AS '44',size45 AS '45'))
                    WHERE last_no = #{last_no}
                      AND lr_mark IN (select lr_mark FROM gc_sorders where ord_No = #{ord_No})
                      AND sl > 0) A
                       LEFT JOIN (SELECT t_name, cm
                                  FROM ba_sizetype
                                      UNPIVOT (cm FOR t_name IN (size01 AS '01',size02 AS '02',size03 AS '03',size04 AS '04',size05 AS '05',size06 AS '06',
                                          size07 AS '07',size08 AS '08',size09 AS '09',size10 AS '10',size11 AS '11',size12 AS '12',size13 AS '13',size14 AS '14',
                                          size15 AS '15',size16 AS '16',size17 AS '17',size18 AS '18',size19 AS '19',size20 AS '20',size21 AS '21',size22 AS '22',
                                          size23 AS '23',size24 AS '24',size25 AS '25',size26 AS '26',size27 AS '27',size28 AS '28',size29 AS '29',size30 AS '30',
                                          size31 AS '31',size32 AS '32',size33 AS '33',size34 AS '34',size35 AS '35',size36 AS '36',size37 AS '37',size38 AS '38',
                                          size39 AS '39',size40 AS '40',size41 AS '41',size42 AS '42',size43 AS '43',size44 AS '44',size45 AS '45'))
                                  WHERE siz_type = 'US') B ON A.T_NAME = B.T_NAME)
        group by typesOf, width, lr_mark
        order by T_name, cm
    </select>

    <!-- 已配套楦头确认  ——尺码 -->
    <select id="getMatchConfirmDetailCM" resultType="integer">
        SELECT shp_date,model_No,ord_No,dev_type,last_no,model_pic,tot_qty,pat_qty,ypat_qty,las_qty,sz_data,
        decode(las_c_flag, 'Y', 'OK', '') las_c_flag,mating_no,pd_line,siz_type
        FROM vw_mk_mating3 where las_c_flag='N'
        <if test="status == 1">
            and pd_line='A線DRESS'
        </if>
        <if test="status == 2">
            and pd_line='B線SPORTS'
        </if>
        OFFSET (#{page_no}-1) * #{page_size} ROWS
        FETCH NEXT #{page_size} ROWS ONLY
    </select>

    <!-- 修改楦头状态-->
    <update id="updateXuanTouState">
        update mk_mating
        set las_c_flag ='Y'
        where mating_no = #{mating_no}
    </update>


    <!-- 成型已投入未產出查詢 -->
    <select id="formingQuery" resultType="com.zqn.modeldata2.entity.MkNpatQcDet">
        select b.MODEL_PIC,a.* from (
        SELECT SHP_DATE,MODEL_NO,ORD_NO,DEV_TYPE,LAST_NOS LAST_NO,TOT_QTY,p1_qty,p2_qty,bar_date,PB_DESC,pd_line,
        sum(decode(
        to_char(CAL_date,'yyyy/mm/dd'),
        to_char(sysdate,'yyyy/mm/dd'),
        decode(to_char(bar_date,'yyyy/mm/dd'),to_char(sysdate,'yyyy/mm/dd'),to_char(sysdate,'HH24')-to_char(bar_date,'HH24'),
        decode(to_char(sysdate,'HH24'),8,0,9,1,10,2,11,3,12,4,13,4,14,5,15,6,16,7,17,8,18,9,19,10,20,11)),
        to_char(bar_date,'yyyy/mm/dd'),
        decode(to_char(bar_date,'HH24'),8,11,9,10,10,9,11,8,12,7,13,7,14,6,15,5,16,4,17,3,18,2,19,1,20,0),
        op_hour
        )) BAR_HOUR
        FROM VW_MK_MATING4 a,(SELECT op_hour,cal_date
        FROM mk_workcal
        WHERE CAL_TYPE ='S' AND dept_no ='VR0102') b
        where (to_char(CAL_date,'yyyy/mm/dd')&gt;=to_char(bar_date,'yyyy/mm/dd') and CAL_date&lt;=sysdate)
        group by SHP_DATE,MODEL_NO,ORD_NO,DEV_TYPE,LAST_NOS,TOT_QTY,p1_qty,p2_qty,bar_date,PB_DESC,pd_line order by
        a.bar_date,a.tot_qty,a.pb_desc,a.pd_line) a,
        (select MODEL_NO,MODEL_PIC from ck_smodel) b where a.MODEL_NO=b.MODEL_NO
        <if test="status2 == 4">
            and pd_line='A線DRESS'
        </if>
        <if test="status2 == 5">
            and pd_line='B線SPORTS'
        </if>
        <if test="status == 2">
            and BAR_HOUR &gt;5
        </if>
        <if test="status == 3">
            and BAR_HOUR &lt;=5
        </if>
        order by BAR_HOUR desc OFFSET (#{page_no}-1) * #{page_size} ROWS
        FETCH NEXT #{page_size} ROWS ONLY
    </select>

    <!-- 待投入数量 -->
    <select id="getformingCount" resultType="integer">
        select count(*) from (
        SELECT pd_line,
        sum(decode(
        to_char(CAL_date,'yyyy/mm/dd'),
        to_char(sysdate,'yyyy/mm/dd'),
        decode(to_char(bar_date,'yyyy/mm/dd'),to_char(sysdate,'yyyy/mm/dd'),to_char(sysdate,'HH24')-to_char(bar_date,'HH24'),
        decode(to_char(sysdate,'HH24'),8,0,9,1,10,2,11,3,12,4,13,4,14,5,15,6,16,7,17,8,18,9,19,10,20,11)),
        to_char(bar_date,'yyyy/mm/dd'),
        decode(to_char(bar_date,'HH24'),8,11,9,10,10,9,11,8,12,7,13,7,14,6,15,5,16,4,17,3,18,2,19,1,20,0),
        op_hour
        )) BAR_HOUR
        FROM VW_MK_MATING4 a,(SELECT op_hour,cal_date
        FROM mk_workcal
        WHERE CAL_TYPE ='S' AND dept_no ='VR0102') b
        where (to_char(CAL_date,'yyyy/mm/dd')&gt;=to_char(bar_date,'yyyy/mm/dd') and CAL_date&lt;=sysdate)
        group by SHP_DATE,MODEL_NO,ORD_NO,DEV_TYPE,LAST_NOS,TOT_QTY,bar_date,PB_DESC,pd_line order by
        a.bar_date,a.tot_qty,a.pb_desc,a.pd_line) a
        <where>
            <if test="status2 == 4">
                and pd_line='A線DRESS'
            </if>
            <if test="status2 == 5">
                and pd_line='B線SPORTS'
            </if>
            <if test="status == 2">
                and BAR_HOUR &gt;5
            </if>
            <if test="status == 3">
                and BAR_HOUR &lt;=5
            </if>
        </where>
    </select>

    <!-- 已配套数量 -->
    <select id="getMatchCount" resultType="integer">
        select count(*) from vw_mk_mating3
        <where>
            <if test="status3 == 1">
                and pd_line='A線DRESS'
            </if>
            <if test="status3 == 2">
                and pd_line='B線SPORTS'
            </if>
        </where>

    </select>


    <insert id="insertConfirmDetail">
        insert into mk_lastqty(ord_no,last_no,width,lr_mark,
        <foreach collection="sizeList" item="item" index="i" open="" separator="," close="">
            size${item}
        </foreach>
        ) values
        (#{ord_no},#{last_no},#{width},#{lr_mark},
        <foreach collection="sizeListCot" item="item" index="i" open="" separator="," close="">
            #{item}
        </foreach>
        )

    </insert>

</mapper>