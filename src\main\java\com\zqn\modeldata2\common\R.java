package com.zqn.modeldata2.common;

import lombok.Data;

import java.io.Serializable;

/**
 * 通用返回结果，服务端响应的数据最终都会封装成此对象
 */
@Data
public class R<T> implements Serializable {
    private Integer code; //编码：1成功，0和其它数字为失败

    private String msg; //错误信息

    private T data; //数据

//    private Map<String, Object> map = new HashMap<>(); //动态数据

    private R(T object, String msg) {
        this.data = object;
        this.msg = msg;
    }

    public R() {

    }

    public static <T> R<T> success(T object) {
        R<T> r = new R<>();
        r.data = object;
        r.code = 1;
        return r;
    }

    public static <T> R<T> error(String msg) {
        R<T> r = new R<>();
        r.msg = msg;
        r.code = 0;
        return r;
    }

    public static <T> R<T> success2(T object, Integer msg) {
        return new R<T>(object, msg.toString());
    }

//    public R<T> add(String key, Object value) {
//        this.map.put(key, value);
//        return this;
//    }
}
