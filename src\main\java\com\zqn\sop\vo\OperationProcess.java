package com.zqn.sop.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import cn.afterturn.easypoi.excel.annotation.ExcelEntity;
import lombok.Data;

import java.util.Date;
import java.util.List;

@ExcelTarget("operationProcess")
@Data
public class OperationProcess {
    private String brand;
    private String style;
    private Date createDate;
    private List<Item> items;
}