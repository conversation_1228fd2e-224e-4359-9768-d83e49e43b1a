package com.zqn.modeldata2.controller;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.entity.Qdpt;
import com.zqn.modeldata2.entity.QdptDt;
import com.zqn.modeldata2.service.QdptService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 前段配套生产
 * @date 2024/6/11 15:25
 */
@RestController
@RequestMapping("/qdpt")
@Validated
public class QdptController {

    @Resource
    private QdptService qdptService;

    /**
     * @description: 查询前段生产进度表
     * @param: pageNo
     * pageSize
     * startTimeTamp 开始时间
     * endTimeTamp 结束时间
     * brand 品牌
     * devType 样品类型
     * @return: com.zqn.modeldata2.common.R<com.github.pagehelper.PageInfo < com.zqn.modeldata2.entity.Qdpt>>
     * <AUTHOR> Yang
     * @date: 2024/6/11 17:00
     */
    @GetMapping("/query")
    public R<PageInfo<Qdpt>> query(@RequestParam(value = "pageNo", defaultValue = "1") int pageNo,
                                   @RequestParam(value = "pageSize", defaultValue = "10") int pageSize,
                                   @RequestParam(value = "startTime", required = false) long startTimeTamp,
                                   @RequestParam(value = "endTime", required = false) long endTimeTamp,
                                   @RequestParam(value = "brand", required = false) String brand,
                                   @RequestParam(value = "devType", required = false) String devType,
                                   @RequestParam(value = "fileType", required = false) Integer fileType,
                                   @RequestParam(value = "cutComplType", required = false) Integer cutComplType,
                                   @RequestParam(value = "sortType", required = false,defaultValue = "") String sortType
                                   ) {
        Date startTime = new Date(startTimeTamp); // 将时间戳转换为Date对象
        Date endTime = new Date(endTimeTamp); // 将时间戳转换为Date对象
        // 使用 Calendar 设置 startTime 为当天的 0 点
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(startTime);
        calendar.set(Calendar.HOUR_OF_DAY, 0); // 设置为 0 点
        calendar.set(Calendar.MINUTE, 0);      // 分钟设置为 0
        calendar.set(Calendar.SECOND, 0);      // 秒设置为 0
        calendar.set(Calendar.MILLISECOND, 0); // 毫秒设置为 0
        startTime = calendar.getTime();        // 获取调整后的时间

        // 使用 Calendar 设置 endTime 为当天的 23 点 59 分 59 秒 999 毫秒
        calendar.setTime(endTime);
        calendar.set(Calendar.HOUR_OF_DAY, 23); // 设置为 23 点
        calendar.set(Calendar.MINUTE, 59);      // 分钟设置为 59
        calendar.set(Calendar.SECOND, 59);      // 秒设置为 59
        calendar.set(Calendar.MILLISECOND, 999); // 毫秒设置为 999
        endTime = calendar.getTime();           // 获取调整后的时间
        return qdptService.query(pageNo, pageSize, startTime, endTime, brand, devType, fileType, cutComplType, sortType);
    }

    @GetMapping("/queryAllDevType")
    public R<List<JSONObject>> queryAllDevType() {
        List<JSONObject> result = qdptService.queryAllDevType();
        return R.success(result);
    }

    /**
     * @description: 投入
     * @param: qdpt 需要ord_no 樣品單號, item_no 項次
     * @return: com.zqn.modeldata2.common.R<com.github.pagehelper.PageInfo < com.zqn.modeldata2.entity.Qdpt>>
     * <AUTHOR> Yang
     * @date: 2024/6/11 17:01
     */
    @PostMapping("/update")
    public R<String> update(@RequestBody Qdpt qdpt) throws Exception {
        Integer result = qdptService.update(qdpt);
        if (result == 0) {
            return R.error("投入异常！");
        }
        qdptService.insertLog(qdpt.getOrd_no(), qdpt.getItem_no());
        return R.success("投入成功");
    }

    @PostMapping("/batchUpdate")
    public R<String> batchUpdate(@RequestBody List<Qdpt> qdpts) throws Exception {
        Integer result = qdptService.batchUpdate(qdpts);
        if (qdpts.size() != result) {
            return R.error("投入异常！");
        }
        for (Qdpt qdpt : qdpts) {
            qdptService.insertLog(qdpt.getOrd_no(), qdpt.getItem_no());
        }
        return R.success("投入成功");
    }

    @GetMapping("/queryLog")
    public R<List<Qdpt>> queryLog() {
        return R.success(qdptService.queryLog());
    }

    /**
     * 根据订单号查询详细仓库扫描明细。
     *
     * @param ordNo 订单号，作为查询详细表格数据的依据，必须提供。
     * @return 返回一个包含查询结果的列表。如果查询成功，列表中将包含Qdpt类型的对象。
     */
    @GetMapping("/queryDetailTableData")
    public R<List<QdptDt>> queryDetailTableData(@RequestParam(value = "ordNo", required = true) String ordNo) {
        // 调用qdptService的服务方法，根据订单号查询详细表格数据
        List<QdptDt> result = qdptService.queryDetailTableData(ordNo);
        // 返回查询结果，包装在R对象中，表示操作成功
        return R.success(result);
    }

    /**
     * 手动结案接口。
     * <p>
     * 因为样品单可能有多个出货期，而扫描是基于样品单号
     * 所以这个扫描明细摆放结案状态有可能有多笔资料
     *
     * @param qdpt 结案业务参数对象，包含所需的所有业务数据。
     * @return 包含结案结果的 {@link R} 对象，成功时结果在数据字段中。
     * <AUTHOR> Yang
     * @date 2024/7/6 10:50
     * @see Qdpt
     * @see R
     */
    @PostMapping("/manualClose")
    public R<String> manualClose(@RequestBody Qdpt qdpt) {
        // 调用业务服务完成手动结案操作
        String result = qdptService.manualClose(qdpt);
        // 返回操作结果，封装在R对象中表示操作成功
        return R.success(result);
    }


       /**
        * 查找投入記錄縂雙數
     * 2024/10/16
     * ybq
     * @param orderNos 投入記錄orderNos
     * @return
     */
    @PostMapping("/findTouRuLogTotQtyCount")
    public R<String> findTouRuLogTotQtyCount(@RequestBody Map<String, List<String>> data) {
        List<String> orderNos = data.get("orderNos");
        BigDecimal touRuLogTotQtyCount = qdptService.findTouRuLogTotQtyCount(orderNos);
        if(touRuLogTotQtyCount!=null){
            return R.success(touRuLogTotQtyCount.toString());
        }
        return R.success("0");
    }

}
