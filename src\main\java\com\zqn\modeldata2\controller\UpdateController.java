package com.zqn.modeldata2.controller;

import com.zqn.modeldata2.common.R;
import com.zqn.modeldata2.entity.Update;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/update")
public class UpdateController {
    @Value("${versionName}")
    private String versionName;

    @Value("${versionDesc}")
    private String versionDesc;

    @Value("${versionType}")
    private String versionType;

    @Value("${versionCode}")
    private String versionCode;

    @Value("${downloadUrl}")
    private String downloadUrl;

    @Value("${isForceUpdate}")
    private String isForceUpdate;

    @GetMapping
    public R<Update> update() {
        Update update = new Update();
        update.setVersionName(versionName);
        update.setVersionDesc(versionDesc);
        update.setVersionType(versionType);
        update.setVersionCode(versionCode);
        update.setDownloadUrl(downloadUrl);
        update.setIsForceUpdate(isForceUpdate);
        return R.success(update);
    }
}
