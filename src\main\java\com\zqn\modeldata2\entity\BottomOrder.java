package com.zqn.modeldata2.entity;

import lombok.Data;

@Data
public class BottomOrder {

    // 订单信息字段
    private String GET_DATE;
    private String ORDER_AUTO;
    private int ORD_QTY;
    private String OUT_FLAG;
    private int IN_TOT_QTY;
    private int OUT_TOT_QTY;
    private String ADDR_FN;
    private String PARENTSMATNO;
    private String MODEL_NAME;
    private String MODEL_NO;
    private String CHN_COLOR;
    private String SORDER_NO;
    private String COMMITMENT;
    private String OUT_CDATE;
    private String PL_STATUS;
    private String FL_STATUS;

    // 生产过程字段
    private String T_5D_FLAG;
    private int T_5D_CPT_QTY;
    private String T_5P_FLAG;
    private int T_5P_CPT_QTY;
    private String T_5T_FLAG;
    private int T_5T_CPT_QTY;
    private String T_5Q_FLAG;
    private int T_5Q_CPT_QTY;
    private String T_5R_FLAG;
    private int T_5R_CPT_QTY;
    private String T_5U_FLAG;
    private int T_5U_CPT_QTY;
    private String T_5S_FLAG;
    private int T_5S_CPT_QTY;
    private String T_5Y_FLAG;
    private int T_5Y_CPT_QTY;


    private byte[]  MODEL_PIC;

    private String pic_base64;

    private String SOLE;

    private String BRAND_NO;

}
