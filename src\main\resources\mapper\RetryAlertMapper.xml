<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zqn.factorymanager.mapper.RetryAlertMapper">

    <select id="queryTop" resultType="com.zqn.factorymanager.entity.RetryAlert">
        SELECT x.brand_no,x.model_no,x.ord_no,x.model_pic,x.dev_type,x.upp_desc,x.upper_der,x.sole_der,x.dutyer,y.b_date1,y.b_date2,z.shp_date
        FROM (
        SELECT a.brand_no,a.model_no,a.ord_no,b.model_pic,a.dev_type,a.UPP_DESC,b.UPPER_DER,b.sole_Der,a.dutyer,
        ROW_NUMBER() OVER (PARTITION BY a.ord_nos ORDER BY a.ord_no  ) as row_num
        FROM gc_sorder a,vck_smodel  b 
        WHERE SUBSTR(ord_type,1,1)='C' AND a.model_no = b.model_no 
        AND a.dev_type IN ('FITTING樣品')
        ) x , (SELECT ord_no,MIN(bar_date) b_date1,MAX(bar_date) b_date2 FROM mk_sorderbar WHERE semi_no='V' AND TYPE='O' 
              AND  bar_date between #{startTime} and #{endTime}
              GROUP BY ord_no  ) y ,(SELECT ord_no ,MIN(shp_date) shp_date  FROM gc_sorders GROUP BY ord_no) z 
        WHERE ROW_NUM >=4 AND x.ord_no= y.ord_no AND x.ord_no= z.ord_no
    </select>

    <select id="queryBottom" resultType="com.zqn.factorymanager.entity.RetryAlert">
        SELECT x.brand_no,x.model_no,x.ord_no,x.model_pic,x.dev_type,x.upp_desc,x.upper_der,x.sole_der,x.dutyer,y.b_date1,y.b_date2,z.shp_date
        FROM (
        SELECT a.brand_no,a.model_no,a.ord_no,b.model_pic,a.dev_type,a.UPP_DESC,b.UPPER_DER,b.sole_Der,a.dutyer,
        ROW_NUMBER() OVER (PARTITION BY a.ord_nos ORDER BY a.ord_no  ) as row_num
        FROM gc_sorder a,vck_smodel  b 
        WHERE SUBSTR(ord_type,1,1)='C' AND a.model_no = b.model_no 
        AND a.dev_type IN ('FITTING樣品')
        ) x , (SELECT ord_no,MIN(bar_date) b_date1,MAX(bar_date) b_date2 FROM mk_sorderbar WHERE semi_no='W' AND TYPE='O' 
              AND  bar_date between #{startTime} and #{endTime}
              GROUP BY ord_no  ) y,(SELECT ord_no ,MIN(shp_date) shp_date  FROM gc_sorders GROUP BY ord_no) z 
        WHERE ROW_NUM >=4 AND x.ord_no= y.ord_no AND x.ord_no= z.ord_no
    </select>
</mapper>