package com.zqn.analysis.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.zqn.analysis.entity.TechnicalDept;
import com.zqn.analysis.entity.TechnicalDeptFactoryQty;
import com.zqn.analysis.entity.TechnicalDeptModelList;
import com.zqn.analysis.mapper.TechnicalDeptBottomMapper;
import com.zqn.analysis.service.TechnicalDeptBottomService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2024/11/29 10:31
 */
@Service
public class TechnicalDeptBottomServiceImpl implements TechnicalDeptBottomService {

    @Resource
    private TechnicalDeptBottomMapper technicalDeptBottomMapper;

    @Override
    @DS("app")
    public TechnicalDept queryByWEB() {
        TechnicalDept vo = new TechnicalDept();

        //查询总人数
        TechnicalDept totalNum = technicalDeptBottomMapper.query();
        //查询各厂面底技术人员分布
        List<TechnicalDeptFactoryQty> queryQtyByFactorys = technicalDeptBottomMapper.queryQtyByFactory();

        //赋值
        vo.setTechnicalTotalNum(totalNum.getTechnicalTotalNum());
        vo.setSopNum(0);

        //赋值图表数据
        List<String> factorys = new ArrayList<>();
        List<Integer> face = new ArrayList<>();
        List<Integer> bottom = new ArrayList<>();
        for (TechnicalDeptFactoryQty queryQtyByFactory : queryQtyByFactorys) {
            factorys.add(queryQtyByFactory.getFactory());
            face.add(queryQtyByFactory.getFaceQty());
            bottom.add(queryQtyByFactory.getBottomQty());
        }
        vo.setCategories(factorys);
        vo.setFaceData(face);
        vo.setBottomData(bottom);
        return vo;
    }

    @Override
    @DS("master")
    public TechnicalDept queryByDEUSER() {
        TechnicalDept vo = new TechnicalDept();
        //查询当日开版型体数
        TechnicalDept modelQtytechnicalDept = technicalDeptBottomMapper.queryModelNoQty();
        //查询当日开版客户数
        TechnicalDept brandQtytechnicalDept = technicalDeptBottomMapper.queryBrandQty();
        vo.setTodayModelNum(modelQtytechnicalDept.getTodayModelNum());
        vo.setTodayCustomerNum(brandQtytechnicalDept.getTodayCustomerNum());
        return vo;
    }

    @Override
    public TechnicalDept queryModelList() {
        List<TechnicalDeptModelList> technicalDeptModelLists = technicalDeptBottomMapper.queryModelList();
        TechnicalDept vo = new TechnicalDept();
        vo.setModelList(technicalDeptModelLists);
        return vo;
    }

    @Override
    @DS("app")
    public List<Map<String, Object>> queryPeopleDetail(String factory) {
        return technicalDeptBottomMapper.queryPeopleDetail(factory);
    }
}
